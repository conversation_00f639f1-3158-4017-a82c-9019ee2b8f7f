import { createClient } from '@supabase/supabase-js';
import { z } from 'zod';

// Define the contact form schema using Zod for validation
export const contactFormSchema = z.object({
  name: z.string().min(2, { message: 'Name must be at least 2 characters' }),
  email: z.string().email({ message: 'Please enter a valid email address' }),
  subject: z.string().min(3, { message: 'Subject must be at least 3 characters' }),
  message: z.string().min(10, { message: 'Message must be at least 10 characters' }),
});

// Type for the contact form data
export type ContactFormData = z.infer<typeof contactFormSchema>;

// Function to send email using Supabase
export async function sendContactEmail(formData: ContactFormData): Promise<{ success: boolean; error?: string }> {
  try {
    // Validate the form data
    contactFormSchema.parse(formData);

    // Create a new Supabase client for this request
    const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || '';
    const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || '';

    if (!supabaseUrl || !supabaseAnonKey) {
      console.error('Missing Supabase URL or anon key');
      return { success: false, error: 'Server configuration error. Please try again later.' };
    }

    console.log('Creating Supabase client with URL:', supabaseUrl);
    const supabase = createClient(supabaseUrl, supabaseAnonKey);

    // Insert the contact message into Supabase
    console.log('Inserting contact message into database:', JSON.stringify(formData));
    const { error: dbError } = await supabase
      .from('contact_messages')
      .insert([
        {
          name: formData.name,
          email: formData.email,
          subject: formData.subject,
          message: formData.message,
          created_at: new Date().toISOString(),
        },
      ]);

    if (dbError) {
      console.error('Error saving contact message to database:', dbError);
      return { success: false, error: 'Failed to save message. Please try again later.' };
    }

    console.log('Contact message saved to database successfully');

    // Call the Edge Function to send an email notification
    // Use the same supabaseUrl that was defined earlier

    if (!supabaseUrl) {
      console.error('Missing Supabase URL');
      return { success: false, error: 'Server configuration error. Please try again later.' };
    }

    console.log('Calling Edge Function to send email notification');

    try {
      const edgeFunctionUrl = `${supabaseUrl}/functions/v1/send-contact-email`;
      console.log('Edge Function URL:', edgeFunctionUrl);

      const response = await fetch(edgeFunctionUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(formData),
      });

      console.log('Edge Function response status:', response.status);

      const responseText = await response.text();
      console.log('Edge Function response text:', responseText);

      let result;
      try {
        result = JSON.parse(responseText);
      } catch (e) {
        console.error('Failed to parse response as JSON:', e);
        return {
          success: true,
          error: 'Message saved but email notification had an unexpected response format. We will still receive your message.'
        };
      }

      if (!response.ok || !result.success) {
        console.error('Error sending email notification:', result);
        console.log('Response status:', response.status);
        console.log('Response details:', result);

        // Still return success since we saved to the database
        return {
          success: true,
          error: 'Message saved but email notification failed. We will still receive your message.'
        };
      }

      console.log('Email notification sent successfully');
      return { success: true };
    } catch (fetchError) {
      console.error('Error calling Edge Function:', fetchError);

      // Still return success since we saved to the database
      return {
        success: true,
        error: 'Message saved but email notification failed. We will still receive your message.'
      };
    }
  } catch (error) {
    console.error('Error in sendContactEmail:', error);
    if (error instanceof z.ZodError) {
      return {
        success: false,
        error: error.errors.map(e => e.message).join(', ')
      };
    }
    return { success: false, error: 'An unexpected error occurred. Please try again later.' };
  }
}
