"use client"

import { motion } from "framer-motion"
import {
  <PERSON>2,
  <PERSON><PERSON>son,
  Layers,
  Server,
  Database,
  Cpu,
  PenTool,
  GitBranch,
  Box,
  Cloud,
  LucideIcon
} from "lucide-react"

// Map of skill names to their corresponding icons
const skillIconMap: Record<string, LucideIcon> = {
  "JavaScript": <PERSON><PERSON>son,
  "TypeScript": <PERSON><PERSON><PERSON>,
  "React": Code2,
  "Next.js": Layers,
  "Node.js": Server,
  "Express": Server,
  "MongoDB": Database,
  "PostgreSQL": Database,
  "Tailwind CSS": PenTool,
  "Git": GitBranch,
  "Docker": Box,
  "AWS": Cloud,
}

interface SkillBadgeProps {
  name: string
}

export default function SkillBadge({ name }: SkillBadgeProps) {
  // Get the icon component for this skill, or use Code2 as fallback
  const IconComponent = skillIconMap[name] || Code2

  return (
    <div className="flex-shrink-0">
      <motion.div
        whileHover={{ scale: 1.1 }}
        className="flex items-center gap-2 rounded-full bg-blue-100 px-3 py-1.5 md:px-4 md:py-2 text-xs md:text-sm font-medium text-blue-700 dark:bg-blue-900/30 dark:text-blue-400 transition-all hover:bg-blue-200 dark:hover:bg-blue-900/50 shadow-sm"
      >
        <IconComponent className="h-4 w-4" />
        {name}
      </motion.div>
    </div>
  )
}
