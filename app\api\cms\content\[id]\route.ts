import { NextRequest, NextResponse } from 'next/server';
import {
  getContentBySectionAndKey,
  getContentById,
  updateContent,
  deleteContent
} from '@/lib/cms/supabase-cms';
import { UpdateContentPayload } from '@/lib/cms/types';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions);

    // Check if user is authenticated for read operations
    if (!session) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const id = params.id;

    // Check if this is a section/key lookup
    if (id.includes('_')) {
      const [section, key] = id.split('_');
      const content = await getContentBySectionAndKey(section as any, key as any);

      if (!content) {
        return NextResponse.json(
          { error: `Content with section ${section} and key ${key} not found` },
          { status: 404 }
        );
      }

      return NextResponse.json(content);
    }

    // Try to fetch by UUID
    const content = await getContentById(id);

    if (content) {
      return NextResponse.json(content);
    }

    // If not found by UUID or section/key, return 404
    return NextResponse.json(
      { error: 'Content not found' },
      { status: 404 }
    );
  } catch (error: any) {
    console.error(`Error in content GET[id] route:`, error);
    return NextResponse.json(
      { error: error.message || 'Failed to fetch content' },
      { status: 500 }
    );
  }
}

export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions);

    // Check if user is authenticated for write operations
    if (!session) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const id = params.id;
    const payload: UpdateContentPayload = await request.json();

    // Validate payload
    if (!payload.content) {
      return NextResponse.json(
        { error: 'Missing required field: content' },
        { status: 400 }
      );
    }

    const content = await updateContent(id, payload);
    return NextResponse.json(content);
  } catch (error: any) {
    console.error(`Error in content PUT[id] route:`, error);
    return NextResponse.json(
      { error: error.message || 'Failed to update content' },
      { status: 500 }
    );
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions);

    // Check if user is authenticated for write operations
    if (!session) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const id = params.id;
    await deleteContent(id);

    return NextResponse.json(
      { message: 'Content deleted successfully' },
      { status: 200 }
    );
  } catch (error: any) {
    console.error(`Error in content DELETE[id] route:`, error);
    return NextResponse.json(
      { error: error.message || 'Failed to delete content' },
      { status: 500 }
    );
  }
}
