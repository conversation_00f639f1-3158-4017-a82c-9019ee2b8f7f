'use server';

import { ContactFormData, contactFormSchema, sendContactEmail } from '@/lib/email-service';
import { z } from 'zod';

export async function submitContactForm(formData: ContactFormData) {
  try {
    console.log('Server action: submitContactForm called with data:', JSON.stringify(formData));

    // Validate the form data
    const validatedData = contactFormSchema.parse(formData);
    console.log('Server action: Form data validated successfully');

    // Send the email
    console.log('Server action: Calling sendContactEmail');
    const result = await sendContactEmail(validatedData);
    console.log('Server action: sendContactEmail result:', JSON.stringify(result));

    return result;
  } catch (error) {
    console.error('Error in submitContactForm:', error);
    if (error instanceof z.ZodError) {
      return {
        success: false,
        error: error.errors.map(e => e.message).join(', ')
      };
    }
    return {
      success: false,
      error: 'An unexpected error occurred. Please try again later.'
    };
  }
}
