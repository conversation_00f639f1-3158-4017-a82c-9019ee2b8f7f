'use client';

import { useState } from 'react';
import { toast } from 'sonner';
import {
  PackageSelectionFormData,
  ClientInformationFormData,
  PaymentVerificationFormData,
  PackageType,
  calculateTotalPrice,
  calculateDepositAmount
} from '@/lib/mpesa/types';
import { registerClient, verifyMpesaPayment } from '@/app/actions/mpesa-actions';
import PackageSelectionForm from './package-selection-form';
import ClientInformationForm from './client-information-form';
import PaymentInstructions from './payment-instructions';
import PaymentVerificationForm from './payment-verification-form';
import ConfirmationPage from './confirmation-page';
import { Progress } from '@/components/ui/progress';

type Step = 'package-selection' | 'client-information' | 'payment-instructions' | 'payment-verification' | 'confirmation';

export default function PaymentFlow() {
  const [currentStep, setCurrentStep] = useState<Step>('package-selection');
  const [progress, setProgress] = useState(25);

  // Form data state
  const [packageData, setPackageData] = useState<PackageSelectionFormData | null>(null);
  const [clientData, setClientData] = useState<ClientInformationFormData | null>(null);
  const [referenceCode, setReferenceCode] = useState<string>('');
  const [totalAmount, setTotalAmount] = useState<number>(0);
  const [depositAmount, setDepositAmount] = useState<number>(0);
  const [questionnaireUrl, setQuestionnaireUrl] = useState<string>('');

  // Handle package selection form submission
  const handlePackageSelection = (data: PackageSelectionFormData) => {
    setPackageData(data);
    setCurrentStep('client-information');
    setProgress(50);
  };

  // Handle client information form submission
  const handleClientInformation = async (data: ClientInformationFormData) => {
    setClientData(data);

    try {
      // Register client and create project
      const result = await registerClient({
        client_name: data.client_name,
        client_email: data.client_email,
        client_phone: data.client_phone,
        package_type: packageData?.package_type as PackageType,
        additional_services: packageData?.additional_services || [],
        maintenance_plan: packageData?.maintenance_plan,
      });

      if (result.success && result.data) {
        setReferenceCode(result.data.reference_code);
        setTotalAmount(result.data.total_amount);
        setDepositAmount(result.data.deposit_amount);
        setCurrentStep('payment-instructions');
        setProgress(75);
      } else {
        toast.error(result.error || 'Failed to register client. Please try again.');
      }
    } catch (error) {
      console.error('Error registering client:', error);
      toast.error('An unexpected error occurred. Please try again later.');
    }
  };

  // Handle payment instructions continue button
  const handlePaymentInstructionsContinue = () => {
    setCurrentStep('payment-verification');
    setProgress(90);
  };

  // Handle payment verification form submission
  const handlePaymentVerification = async (data: PaymentVerificationFormData) => {
    try {
      // Show loading toast
      const loadingToast = toast.loading('Verifying payment...');

      try {
        // First try using the server action
        console.log('Attempting to verify payment using server action...');
        const result = await verifyMpesaPayment({
          reference_code: referenceCode,
          mpesa_code: data.mpesa_code,
        });

        // Dismiss loading toast
        toast.dismiss(loadingToast);

        if (result.success && result.data) {
          // Set questionnaire URL
          const questionnaireToken = result.data.questionnaire_access_token;
          setQuestionnaireUrl(`/questionnaire/${questionnaireToken}`);

          setCurrentStep('confirmation');
          setProgress(100);
          toast.success('Payment verified successfully!');
        } else {
          toast.error(result.error || 'Failed to verify payment. Please check the M-Pesa code and try again.');
        }
      } catch (serverActionError) {
        console.error('Server action error:', serverActionError);

        // If server action fails, try the API route as fallback
        console.log('Server action failed, trying API route fallback...');
        try {
          // Create form data for API call
          const formData = new FormData();
          formData.append('reference_code', referenceCode);
          formData.append('mpesa_code', data.mpesa_code);

          // Call the API route
          const response = await fetch('/api/mpesa/verify', {
            method: 'POST',
            body: formData,
          });

          // Dismiss loading toast
          toast.dismiss(loadingToast);

          if (response.ok) {
            const result = await response.json();

            if (result.success && result.data) {
              // Set questionnaire URL
              const questionnaireToken = result.data.questionnaire_access_token;
              setQuestionnaireUrl(`/questionnaire/${questionnaireToken}`);

              setCurrentStep('confirmation');
              setProgress(100);
              toast.success('Payment verified successfully!');
            } else {
              toast.error(result.error || 'Failed to verify payment. Please check the M-Pesa code and try again.');
            }
          } else {
            const errorData = await response.json().catch(() => ({}));
            toast.error(errorData.error || 'Failed to verify payment. Please try again later.');
          }
        } catch (apiError) {
          // Dismiss loading toast if still showing
          toast.dismiss(loadingToast);

          console.error('API route error:', apiError);
          toast.error('Unable to verify payment. Please try again later or contact support.');
        }
      }
    } catch (error) {
      console.error('Unexpected error in payment verification:', error);
      toast.error('An unexpected error occurred. Please try again later or contact support.');
    }
  };

  // Handle back button clicks
  const handleBack = (step: Step) => {
    setCurrentStep(step);

    // Update progress based on step
    switch (step) {
      case 'package-selection':
        setProgress(25);
        break;
      case 'client-information':
        setProgress(50);
        break;
      case 'payment-instructions':
        setProgress(75);
        break;
      case 'payment-verification':
        setProgress(90);
        break;
      default:
        break;
    }
  };

  // Render current step
  const renderStep = () => {
    switch (currentStep) {
      case 'package-selection':
        return <PackageSelectionForm onSubmit={handlePackageSelection} />;

      case 'client-information':
        return (
          <ClientInformationForm
            onSubmit={handleClientInformation}
            onBack={() => handleBack('package-selection')}
          />
        );

      case 'payment-instructions':
        return (
          <PaymentInstructions
            referenceCode={referenceCode}
            depositAmount={depositAmount}
            onContinue={handlePaymentInstructionsContinue}
            onBack={() => handleBack('client-information')}
          />
        );

      case 'payment-verification':
        return (
          <PaymentVerificationForm
            referenceCode={referenceCode}
            onSubmit={handlePaymentVerification}
            onBack={() => handleBack('payment-instructions')}
          />
        );

      case 'confirmation':
        return (
          <ConfirmationPage
            clientName={clientData?.client_name || ''}
            clientEmail={clientData?.client_email || ''}
            clientPhone={clientData?.client_phone || ''}
            referenceCode={referenceCode}
            packageType={packageData?.package_type || 'basic'}
            totalAmount={totalAmount}
            depositAmount={depositAmount}
            questionnaireUrl={questionnaireUrl}
          />
        );

      default:
        return null;
    }
  };

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-center mb-6">Start Your Web Development Project</h1>

        <div className="w-full max-w-3xl mx-auto mb-8">
          <div className="flex justify-between mb-2">
            <span className={`text-sm ${currentStep === 'package-selection' ? 'text-primary font-medium' : 'text-muted-foreground'}`}>
              Package
            </span>
            <span className={`text-sm ${currentStep === 'client-information' ? 'text-primary font-medium' : 'text-muted-foreground'}`}>
              Information
            </span>
            <span className={`text-sm ${currentStep === 'payment-instructions' ? 'text-primary font-medium' : 'text-muted-foreground'}`}>
              Payment
            </span>
            <span className={`text-sm ${currentStep === 'payment-verification' ? 'text-primary font-medium' : 'text-muted-foreground'}`}>
              Verification
            </span>
            <span className={`text-sm ${currentStep === 'confirmation' ? 'text-primary font-medium' : 'text-muted-foreground'}`}>
              Confirmation
            </span>
          </div>
          <Progress value={progress} className="h-2" />
        </div>
      </div>

      {renderStep()}
    </div>
  );
}
