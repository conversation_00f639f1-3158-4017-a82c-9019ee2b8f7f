# Questionnaire Testing Guide

This guide will help you test the questionnaire functionality in your Next.js portfolio project, independent of the payment verification process.

## Testing Approaches

There are two main approaches to testing the questionnaire functionality:

1. **Direct Testing**: Generate a test token and access the questionnaire directly
2. **Browser Testing**: Use browser scripts to test the questionnaire forms

## Approach 1: Direct Testing with a Test Token

### Step 1: Generate a Test Token

Run the `generate-test-token.js` script to create a test client project and generate a questionnaire access token:

```bash
node generate-test-token.js
```

This script will:
1. Create a test client project in your Supabase database
2. Generate a questionnaire access token
3. Print the token and questionnaire URL

### Step 2: Access the Questionnaire

Open the questionnaire URL in your browser:

```
http://localhost:3000/questionnaire/YOUR_TOKEN
```

Replace `YOUR_TOKEN` with the token generated in Step 1.

### Step 3: Test the Questionnaire Manually

Fill out the questionnaire forms manually to test the functionality:

1. Fill out the General Information form
2. Click "Continue"
3. Fill out the Package-Specific form
4. Click "Continue"
5. Verify that the completion page is displayed

## Approach 2: Browser Testing with Scripts

### Step 1: Access the Questionnaire

If you already have a valid questionnaire token, navigate to the questionnaire page:

```
http://localhost:3000/questionnaire/YOUR_TOKEN
```

### Step 2: Load the Test Script

Open your browser's developer console (F12 or Ctrl+Shift+I) and copy-paste the entire content of `questionnaire-test.js`.

### Step 3: Run the Test Functions

1. If you're not already on a questionnaire page, navigate to one:
   ```javascript
   goToQuestionnaire("YOUR_TOKEN");
   ```

2. Test the questionnaire flow:
   ```javascript
   testQuestionnaireFlow();
   ```

3. Check if the current token is valid:
   ```javascript
   checkTokenValidity(getTokenFromUrl());
   ```

4. Fill individual forms:
   ```javascript
   fillGeneralInfoForm();
   fillBasicPackageForm();
   ```

## Troubleshooting

### Invalid Token

If you see an "Invalid Access Token" message, your token might be:
- Expired
- Invalid format
- Not found in the database

**Solution**: Generate a new token using the `generate-test-token.js` script.

### Form Fields Not Found

If the test script reports that form fields are not found, it could be due to:
- Different field names in your implementation
- Different HTML structure
- JavaScript errors

**Solution**: Inspect the form elements in your browser's developer tools and update the test script accordingly.

### Submission Errors

If the form submits but you encounter errors, check:
- Server-side validation
- Database constraints
- Server logs for error details

**Solution**: Look at the browser console and network tab for error details.

## Testing the Complete Flow

Once you've verified that the questionnaire functionality works correctly, you can test the complete flow:

1. Start from the payment flow page
2. Select a package
3. Enter client information
4. Complete the payment verification
5. Access the questionnaire
6. Fill out the questionnaire forms

Use the `test-payment-flow-enhanced.js` script to help with the payment flow testing.

## Database Verification

To verify that the questionnaire responses are being saved correctly:

1. Fill out and submit the questionnaire forms
2. Check the `questionnaire_responses` table in your Supabase database
3. Verify that the responses match what you entered in the forms

You can use the Supabase dashboard or run SQL queries to check the database.

## Next Steps

After testing the questionnaire functionality, you might want to:

1. Test the payment verification process
2. Test the email notification system
3. Test the admin dashboard for viewing questionnaire responses

Use the other testing scripts provided to help with these tasks.
