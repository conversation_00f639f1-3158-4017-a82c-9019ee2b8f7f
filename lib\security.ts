import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth";
import { NextRequest, NextResponse } from "next/server";

/**
 * Validates if the current session is authenticated and valid
 */
export async function validateSession() {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session) {
      return { isValid: false, error: "No session found" };
    }

    // Check if session is expired
    if (session.expires) {
      const expiryTime = new Date(session.expires).getTime();
      const currentTime = new Date().getTime();
      
      if (currentTime >= expiryTime) {
        return { isValid: false, error: "Session expired" };
      }
    }

    return { isValid: true, session };
  } catch (error) {
    console.error("Session validation error:", error);
    return { isValid: false, error: "Session validation failed" };
  }
}

/**
 * Middleware helper to protect API routes
 */
export async function withAuth(handler: (req: NextRequest) => Promise<NextResponse>) {
  return async (req: NextRequest) => {
    const validation = await validateSession();
    
    if (!validation.isValid) {
      return NextResponse.json(
        { error: "Unauthorized", message: validation.error },
        { status: 401 }
      );
    }

    return handler(req);
  };
}

/**
 * Security headers for admin routes
 */
export const securityHeaders = {
  'X-Frame-Options': 'DENY',
  'X-Content-Type-Options': 'nosniff',
  'Referrer-Policy': 'strict-origin-when-cross-origin',
  'X-XSS-Protection': '1; mode=block',
  'Strict-Transport-Security': 'max-age=31536000; includeSubDomains',
  'Content-Security-Policy': "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data: https:; font-src 'self' data:; connect-src 'self' https:; frame-ancestors 'none';",
};

/**
 * Apply security headers to a response
 */
export function applySecurityHeaders(response: NextResponse): NextResponse {
  Object.entries(securityHeaders).forEach(([key, value]) => {
    response.headers.set(key, value);
  });
  
  return response;
}

/**
 * Rate limiting helper (simple in-memory implementation)
 * In production, you should use Redis or a proper rate limiting service
 */
const rateLimitMap = new Map<string, { count: number; resetTime: number }>();

export function rateLimit(identifier: string, maxAttempts: number = 5, windowMs: number = 300000): boolean {
  const now = Date.now();
  const record = rateLimitMap.get(identifier);

  if (!record || now > record.resetTime) {
    rateLimitMap.set(identifier, { count: 1, resetTime: now + windowMs });
    return true;
  }

  if (record.count >= maxAttempts) {
    return false;
  }

  record.count++;
  return true;
}

/**
 * Clean up expired rate limit records
 */
export function cleanupRateLimit(): void {
  const now = Date.now();
  for (const [key, record] of rateLimitMap.entries()) {
    if (now > record.resetTime) {
      rateLimitMap.delete(key);
    }
  }
}

// Clean up rate limit records every 5 minutes
if (typeof window === 'undefined') {
  setInterval(cleanupRateLimit, 300000);
}
