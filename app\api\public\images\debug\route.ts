import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@supabase/supabase-js';

// Create a Supabase client for public routes
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || '';
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || '';
const supabase = createClient(supabaseUrl, supabaseAnonKey);

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const projectName = searchParams.get('projectName');
    
    // Get all project images
    const { data: allImages, error: allImagesError } = await supabase
      .from('portfolio_images')
      .select('*')
      .eq('type', 'project')
      .order('created_at', { ascending: false });
    
    if (allImagesError) {
      console.error('Error fetching all project images:', allImagesError);
      return NextResponse.json(
        { error: `Failed to fetch images: ${allImagesError.message}` },
        { status: 500 }
      );
    }
    
    // If a project name is provided, try to find matches
    let exactMatch = null;
    let partialMatches = [];
    
    if (projectName) {
      // Try exact match (case insensitive)
      exactMatch = allImages.find(img => 
        img.name.toLowerCase() === projectName.toLowerCase()
      );
      
      // Find partial matches
      partialMatches = allImages.filter(img => 
        img.name.toLowerCase().includes(projectName.toLowerCase()) ||
        projectName.toLowerCase().includes(img.name.toLowerCase())
      );
    }
    
    return NextResponse.json({
      totalImages: allImages.length,
      allImages: allImages.map(img => ({
        id: img.id,
        name: img.name,
        type: img.type,
        created_at: img.created_at
      })),
      searchResults: projectName ? {
        query: projectName,
        exactMatch: exactMatch ? {
          id: exactMatch.id,
          name: exactMatch.name,
          public_url: exactMatch.public_url
        } : null,
        partialMatches: partialMatches.map(img => ({
          id: img.id,
          name: img.name,
          public_url: img.public_url,
          nameMatchScore: calculateMatchScore(img.name, projectName)
        }))
      } : null
    });
  } catch (error: any) {
    console.error('Error in debug images route:', error);
    return NextResponse.json(
      { error: error.message || 'Failed to fetch debug info' },
      { status: 500 }
    );
  }
}

// Helper function to calculate a simple match score between two strings
function calculateMatchScore(str1: string, str2: string): number {
  const s1 = str1.toLowerCase();
  const s2 = str2.toLowerCase();
  
  // Exact match
  if (s1 === s2) return 100;
  
  // One contains the other
  if (s1.includes(s2)) return 80;
  if (s2.includes(s1)) return 80;
  
  // Calculate percentage of matching words
  const words1 = s1.split(/\s+/);
  const words2 = s2.split(/\s+/);
  
  let matchCount = 0;
  for (const word1 of words1) {
    if (word1.length < 3) continue; // Skip short words
    for (const word2 of words2) {
      if (word2.length < 3) continue; // Skip short words
      if (word1 === word2 || word1.includes(word2) || word2.includes(word1)) {
        matchCount++;
        break;
      }
    }
  }
  
  return Math.round((matchCount / Math.max(words1.length, words2.length)) * 70);
}
