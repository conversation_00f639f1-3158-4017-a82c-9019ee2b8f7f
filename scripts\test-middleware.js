#!/usr/bin/env node

/**
 * Test script to verify middleware configuration
 */

const fs = require('fs');
const path = require('path');

console.log('🔍 Testing Middleware Configuration...\n');

// Check if middleware.ts exists
const middlewarePath = path.join(process.cwd(), 'middleware.ts');
if (fs.existsSync(middlewarePath)) {
  console.log('✅ middleware.ts exists in project root');
} else {
  console.log('❌ middleware.ts not found in project root');
  process.exit(1);
}

// Read and analyze middleware content
const middlewareContent = fs.readFileSync(middlewarePath, 'utf8');

// Check for required imports
const requiredImports = [
  'NextRequest',
  'NextResponse',
  'getToken'
];

console.log('\n📦 Checking imports...');
requiredImports.forEach(imp => {
  if (middlewareContent.includes(imp)) {
    console.log(`✅ ${imp} imported`);
  } else {
    console.log(`❌ ${imp} missing`);
  }
});

// Check for middleware function
console.log('\n🔧 Checking middleware function...');
if (middlewareContent.includes('export async function middleware')) {
  console.log('✅ Middleware function exported correctly');
} else {
  console.log('❌ Middleware function not found or incorrect export');
}

// Check for config export
console.log('\n⚙️  Checking configuration...');
if (middlewareContent.includes('export const config')) {
  console.log('✅ Config object exported');
} else {
  console.log('❌ Config object not found');
}

// Check matcher patterns
if (middlewareContent.includes("'/admin'")) {
  console.log('✅ /admin route in matcher');
} else {
  console.log('❌ /admin route not in matcher');
}

if (middlewareContent.includes("'/admin/:path*'")) {
  console.log('✅ /admin/:path* route in matcher');
} else {
  console.log('❌ /admin/:path* route not in matcher');
}

// Check environment variables
console.log('\n🔐 Checking environment variables...');
if (process.env.NEXTAUTH_SECRET) {
  console.log('✅ NEXTAUTH_SECRET is set');
} else {
  console.log('❌ NEXTAUTH_SECRET is not set');
}

// Check Next.js version
console.log('\n📋 Checking Next.js version...');
try {
  const packageJson = JSON.parse(fs.readFileSync('package.json', 'utf8'));
  const nextVersion = packageJson.dependencies.next;
  console.log(`✅ Next.js version: ${nextVersion}`);
  
  // Check if version supports middleware
  const versionNumber = parseFloat(nextVersion.replace(/[^\d.]/g, ''));
  if (versionNumber >= 12.0) {
    console.log('✅ Next.js version supports middleware');
  } else {
    console.log('❌ Next.js version may not support middleware properly');
  }
} catch (error) {
  console.log('❌ Could not read package.json');
}

console.log('\n' + '='.repeat(50));
console.log('🔒 Middleware Configuration Test Complete');
console.log('\nIf all checks pass but middleware still doesn\'t work:');
console.log('1. Restart the development server');
console.log('2. Clear browser cache and cookies');
console.log('3. Test in incognito/private window');
console.log('4. Check browser console for middleware logs');
