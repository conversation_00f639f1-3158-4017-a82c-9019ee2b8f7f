'use client'

import { useEffect, useState } from 'react'
import { performanceMonitor, type PerformanceReport } from '@/lib/performance'

interface PerformanceMonitorProps {
  enabled?: boolean
  reportInterval?: number
  showInConsole?: boolean
}

export default function PerformanceMonitor({
  enabled = typeof window !== 'undefined' && process.env.NODE_ENV === 'development',
  reportInterval = 30000, // 30 seconds
  showInConsole = true
}: PerformanceMonitorProps) {
  const [report, setReport] = useState<PerformanceReport | null>(null)

  useEffect(() => {
    if (!enabled) return

    // Monitor page load performance
    const measurePageLoad = () => {
      if (typeof window === 'undefined') return

      const navigation = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming
      if (navigation) {
        performanceMonitor.recordMetric({
          name: 'Page.Load',
          value: navigation.loadEventEnd - navigation.navigationStart,
          unit: 'ms',
          timestamp: Date.now(),
          metadata: {
            domContentLoaded: navigation.domContentLoadedEventEnd - navigation.navigationStart,
            firstByte: navigation.responseStart - navigation.navigationStart,
            domComplete: navigation.domComplete - navigation.navigationStart
          }
        })
      }
    }

    // Monitor resource loading
    const measureResources = () => {
      if (typeof window === 'undefined') return

      const resources = performance.getEntriesByType('resource')
      resources.forEach((resource: PerformanceResourceTiming) => {
        if (resource.duration > 0) {
          performanceMonitor.recordMetric({
            name: `Resource.${resource.initiatorType}`,
            value: resource.duration,
            unit: 'ms',
            timestamp: Date.now(),
            metadata: {
              name: resource.name,
              size: resource.transferSize,
              cached: resource.transferSize === 0
            }
          })
        }
      })
    }

    // Monitor memory usage (if available)
    const measureMemory = () => {
      if (typeof window === 'undefined' || !(performance as any).memory) return

      const memory = (performance as any).memory
      performanceMonitor.recordMetric({
        name: 'Memory.Used',
        value: memory.usedJSHeapSize,
        unit: 'bytes',
        timestamp: Date.now(),
        metadata: {
          total: memory.totalJSHeapSize,
          limit: memory.jsHeapSizeLimit
        }
      })
    }

    // Initial measurements
    measurePageLoad()
    measureResources()
    measureMemory()

    // Set up periodic reporting
    const interval = setInterval(() => {
      measureMemory()
      const currentReport = performanceMonitor.generateReport()
      setReport(currentReport)

      if (showInConsole) {
        performanceMonitor.logReport()
      }
    }, reportInterval)

    // Monitor long tasks (if supported)
    if ('PerformanceObserver' in window) {
      try {
        const longTaskObserver = new PerformanceObserver((list) => {
          list.getEntries().forEach((entry) => {
            performanceMonitor.recordMetric({
              name: 'LongTask',
              value: entry.duration,
              unit: 'ms',
              timestamp: Date.now(),
              metadata: {
                startTime: entry.startTime,
                name: entry.name
              }
            })
          })
        })
        longTaskObserver.observe({ entryTypes: ['longtask'] })

        return () => {
          clearInterval(interval)
          longTaskObserver.disconnect()
        }
      } catch (error) {
        console.warn('Long task monitoring not supported:', error)
      }
    }

    return () => {
      clearInterval(interval)
    }
  }, [enabled, reportInterval, showInConsole])

  // Don't render anything in production or when disabled
  if (!enabled || process.env.NODE_ENV === 'production') {
    return null
  }

  return (
    <div className="fixed bottom-4 right-4 z-50 max-w-sm">
      {report && (
        <div className="bg-black/80 text-white p-3 rounded-lg text-xs font-mono">
          <div className="font-bold mb-2">⚡ Performance</div>
          <div className="space-y-1">
            <div>Metrics: {report.summary.totalMetrics}</div>
            <div>Avg Response: {report.summary.averageResponseTime.toFixed(1)}ms</div>
            <div>Slowest: {report.summary.slowestOperation}</div>
            {report.recommendations.length > 0 && (
              <div className="mt-2 pt-2 border-t border-gray-600">
                <div className="font-bold">Recommendations:</div>
                {report.recommendations.slice(0, 2).map((rec, index) => (
                  <div key={index} className="text-yellow-300 text-xs">
                    • {rec.substring(0, 50)}...
                  </div>
                ))}
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  )
}

/**
 * Hook to measure component render performance
 */
export function useRenderPerformance(componentName: string) {
  useEffect(() => {
    const startTime = performance.now()

    return () => {
      const endTime = performance.now()
      performanceMonitor.recordMetric({
        name: `Component.${componentName}`,
        value: endTime - startTime,
        unit: 'ms',
        timestamp: Date.now(),
        metadata: { type: 'render' }
      })
    }
  })
}

/**
 * Higher-order component to measure render performance
 */
export function withPerformanceMonitoring<P extends object>(
  WrappedComponent: React.ComponentType<P>,
  componentName?: string
) {
  const displayName = componentName || WrappedComponent.displayName || WrappedComponent.name || 'Component'

  const MonitoredComponent = (props: P) => {
    useRenderPerformance(displayName)
    return <WrappedComponent {...props} />
  }

  MonitoredComponent.displayName = `withPerformanceMonitoring(${displayName})`

  return MonitoredComponent
}
