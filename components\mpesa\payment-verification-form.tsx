'use client';

import { useState } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { motion } from 'framer-motion';
import { PaymentVerificationFormData, paymentVerificationSchema } from '@/lib/mpesa/types';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { Card, CardContent } from '@/components/ui/card';
import { ArrowLeft } from 'lucide-react';

interface PaymentVerificationFormProps {
  referenceCode: string;
  onSubmit: (data: PaymentVerificationFormData) => void;
  onBack: () => void;
}

export default function PaymentVerificationForm({
  referenceCode,
  onSubmit,
  onBack,
}: PaymentVerificationFormProps) {
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Initialize form with react-hook-form and zod validation
  const form = useForm<PaymentVerificationFormData>({
    resolver: zodResolver(paymentVerificationSchema),
    defaultValues: {
      mpesa_code: '',
    },
  });

  // Handle form submission
  const handleSubmit = (data: PaymentVerificationFormData) => {
    setIsSubmitting(true);

    try {
      onSubmit(data);
    } catch (error) {
      console.error('Error submitting payment verification:', error);
      setIsSubmitting(false);
    }
  };

  return (
    <motion.div
      initial={{ opacity: 0, x: 50 }}
      animate={{ opacity: 1, x: 0 }}
      exit={{ opacity: 0, x: -50 }}
      transition={{ duration: 0.5 }}
      className="w-full max-w-2xl mx-auto"
    >
      <h2 className="text-2xl font-bold text-center mb-4 sm:mb-6">Verify Your Payment</h2>

      <Card className="mb-4 sm:mb-6 mobile-card">
        <CardContent className="pt-4 sm:pt-6">
          <div className="mobile-spacing">
            <p className="text-center">
              Please enter the M-Pesa confirmation code you received after making the payment.
            </p>

            <div className="bg-muted p-3 sm:p-4 rounded-lg mb-2 sm:mb-4">
              <p className="font-medium mb-2">Your reference code:</p>
              <div className="bg-background p-2 sm:p-3 rounded border overflow-x-auto">
                <span className="font-mono text-base sm:text-lg font-bold">{referenceCode}</span>
              </div>
            </div>

            <Form {...form}>
              <form onSubmit={form.handleSubmit(handleSubmit)} className="mobile-spacing">
                <FormField
                  control={form.control}
                  name="mpesa_code"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="text-base">M-Pesa Confirmation Code</FormLabel>
                      <FormControl>
                        <motion.div
                          whileHover={{ scale: 1.01 }}
                          transition={{ duration: 0.2 }}
                        >
                          <Input
                            placeholder="Enter the 10-character code (e.g., QDE12RTYHJ)"
                            {...field}
                            className="mobile-input rounded-md border border-gray-300 dark:border-gray-700 bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 focus:border-blue-600 dark:focus:border-blue-400 focus:outline-none"
                            maxLength={10}
                            value={field.value.toUpperCase()}
                            onChange={(e) => field.onChange(e.target.value.toUpperCase())}
                          />
                        </motion.div>
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <div className="bg-yellow-50 dark:bg-yellow-900/20 text-yellow-800 dark:text-yellow-200 p-3 sm:p-4 rounded-lg">
                  <p className="text-sm">
                    <strong>Where to find your code:</strong> The M-Pesa confirmation code is a 10-character
                    alphanumeric code found in the M-Pesa confirmation SMS you received after making the payment.
                    It usually starts with a letter followed by numbers and letters.
                  </p>
                </div>

                <div className="flex flex-col sm:flex-row justify-between gap-4 pt-4">
                  <Button
                    type="button"
                    variant="outline"
                    onClick={onBack}
                    disabled={isSubmitting}
                    className="mobile-button order-2 sm:order-1"
                  >
                    <ArrowLeft className="mr-2 h-4 w-4" /> Back
                  </Button>

                  <motion.div
                    whileHover={{ scale: 1.03 }}
                    whileTap={{ scale: 0.97 }}
                    transition={{ duration: 0.2 }}
                    className="w-full sm:w-auto order-1 sm:order-2"
                  >
                    <Button
                      type="submit"
                      disabled={isSubmitting}
                      className="bg-blue-600 hover:bg-blue-700 dark:bg-blue-500 dark:hover:bg-blue-600 mobile-button w-full"
                    >
                      {isSubmitting ? 'Verifying...' : 'Verify Payment'}
                    </Button>
                  </motion.div>
                </div>
              </form>
            </Form>
          </div>
        </CardContent>
      </Card>
    </motion.div>
  );
}
