import { NextAuthOptions } from "next-auth";
import CredentialsProvider from "next-auth/providers/credentials";
import crypto from "crypto";
import { createClient } from '@supabase/supabase-js';

// Create a Supabase client
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || '';
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || '';
const supabase = createClient(supabaseUrl, supabaseAnonKey);

// Simple password verification function
const verifyPassword = (password: string, storedHash: string): boolean => {
  try {
    const [salt, hash] = storedHash.split(':');
    const calculatedHash = crypto.pbkdf2Sync(password, salt, 1000, 64, 'sha256').toString('hex');
    return calculatedHash === hash;
  } catch (error) {
    console.error('Error verifying password:', error);
    return false;
  }
};

export const authOptions: NextAuthOptions = {
  providers: [
    CredentialsProvider({
      name: "Credentials",
      credentials: {
        email: { label: "Email", type: "email" },
        password: { label: "Password", type: "password" }
      },
      async authorize(credentials) {
        if (!credentials?.email || !credentials?.password) {
          return null;
        }

        // For simplicity, we're using a hardcoded admin user
        // In a real application, you would fetch this from a database
        const adminEmail = process.env.ADMIN_EMAIL;
        const adminPasswordHash = process.env.ADMIN_PASSWORD_HASH;

        if (!adminEmail || !adminPasswordHash) {
          console.error("Admin credentials not configured");
          return null;
        }

        if (credentials.email !== adminEmail) {
          return null;
        }

        // Verify password using our custom function
        const isValidPassword = verifyPassword(
          credentials.password,
          adminPasswordHash
        );

        if (!isValidPassword) {
          return null;
        }

        // Return a user object
        return {
          id: "admin",
          email: adminEmail,
          name: "Admin",
        };
      }
    })
  ],
  session: {
    strategy: "jwt",
    maxAge: 8 * 60 * 60, // 8 hours (more secure than 30 days)
    updateAge: 60 * 60, // Update session every hour
  },
  pages: {
    signIn: "/admin/login",
    error: "/admin/login",
  },
  callbacks: {
    async jwt({ token, user, trigger }) {
      if (user) {
        token.id = user.id;
        token.email = user.email;
        token.name = user.name;
        token.loginTime = Date.now();
      }

      // Add security check for token age
      if (token.loginTime) {
        const tokenAge = Date.now() - (token.loginTime as number);
        const maxAge = 8 * 60 * 60 * 1000; // 8 hours in milliseconds

        if (tokenAge > maxAge) {
          // Token is too old, force re-authentication
          return null;
        }
      }

      return token;
    },
    async session({ session, token }) {
      if (token) {
        session.user.id = token.id as string;
        session.user.email = token.email as string;
        session.user.name = token.name as string;

        // Add session metadata for security
        session.loginTime = token.loginTime as number;
      }
      return session;
    },
    async redirect({ url, baseUrl }) {
      // Ensure redirects stay within the application
      if (url.startsWith("/")) return `${baseUrl}${url}`;
      if (new URL(url).origin === baseUrl) return url;
      return baseUrl;
    },
  },
  secret: process.env.NEXTAUTH_SECRET,
  trustHost: true,
};

// Add this to your next-auth.d.ts file to extend the session types
declare module "next-auth" {
  interface User {
    id: string;
    email: string;
    name: string;
  }

  interface Session {
    user: User;
    loginTime?: number;
  }

  interface JWT {
    loginTime?: number;
  }
}
