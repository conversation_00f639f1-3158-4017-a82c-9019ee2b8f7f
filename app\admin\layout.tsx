"use client";

import { useEffect, useState } from "react";
import { useSession, signOut } from "next-auth/react";
import { usePathname, useRouter } from "next/navigation";
import { Sidebar } from "@/components/admin/sidebar";
import { AdminMobileMenu } from "@/components/admin/AdminMobileMenu";
import { useIsMobile } from "@/hooks/use-mobile";
import { toast } from "sonner";

export default function AdminLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const { data: session, status } = useSession();
  const router = useRouter();
  const pathname = usePathname();
  const [isLoading, setIsLoading] = useState(true);
  const [sessionChecked, setSessionChecked] = useState(false);
  const isMobile = useIsMobile();

  // Session validation and security checks
  useEffect(() => {
    // Skip authentication check for login page
    if (pathname === "/admin/login") {
      setIsLoading(false);
      setSessionChecked(true);
      return;
    }

    // Handle authentication states
    if (status === "loading") {
      return; // Still loading, wait
    }

    if (status === "unauthenticated") {
      if (sessionChecked) {
        toast.error("Session expired. Please log in again.");
      }
      router.push("/admin/login");
      return;
    }

    if (status === "authenticated") {
      // Validate session token expiry
      if (session?.expires) {
        const expiryTime = new Date(session.expires).getTime();
        const currentTime = new Date().getTime();

        if (currentTime >= expiryTime) {
          toast.error("Session expired. Please log in again.");
          signOut({ callbackUrl: "/admin/login" });
          return;
        }
      }

      setIsLoading(false);
      setSessionChecked(true);
    }
  }, [status, session, router, pathname, sessionChecked]);

  // Auto-logout on session expiry
  useEffect(() => {
    if (status === "authenticated" && session?.expires) {
      const expiryTime = new Date(session.expires).getTime();
      const currentTime = new Date().getTime();
      const timeUntilExpiry = expiryTime - currentTime;

      if (timeUntilExpiry > 0) {
        const timeoutId = setTimeout(() => {
          toast.error("Session expired. You have been logged out.");
          signOut({ callbackUrl: "/admin/login" });
        }, timeUntilExpiry);

        return () => clearTimeout(timeoutId);
      }
    }
  }, [session, status]);

  // Prepare content based on state
  const renderContent = () => {
    // Login page
    if (pathname === "/admin/login") {
      return children;
    }

    // Loading state or unauthenticated
    if (isLoading || status === "loading" || status === "unauthenticated") {
      return (
        <div className="flex h-screen w-full items-center justify-center bg-gray-50 dark:bg-gray-900">
          <div className="text-center">
            <div className="h-8 w-8 animate-spin rounded-full border-b-2 border-t-2 border-blue-500 mx-auto mb-4"></div>
            <p className="text-sm text-gray-600 dark:text-gray-400 font-medium">
              {status === "unauthenticated" ? "Redirecting to login..." : "Loading..."}
            </p>
          </div>
        </div>
      );
    }

    // Ensure we have a valid session before rendering admin content
    if (status !== "authenticated" || !session) {
      return (
        <div className="flex h-screen w-full items-center justify-center bg-gray-50 dark:bg-gray-900">
          <div className="text-center">
            <p className="text-sm text-red-600 dark:text-red-400 font-medium">
              Authentication required. Redirecting...
            </p>
          </div>
        </div>
      );
    }

    // Admin layout
    return (
      <div className="flex h-screen flex-col md:flex-row">
        {isMobile ? (
          <>
            <AdminMobileMenu />
            <main className="flex-1 overflow-auto p-4">
              {children}
            </main>
          </>
        ) : (
          <>
            <Sidebar />
            <main className="flex-1 overflow-auto p-6">
              {children}
            </main>
          </>
        )}
      </div>
    );
  };

  // Render the layout
  return renderContent();
}
