"use client";

import { useState, useEffect } from 'react';
import { ContentSection, ContentKey, ImageType, PortfolioImage } from '@/lib/cms/types';
import { getContent, getSectionContent, getLatestImageByType, getImageByName } from '@/lib/cms/client';

// Hook to fetch a single content item
export function useCmsContent(section: ContentSection, key: ContentKey) {
  const [content, setContent] = useState<string>('');
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchContent = async () => {
      try {
        setIsLoading(true);
        const data = await getContent(section, key);
        setContent(data);
        setError(null);
      } catch (err: any) {
        console.error(`Error fetching content for ${section}.${key}:`, err);
        setError(err.message || 'Failed to fetch content');
      } finally {
        setIsLoading(false);
      }
    };

    fetchContent();
  }, [section, key]);

  return { content, isLoading, error };
}

// Hook to fetch all content for a section
export function useCmsSectionContent(section: ContentSection) {
  const [content, setContent] = useState<Record<string, string>>({});
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchContent = async () => {
      try {
        setIsLoading(true);
        const data = await getSectionContent(section);
        setContent(data);
        setError(null);
      } catch (err: any) {
        console.error(`Error fetching content for section ${section}:`, err);
        setError(err.message || 'Failed to fetch content');
      } finally {
        setIsLoading(false);
      }
    };

    fetchContent();
  }, [section]);

  return { content, isLoading, error };
}

// Hook to fetch the latest image of a specific type
export function useCmsImage(type: ImageType) {
  const [imageUrl, setImageUrl] = useState<string | null>(null);
  const [altText, setAltText] = useState<string>('');
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    // Set default image based on type
    const setDefaultImage = () => {
      switch (type) {
        case 'profile':
          setImageUrl('/images/profile.jpg');
          setAltText('Profile Picture');
          break;
        case 'project':
          setImageUrl('/images/placeholder-project.jpg');
          setAltText('Project Image');
          break;
        default:
          setImageUrl(null);
          setAltText('');
      }
    };

    const fetchImage = async () => {
      try {
        setIsLoading(true);

        // Set default image first to avoid flicker
        setDefaultImage();

        // Then try to fetch the actual image
        const image = await getLatestImageByType(type);

        if (image && image.public_url) {
          setImageUrl(image.public_url);
          setAltText(image.alt_text || `${type} Image`);
          setError(null);
        } else {
          // Keep the default image that was already set
          console.log(`No ${type} image found, using default`);
        }
      } catch (err: any) {
        console.error(`Error fetching ${type} image:`, err);
        setError(err.message || 'Failed to fetch image');

        // Default image is already set, so no need to set it again
      } finally {
        setIsLoading(false);
      }
    };

    fetchImage();
  }, [type]);

  return { imageUrl, altText, isLoading, error };
}

// Hook to fetch a project image by name
export function useProjectImageByName(name: string) {
  const [imageUrl, setImageUrl] = useState<string | null>(null);
  const [altText, setAltText] = useState<string>('');
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [debugInfo, setDebugInfo] = useState<any>(null);

  useEffect(() => {
    const fetchImage = async () => {
      try {
        setIsLoading(true);

        // Clean the name to improve matching
        const cleanedName = name.trim();

        if (!cleanedName) {
          throw new Error('Project name is empty');
        }

        console.log(`[useProjectImageByName] Fetching image for project: "${cleanedName}"`);

        // First, fetch debug info to see what's available
        try {
          const debugRes = await fetch(`/api/public/images/debug?projectName=${encodeURIComponent(cleanedName)}`);
          if (debugRes.ok) {
            const debugData = await debugRes.json();
            setDebugInfo(debugData);
            console.log(`[useProjectImageByName] Debug info for "${cleanedName}":`, debugData);

            // Log all available project images
            console.log(`[useProjectImageByName] Available project images (${debugData.totalImages}):`);
            debugData.allImages.forEach((img: any) => {
              console.log(`- ${img.name} (${img.id.substring(0, 8)}...)`);
            });

            // Log search results
            if (debugData.searchResults) {
              if (debugData.searchResults.exactMatch) {
                console.log(`[useProjectImageByName] Exact match found: ${debugData.searchResults.exactMatch.name}`);
              } else {
                console.log(`[useProjectImageByName] No exact match found for "${cleanedName}"`);
              }

              if (debugData.searchResults.partialMatches.length > 0) {
                console.log(`[useProjectImageByName] Partial matches found (${debugData.searchResults.partialMatches.length}):`);
                debugData.searchResults.partialMatches.forEach((match: any) => {
                  console.log(`- ${match.name} (Score: ${match.nameMatchScore}%)`);
                });
              } else {
                console.log(`[useProjectImageByName] No partial matches found for "${cleanedName}"`);
              }
            }
          }
        } catch (debugErr) {
          console.error(`[useProjectImageByName] Error fetching debug info:`, debugErr);
        }

        // Now try to get the actual image
        const image = await getImageByName(cleanedName);

        if (image) {
          console.log(`[useProjectImageByName] Found image for "${cleanedName}":`, image.name);
          console.log(`[useProjectImageByName] Image URL:`, image.public_url);
          setImageUrl(image.public_url);
          setAltText(image.alt_text);
        } else {
          console.log(`[useProjectImageByName] No image found for "${cleanedName}", using default`);
          // Set default project image if not found
          setImageUrl('/images/projects/default-project.jpeg');
          setAltText(`${cleanedName} Project Image`);
        }

        setError(null);
      } catch (err: any) {
        console.error(`[useProjectImageByName] Error fetching image for ${name}:`, err);
        setError(err.message || 'Failed to fetch image');

        // Set default image on error
        setImageUrl('/images/projects/default-project.jpeg');
        setAltText(`${name} Project Image`);
      } finally {
        setIsLoading(false);
      }
    };

    if (name) {
      fetchImage();
    }
  }, [name]);

  return { imageUrl, altText, isLoading, error, debugInfo };
}
