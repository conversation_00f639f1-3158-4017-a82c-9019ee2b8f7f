import { serve } from "https://deno.land/std@0.168.0/http/server.ts";
import { corsHeaders } from "../_shared/cors.ts";

interface ProjectRequestNotificationPayload {
  client_name: string;
  client_email: string;
  client_phone: string;
  package_type: string;
  additional_services?: string[];
  maintenance_plan?: string | null;
  total_amount: number;
  reference_code: string;
  created_at: string;
}

const RESEND_API_KEY = Deno.env.get("RESEND_API_KEY") || "";
const RECIPIENT_EMAIL = Deno.env.get("RECIPIENT_EMAIL") || "<EMAIL>";

// Helper function to format package type
function formatPackageType(packageType: string): string {
  switch (packageType) {
    case 'basic':
      return 'Basic Package';
    case 'standard':
      return 'Standard Package';
    case 'premium':
      return 'Premium Package';
    default:
      return packageType;
  }
}

// Helper function to format currency
function formatCurrency(amount: number): string {
  return `KES ${amount.toLocaleString()}`;
}

// Helper function to format additional services
function formatAdditionalServices(services?: string[]): string {
  if (!services || services.length === 0) {
    return 'None';
  }
  return services.join(', ');
}

// Helper function to format date
function formatDate(dateString: string): string {
  const date = new Date(dateString);
  return date.toLocaleString('en-US', {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit',
    timeZone: 'Africa/Nairobi'
  });
}

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === "OPTIONS") {
    return new Response("ok", { headers: corsHeaders });
  }

  try {
    // Get the request payload
    const payload: ProjectRequestNotificationPayload = await req.json();
    
    // Validate the payload
    if (!payload.client_name || !payload.client_email || !payload.package_type || !payload.reference_code) {
      return new Response(
        JSON.stringify({
          success: false,
          error: "Missing required fields",
        }),
        {
          headers: { ...corsHeaders, "Content-Type": "application/json" },
          status: 400,
        }
      );
    }

    // Create the email HTML content
    const emailHtml = `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; background-color: #f9f9f9;">
        <div style="background-color: white; padding: 30px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1);">
          <h1 style="color: #2563eb; margin-bottom: 20px; text-align: center;">🚀 New Project Request Received!</h1>
          
          <div style="background-color: #f8fafc; padding: 20px; border-radius: 8px; margin-bottom: 20px;">
            <h2 style="color: #1e40af; margin-top: 0;">Client Information</h2>
            <p><strong>Name:</strong> ${payload.client_name}</p>
            <p><strong>Email:</strong> ${payload.client_email}</p>
            <p><strong>Phone:</strong> ${payload.client_phone}</p>
            <p><strong>Reference Code:</strong> <span style="background-color: #dbeafe; padding: 4px 8px; border-radius: 4px; font-family: monospace;">${payload.reference_code}</span></p>
          </div>

          <div style="background-color: #f0f9ff; padding: 20px; border-radius: 8px; margin-bottom: 20px;">
            <h2 style="color: #0369a1; margin-top: 0;">Project Details</h2>
            <p><strong>Package:</strong> ${formatPackageType(payload.package_type)}</p>
            <p><strong>Total Amount:</strong> <span style="color: #059669; font-weight: bold; font-size: 18px;">${formatCurrency(payload.total_amount)}</span></p>
            <p><strong>Additional Services:</strong> ${formatAdditionalServices(payload.additional_services)}</p>
            <p><strong>Maintenance Plan:</strong> ${payload.maintenance_plan || 'None'}</p>
          </div>

          <div style="background-color: #fef3c7; padding: 20px; border-radius: 8px; margin-bottom: 20px;">
            <h2 style="color: #92400e; margin-top: 0;">📅 Submission Details</h2>
            <p><strong>Submitted:</strong> ${formatDate(payload.created_at)}</p>
          </div>

          <div style="background-color: #ecfdf5; padding: 20px; border-radius: 8px; text-align: center;">
            <h3 style="color: #065f46; margin-top: 0;">Next Steps</h3>
            <p style="margin-bottom: 15px;">1. Review the project request in your CMS dashboard</p>
            <p style="margin-bottom: 15px;">2. Contact the client to discuss project details</p>
            <p style="margin-bottom: 0;">3. Generate an access token for the client to complete the questionnaire</p>
          </div>

          <div style="text-align: center; margin-top: 30px; padding-top: 20px; border-top: 1px solid #e5e7eb;">
            <p style="color: #6b7280; font-size: 14px; margin: 0;">
              This notification was sent automatically from your Next.js Portfolio website.
            </p>
          </div>
        </div>
      </div>
    `;

    // Send email using Resend
    const res = await fetch("https://api.resend.com/emails", {
      method: "POST",
      headers: {
        "Authorization": `Bearer ${RESEND_API_KEY}`,
        "Content-Type": "application/json",
      },
      body: JSON.stringify({
        from: "Portfolio Notifications <<EMAIL>>",
        to: RECIPIENT_EMAIL,
        subject: `🚀 New Project Request: ${formatPackageType(payload.package_type)} - ${payload.client_name}`,
        html: emailHtml,
      }),
    });

    const data = await res.json();

    if (res.status >= 400) {
      console.error("Error sending project request notification:", data);
      return new Response(
        JSON.stringify({
          success: false,
          error: "Failed to send email notification",
        }),
        {
          headers: { ...corsHeaders, "Content-Type": "application/json" },
          status: 500,
        }
      );
    }

    return new Response(
      JSON.stringify({
        success: true,
        data,
      }),
      {
        headers: { ...corsHeaders, "Content-Type": "application/json" },
        status: 200,
      }
    );
  } catch (error) {
    console.error("Error in send-project-request-notification function:", error);
    return new Response(
      JSON.stringify({
        success: false,
        error: "Internal server error",
      }),
      {
        headers: { ...corsHeaders, "Content-Type": "application/json" },
        status: 500,
      }
    );
  }
});
