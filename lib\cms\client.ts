import { PortfolioContent, PortfolioImage, ContentSection, ContentKey, ImageType } from './types';

// Content fetching functions
export async function getContent(section: ContentSection, key: ContentKey): Promise<string> {
  try {
    const res = await fetch(`/api/public/content?section=${section}&key=${key}`);

    if (res.ok) {
      const data: PortfolioContent = await res.json();
      return data.content;
    }

    // Return default values if content not found
    return getDefaultContent(section, key);
  } catch (error) {
    console.error(`Error fetching content for ${section}.${key}:`, error);
    return getDefaultContent(section, key);
  }
}

export async function getSectionContent(section: ContentSection): Promise<Record<string, string>> {
  try {
    const res = await fetch(`/api/public/content?section=${section}`);

    if (!res.ok) {
      throw new Error(`Failed to fetch ${section} content`);
    }

    const data: PortfolioContent[] = await res.json();

    // Convert to a key-value object
    const contentMap: Record<string, string> = {};
    data.forEach(item => {
      contentMap[item.key] = item.content;
    });

    return contentMap;
  } catch (error) {
    console.error(`Error fetching content for section ${section}:`, error);
    return {};
  }
}

// Image fetching functions
export async function getLatestImageByType(type: ImageType): Promise<PortfolioImage | null> {
  try {
    // Add a cache-busting parameter to avoid stale responses
    const cacheBuster = new Date().getTime();
    const res = await fetch(`/api/public/images?type=${type}&_=${cacheBuster}`, {
      // Add cache control headers
      cache: 'no-store',
      headers: {
        'Cache-Control': 'no-cache, no-store, must-revalidate',
        'Pragma': 'no-cache',
        'Expires': '0'
      }
    });

    // If the response is not OK but it's a 404, just return null instead of throwing
    if (!res.ok) {
      if (res.status === 404) {
        console.log(`No images found for type: ${type}`);
        return null;
      }
      console.warn(`Non-OK response fetching ${type} images: ${res.status} ${res.statusText}`);
      // Don't throw, just return null
      return null;
    }

    const data: PortfolioImage[] = await res.json();

    // Return the most recent image (they're already sorted by created_at desc)
    return data.length > 0 ? data[0] : null;
  } catch (error) {
    console.error(`Error fetching ${type} image:`, error);
    // Return null instead of throwing
    return null;
  }
}

export async function getImageByName(name: string): Promise<PortfolioImage | null> {
  try {
    // Use the dedicated by-name endpoint
    const res = await fetch(`/api/public/images/by-name?name=${encodeURIComponent(name)}`);

    if (!res.ok) {
      throw new Error(`Failed to fetch image with name ${name}`);
    }

    const data = await res.json();

    // The endpoint returns either a single image object or null
    return data || null;
  } catch (error) {
    console.error(`Error fetching image with name ${name}:`, error);
    return null;
  }
}

export async function getImagesByType(type: ImageType): Promise<PortfolioImage[]> {
  try {
    const res = await fetch(`/api/public/images?type=${type}`);

    if (!res.ok) {
      throw new Error(`Failed to fetch ${type} images`);
    }

    return await res.json();
  } catch (error) {
    console.error(`Error fetching ${type} images:`, error);
    return [];
  }
}

// Default content values
export function getDefaultContent(section: ContentSection, key: ContentKey): string {
  const defaults: Record<string, Record<string, string>> = {
    hero: {
      title: "Hi, I'm Zachary Odero",
      subtitle: "Full Stack Developer",
      description: "I build modern web applications with React, Next.js, and Node.js."
    },
    about: {
      bio: "I'm a passionate developer with experience in building web applications using modern technologies.",
      education: "Bachelor's Degree in Computer Science",
      experience: "3+ years of experience in web development"
    },
    projects: {
      project_intro: "Here are some of my recent projects:",
      project_1_title: "E-commerce Platform",
      project_1_description: "A full-featured e-commerce platform with product management, cart functionality, and payment processing.",
      project_1_tags: "Next.js,Stripe,Tailwind CSS",
      project_1_github: "https://github.com/zacharyodero/ecommerce-platform",
      project_1_demo: "https://ecommerce-platform-demo.vercel.app",
      project_2_title: "Task Management App",
      project_2_description: "A productivity application with real-time updates and team collaboration features.",
      project_2_tags: "React,Firebase,Material UI",
      project_2_github: "https://github.com/zacharyodero/task-management-app",
      project_2_demo: "https://task-management-app-demo.vercel.app",
      project_3_title: "Portfolio Website",
      project_3_description: "A minimalist portfolio website showcasing my work and skills.",
      project_3_tags: "Next.js,Tailwind CSS,Framer Motion",
      project_3_github: "https://github.com/zacharyodero/my-nextjs-portfolio",
      project_3_demo: "https://zacharyodero.com"
    },
    skills: {
      skills_intro: "My technical skills include:",
      frontend_skills: "React,Next.js,TypeScript,HTML,CSS,Tailwind CSS,Framer Motion",
      backend_skills: "Node.js,Express,Supabase,Firebase,PostgreSQL,MongoDB",
      other_skills: "Git,GitHub,Vercel,AWS,Docker,Jest"
    },
    contact: {
      contact_intro: "Let's connect! Feel free to reach out to me through any of the following channels:",
      email: "<EMAIL>",
      linkedin: "www.linkedin.com/in/zacharyodero",
      github: "https://github.com/ZacharyOdero"
    },
    general: {
      site_title: "Zachary Odero - Portfolio",
      site_description: "Personal portfolio website of Zachary Odero",
      meta_keywords: "web development, full stack, react, next.js, portfolio"
    }
  };

  return defaults[section]?.[key] || "";
}
