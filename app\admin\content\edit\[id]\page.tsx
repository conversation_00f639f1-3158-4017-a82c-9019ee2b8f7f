"use client";

import { useEffect, useState } from "react";
import { useRouter } from "next/navigation";
import { toast } from "sonner";
import { useSession } from "next-auth/react";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { PortfolioContent } from "@/lib/cms/types";
import { use } from "react";

export default function EditContentPage({
  params,
}: {
  params: { id: string };
}) {
  const router = useRouter();
  const { data: session, status } = useSession();
  const resolvedParams = use(params);
  const id = resolvedParams.id;

  const [content, setContent] = useState<PortfolioContent | null>(null);
  const [contentValue, setContentValue] = useState("");
  const [isLoading, setIsLoading] = useState(true);
  const [isSubmitting, setIsSubmitting] = useState(false);

  useEffect(() => {
    // Don't fetch if not authenticated
    if (status === "unauthenticated") {
      router.push("/admin/login");
      return;
    }

    // Wait for authentication to complete
    if (status === "loading") {
      return;
    }

    const fetchContent = async () => {
      try {
        const res = await fetch(`/api/cms/content/${id}`);

        if (!res.ok) {
          if (res.status === 401) {
            toast.error("You must be logged in to view this content");
            router.push("/admin/login");
            return;
          }
          if (res.status === 404) {
            toast.error("Content not found");
            router.push("/admin/content");
            return;
          }
          throw new Error("Failed to fetch content");
        }

        const data = await res.json();
        setContent(data);
        setContentValue(data.content);
      } catch (error) {
        console.error("Error fetching content:", error);
        toast.error("Failed to load content");
      } finally {
        setIsLoading(false);
      }
    };

    fetchContent();
  }, [id, router, status]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    // Check if user is authenticated
    if (status !== "authenticated") {
      toast.error("You must be logged in to update content");
      router.push("/admin/login");
      return;
    }

    if (!contentValue) {
      toast.error("Content cannot be empty");
      return;
    }

    setIsSubmitting(true);

    try {
      const res = await fetch(`/api/cms/content/${id}`, {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          content: contentValue,
        }),
      });

      if (!res.ok) {
        if (res.status === 401) {
          toast.error("You must be logged in to update content");
          router.push("/admin/login");
          return;
        }

        const error = await res.json();
        throw new Error(error.error || "Failed to update content");
      }

      toast.success("Content updated successfully");
      router.push("/admin/content");
    } catch (error: any) {
      console.error("Error updating content:", error);
      toast.error(error.message || "Failed to update content");
    } finally {
      setIsSubmitting(false);
    }
  };

  if (isLoading) {
    return (
      <div className="flex justify-center py-8">
        <div className="h-8 w-8 animate-spin rounded-full border-b-2 border-t-2 border-blue-500"></div>
      </div>
    );
  }

  if (!content) {
    return (
      <div className="flex flex-col items-center justify-center py-8 text-center">
        <p className="mb-4 text-gray-500">Content not found</p>
        <Button variant="outline" onClick={() => router.push("/admin/content")}>
          Back to Content
        </Button>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-3xl font-bold">Edit Content</h1>
        <Button variant="outline" onClick={() => router.back()}>
          Cancel
        </Button>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>
            Editing: {content.section} - {content.key}
          </CardTitle>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleSubmit} className="space-y-6">
            <div className="space-y-2">
              <Label htmlFor="content">Content</Label>
              <Textarea
                id="content"
                value={contentValue}
                onChange={(e) => setContentValue(e.target.value)}
                rows={10}
                placeholder="Enter content here..."
              />
            </div>

            <div className="flex space-x-2">
              <Button type="submit" disabled={isSubmitting}>
                {isSubmitting ? "Saving..." : "Save Changes"}
              </Button>
              <Button
                type="button"
                variant="outline"
                onClick={() => router.push("/admin/content")}
              >
                Cancel
              </Button>
            </div>
          </form>
        </CardContent>
      </Card>
    </div>
  );
}
