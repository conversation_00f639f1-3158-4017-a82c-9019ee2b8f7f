/**
 * Questionnaire Test Script
 *
 * This script helps test the questionnaire functionality directly,
 * bypassing the payment verification process.
 */

// =============================================
// Direct Questionnaire Access
// =============================================

// Sample questionnaire tokens for testing
// Note: These are example tokens and may not work in your system
const sampleTokens = [
  '7li0nn6j2zcunaxbhd36blyj21pjm50p' // Valid token found in the database
];

// Function to navigate to questionnaire with a token
function goToQuestionnaire(token) {
  if (!token) {
    console.error('No token provided');
    return;
  }

  const url = `/questionnaire/${token}`;
  console.log(`Navigating to questionnaire with URL: ${url}`);

  // Open in the same window
  window.location.href = url;
}

// Function to extract token from current URL
function getTokenFromUrl() {
  const path = window.location.pathname;
  const matches = path.match(/\/questionnaire\/([a-zA-Z0-9]+)/);

  if (matches && matches[1]) {
    return matches[1];
  }

  return null;
}

// =============================================
// Questionnaire Form Testing
// =============================================

// Test data for general information form
const testGeneralInfo = {
  project_name: 'Test Project',
  project_description: 'This is a test project for testing the questionnaire functionality.',
  target_audience: 'Test users and developers',
  project_goals: ['online_presence', 'lead_generation', 'brand_awareness'],
  color_scheme: 'modern',
  has_logo_or_brand_guidelines: true,
  preferred_launch_timeline: '1-2_months'
};

// Test data for basic package form
const testBasicPackage = {
  requested_pages: ['Home', 'About', 'Services', 'Contact', 'Blog'],
  has_content_ready: true,
  contact_form_fields: ['name', 'email', 'phone', 'message'],
  has_domain_name: true,
  preferred_hosting: 'vercel'
};

// Function to fill general information form
function fillGeneralInfoForm() {
  console.log('Filling general information form...');

  // Project name
  const projectNameInput = document.querySelector('input[name="project_name"]');
  if (projectNameInput) {
    projectNameInput.value = testGeneralInfo.project_name;
    projectNameInput.dispatchEvent(new Event('input', { bubbles: true }));
    console.log(`Filled project name: ${testGeneralInfo.project_name}`);
  } else {
    console.error('Project name input not found');
  }

  // Project description
  const projectDescInput = document.querySelector('textarea[name="project_description"]');
  if (projectDescInput) {
    projectDescInput.value = testGeneralInfo.project_description;
    projectDescInput.dispatchEvent(new Event('input', { bubbles: true }));
    console.log(`Filled project description`);
  } else {
    console.error('Project description input not found');
  }

  // Target audience
  const targetAudienceInput = document.querySelector('textarea[name="target_audience"]');
  if (targetAudienceInput) {
    targetAudienceInput.value = testGeneralInfo.target_audience;
    targetAudienceInput.dispatchEvent(new Event('input', { bubbles: true }));
    console.log(`Filled target audience`);
  } else {
    console.error('Target audience input not found');
  }

  // Project goals (checkboxes)
  testGeneralInfo.project_goals.forEach(goal => {
    const goalCheckbox = document.querySelector(`input[value="${goal}"]`);
    if (goalCheckbox && !goalCheckbox.checked) {
      goalCheckbox.click();
      console.log(`Selected project goal: ${goal}`);
    }
  });

  // Color scheme (radio buttons)
  const colorSchemeRadio = document.querySelector(`input[value="${testGeneralInfo.color_scheme}"]`);
  if (colorSchemeRadio) {
    colorSchemeRadio.click();
    console.log(`Selected color scheme: ${testGeneralInfo.color_scheme}`);
  }

  // Logo checkbox
  const logoCheckbox = document.querySelector('input[name="has_logo_or_brand_guidelines"]');
  if (logoCheckbox) {
    if (logoCheckbox.checked !== testGeneralInfo.has_logo_or_brand_guidelines) {
      logoCheckbox.click();
    }
    console.log(`Set has logo: ${testGeneralInfo.has_logo_or_brand_guidelines}`);
  }

  // Timeline (radio buttons)
  const timelineRadio = document.querySelector(`input[value="${testGeneralInfo.preferred_launch_timeline}"]`);
  if (timelineRadio) {
    timelineRadio.click();
    console.log(`Selected timeline: ${testGeneralInfo.preferred_launch_timeline}`);
  }

  console.log('General information form filled. Click "Continue" to proceed.');
}

// Function to fill basic package form
function fillBasicPackageForm() {
  console.log('Filling basic package form...');

  // Requested pages
  // This depends on how your form is implemented
  // If it's a list of text inputs:
  const pageInputs = document.querySelectorAll('input[placeholder*="Page"]');
  if (pageInputs.length > 0) {
    pageInputs.forEach((input, index) => {
      if (index < testBasicPackage.requested_pages.length) {
        input.value = testBasicPackage.requested_pages[index];
        input.dispatchEvent(new Event('input', { bubbles: true }));
        console.log(`Filled page ${index + 1}: ${testBasicPackage.requested_pages[index]}`);
      }
    });
  } else {
    console.error('Page inputs not found');
  }

  // Has content ready checkbox
  const contentReadyCheckbox = document.querySelector('input[name="has_content_ready"]');
  if (contentReadyCheckbox) {
    if (contentReadyCheckbox.checked !== testBasicPackage.has_content_ready) {
      contentReadyCheckbox.click();
    }
    console.log(`Set has content ready: ${testBasicPackage.has_content_ready}`);
  }

  // Contact form fields (checkboxes)
  testBasicPackage.contact_form_fields.forEach(field => {
    const fieldCheckbox = document.querySelector(`input[value="${field}"]`);
    if (fieldCheckbox && !fieldCheckbox.checked) {
      fieldCheckbox.click();
      console.log(`Selected contact form field: ${field}`);
    }
  });

  // Has domain name checkbox
  const domainCheckbox = document.querySelector('input[name="has_domain_name"]');
  if (domainCheckbox) {
    if (domainCheckbox.checked !== testBasicPackage.has_domain_name) {
      domainCheckbox.click();
    }
    console.log(`Set has domain name: ${testBasicPackage.has_domain_name}`);
  }

  // Preferred hosting (radio buttons)
  const hostingRadio = document.querySelector(`input[value="${testBasicPackage.preferred_hosting}"]`);
  if (hostingRadio) {
    hostingRadio.click();
    console.log(`Selected preferred hosting: ${testBasicPackage.preferred_hosting}`);
  }

  console.log('Basic package form filled. Click "Continue" to proceed.');
}

// Function to test the questionnaire flow
function testQuestionnaireFlow() {
  console.log('=== TESTING QUESTIONNAIRE FLOW ===');

  // Check if we're on the questionnaire page
  const isQuestionnairePage = window.location.pathname.includes('/questionnaire/');
  if (!isQuestionnairePage) {
    console.error('Not on questionnaire page. Please navigate to a questionnaire page first.');
    console.log('You can use goToQuestionnaire(token) to navigate to a questionnaire page.');
    return;
  }

  // Get the current step
  const generalInfoForm = document.querySelector('input[name="project_name"]');
  const basicPackageForm = document.querySelector('input[name="has_content_ready"]');
  const completePage = document.querySelector('.text-green-500'); // Success checkmark

  if (generalInfoForm) {
    console.log('Currently on General Information form');
    fillGeneralInfoForm();

    console.log('\nAfter clicking "Continue" on the general information form, run:');
    console.log('fillBasicPackageForm()');
  } else if (basicPackageForm) {
    console.log('Currently on Basic Package form');
    fillBasicPackageForm();
  } else if (completePage) {
    console.log('Questionnaire completed successfully!');
  } else {
    console.log('Could not determine current questionnaire step');
    console.log('Please check if the questionnaire is loaded correctly');
  }
}

// Function to check questionnaire token validity
function checkTokenValidity(token) {
  console.log(`Checking validity of token: ${token}`);

  // Look for error messages on the page
  const errorTitle = document.querySelector('.text-destructive');
  if (errorTitle && errorTitle.textContent.includes('Invalid Access Token')) {
    console.error('Token is invalid or expired');
    return false;
  }

  // Look for the questionnaire form
  const questionnaireForm = document.querySelector('form');
  if (questionnaireForm) {
    console.log('Token appears to be valid - questionnaire form is loaded');
    return true;
  }

  // Look for loading indicator
  const loadingIndicator = document.querySelector('.animate-spin');
  if (loadingIndicator) {
    console.log('Questionnaire is still loading...');
    return 'loading';
  }

  console.log('Could not determine token validity');
  return 'unknown';
}

// Function to use the first valid token from our sample
function useValidToken() {
  if (sampleTokens.length > 0) {
    const token = sampleTokens[0];
    console.log(`Using valid token: ${token}`);
    goToQuestionnaire(token);
  } else {
    console.error('No valid tokens found in sampleTokens array');
  }
}

// =============================================
// Usage Instructions
// =============================================

console.log('=== QUESTIONNAIRE TEST SCRIPT LOADED ===');
console.log('To test the questionnaire functionality:');
console.log('1. Use the valid token we found in the database:');
console.log('   useValidToken()');
console.log('2. Or if you have a different token, navigate to the questionnaire:');
console.log('   goToQuestionnaire("your-token-here")');
console.log('3. Once on the questionnaire page, test the flow:');
console.log('   testQuestionnaireFlow()');
console.log('4. To check if the current token is valid:');
console.log('   checkTokenValidity(getTokenFromUrl())');
console.log('5. To fill individual forms:');
console.log('   fillGeneralInfoForm()');
console.log('   fillBasicPackageForm()');
