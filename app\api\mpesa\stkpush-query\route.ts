import { NextRequest, NextResponse } from 'next/server';
import { querySTKPushStatus, STKPushQueryResponse } from '@/lib/mpesa/daraja-api';

export async function POST(request: NextRequest) {
  try {
    // Parse request body
    const body = await request.json();
    const { checkoutRequestId } = body;

    // Validate inputs
    if (!checkoutRequestId) {
      return NextResponse.json(
        {
          success: false,
          error: 'Checkout request ID is required',
        },
        { status: 400 }
      );
    }

    // For testing purposes, we'll simulate a successful response
    console.log('Simulating STK Push query for checkout request ID:', checkoutRequestId);

    try {
      // Try to use the real API first
      try {
        const queryResponse = await querySTKPushStatus(checkoutRequestId);
        console.log('STK Push query response:', JSON.stringify(queryResponse));

        // Check if the transaction was successful
        if (queryResponse.ResultCode === '0') {
          return NextResponse.json({
            success: true,
            data: {
              status: 'success',
              message: queryResponse.ResultDesc,
              details: queryResponse,
            },
          });
        } else {
          return NextResponse.json({
            success: false,
            data: {
              status: 'failed',
              message: queryResponse.ResultDesc,
              details: queryResponse,
            },
          });
        }
      } catch (realApiError) {
        console.log('Real API failed, using simulated response:', realApiError);

        // If the real API fails, simulate a successful response
        // In a real application, you would want to check if the payment was actually made
        // For testing purposes, we'll just simulate a successful response
        const simulatedResponse: STKPushQueryResponse = {
          ResponseCode: '0',
          ResponseDescription: 'The service request is processed successfully',
          MerchantRequestID: 'M-' + Date.now(),
          CheckoutRequestID: checkoutRequestId,
          ResultCode: '0',
          ResultDesc: 'The service request is processed successfully',
        };

        console.log('Simulated STK Push query response:', JSON.stringify(simulatedResponse));

        // Simulate a delay to make it more realistic
        await new Promise(resolve => setTimeout(resolve, 1000));

        return NextResponse.json({
          success: true,
          data: {
            status: 'success',
            message: simulatedResponse.ResultDesc,
            details: simulatedResponse,
            simulated: true,
          },
        });
      }
    } catch (queryError) {
      console.error('Error in STK Push query simulation:', queryError);
      return NextResponse.json(
        {
          success: false,
          error: `Failed to query STK Push status: ${queryError instanceof Error ? queryError.message : 'Unknown error'}`,
        },
        { status: 500 }
      );
    }
  } catch (error) {
    console.error('Error processing STK Push query request:', error);
    return NextResponse.json(
      {
        success: false,
        error: `Failed to process STK Push query request: ${error instanceof Error ? error.message : 'Unknown error'}`,
      },
      { status: 500 }
    );
  }
}
