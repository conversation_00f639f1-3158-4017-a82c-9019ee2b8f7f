'use client';

import { useState, useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { motion } from 'framer-motion';
import Link from 'next/link';
import { PackageSelectionFormData, packageSelectionSchema, ADDITIONAL_SERVICES, MAINTENANCE_PLANS, PACKAGE_PRICES, calculateTotalPrice } from '@/lib/mpesa/types';
import { Button } from '@/components/ui/button';
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { Checkbox } from '@/components/ui/checkbox';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Check, Info } from 'lucide-react';
import { cn } from '@/lib/utils';
import { Toolt<PERSON>, Too<PERSON><PERSON><PERSON>ontent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';

interface PackageSelectionFormProps {
  onSubmit: (data: PackageSelectionFormData) => void;
}

export default function PackageSelectionForm({ onSubmit }: PackageSelectionFormProps) {
  const [totalPrice, setTotalPrice] = useState<number>(PACKAGE_PRICES.basic);
  const [depositAmount, setDepositAmount] = useState<number>(Math.round(PACKAGE_PRICES.basic * 0.4));

  // Initialize form with react-hook-form and zod validation
  const form = useForm<PackageSelectionFormData>({
    resolver: zodResolver(packageSelectionSchema),
    defaultValues: {
      package_type: 'basic',
      additional_services: [], // Initialize as empty array
      maintenance_plan: null,  // Initialize as null instead of undefined
    },
  });

  // Watch form values to calculate total price
  const watchPackageType = form.watch('package_type');
  const watchAdditionalServices = form.watch('additional_services');
  const watchMaintenancePlan = form.watch('maintenance_plan');

  // Update total price when form values change
  useEffect(() => {
    const total = calculateTotalPrice(
      watchPackageType,
      watchAdditionalServices,
      watchMaintenancePlan
    );
    setTotalPrice(total);
    setDepositAmount(Math.round(total * 0.4));
  }, [watchPackageType, watchAdditionalServices, watchMaintenancePlan]);

  // Handle form submission
  const handleSubmit = (data: PackageSelectionFormData) => {
    onSubmit(data);
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
      className="w-full max-w-4xl mx-auto"
    >
      <Form {...form}>
        <form onSubmit={form.handleSubmit(handleSubmit)} className="mobile-spacing">
          <div className="mobile-spacing">
            <h2 className="text-2xl font-bold text-center mb-4 sm:mb-6">Select Your Package</h2>

            {/* Web Features Guide Link */}
            <div className="mb-6 p-4 bg-blue-50 dark:bg-blue-950/20 border border-blue-200 dark:border-blue-800 rounded-lg">
              <div className="flex flex-col sm:flex-row items-center justify-between gap-4">
                <div className="text-center sm:text-left">
                  <h3 className="font-semibold text-blue-900 dark:text-blue-100 mb-1">
                    New to web development features?
                  </h3>
                  <p className="text-sm text-blue-700 dark:text-blue-300">
                    Learn about website features and their business benefits to make informed decisions.
                  </p>
                </div>
                <Link href="/web-features" className="flex-shrink-0">
                  <Button variant="outline" size="sm" className="gap-2 border-blue-300 text-blue-700 hover:bg-blue-100 dark:border-blue-700 dark:text-blue-300 dark:hover:bg-blue-900/50">
                    <Info className="h-4 w-4" />
                    Learn About Features
                  </Button>
                </Link>
              </div>
            </div>

            <FormField
              control={form.control}
              name="package_type"
              render={({ field }) => (
                <FormItem className="mobile-spacing">
                  <FormLabel className="text-lg font-semibold">Choose a Package</FormLabel>
                  <FormControl>
                    <div className="grid grid-cols-1 gap-6 sm:gap-4 md:grid-cols-3">
                      <PackageCard
                        id="basic"
                        title="Basic Package"
                        price={PACKAGE_PRICES.basic}
                        description="Perfect for small businesses or personal websites"
                        features={[
                          "Static 5-page Next.js website",
                          "Responsive design",
                          "SEO optimization basics",
                          "Contact form integration",
                          "Basic CMS setup",
                          "Deployment to Vercel or Netlify",
                          "1 round of revisions",
                          "Delivery: 2-3 weeks"
                        ]}
                        selected={field.value === 'basic'}
                        onChange={() => {
                          // Toggle selection - if already selected, do nothing (required field)
                          if (field.value !== 'basic') {
                            field.onChange('basic');

                            // Immediately update price for better responsiveness
                            const total = calculateTotalPrice(
                              'basic',
                              watchAdditionalServices,
                              watchMaintenancePlan
                            );
                            setTotalPrice(total);
                            setDepositAmount(Math.round(total * 0.4));
                          }
                        }}
                      />

                      <PackageCard
                        id="standard"
                        title="Standard Package"
                        price={PACKAGE_PRICES.standard}
                        description="Ideal for growing businesses with more content needs"
                        features={[
                          "Dynamic Next.js website (up to 8 pages)",
                          "Responsive design across all devices",
                          "Full SEO optimization",
                          "Blog integration with SSG",
                          "CMS integration (Contentful or Sanity)",
                          "User authentication (NextAuth.js)",
                          "Custom styling with Tailwind CSS",
                          "2 rounds of revisions",
                          "Delivery: 3-5 weeks"
                        ]}
                        selected={field.value === 'standard'}
                        onChange={() => {
                          // Toggle selection - if already selected, do nothing (required field)
                          if (field.value !== 'standard') {
                            field.onChange('standard');

                            // Immediately update price for better responsiveness
                            const total = calculateTotalPrice(
                              'standard',
                              watchAdditionalServices,
                              watchMaintenancePlan
                            );
                            setTotalPrice(total);
                            setDepositAmount(Math.round(total * 0.4));
                          }
                        }}
                      />

                      <PackageCard
                        id="premium"
                        title="Premium Package"
                        price={PACKAGE_PRICES.premium}
                        description="Complete solution for businesses requiring advanced functionality"
                        features={[
                          "Advanced Next.js application (up to 12 pages)",
                          "E-commerce functionality with payment integration",
                          "User authentication and basic dashboard",
                          "Database integration (MongoDB)",
                          "Up to 2 third-party API integrations",
                          "Custom admin panel",
                          "Performance optimization",
                          "3 rounds of revisions",
                          "Delivery: 6-8 weeks"
                        ]}
                        selected={field.value === 'premium'}
                        onChange={() => {
                          // Toggle selection - if already selected, do nothing (required field)
                          if (field.value !== 'premium') {
                            field.onChange('premium');

                            // Immediately update price for better responsiveness
                            const total = calculateTotalPrice(
                              'premium',
                              watchAdditionalServices,
                              watchMaintenancePlan
                            );
                            setTotalPrice(total);
                            setDepositAmount(Math.round(total * 0.4));
                          }
                        }}
                      />
                    </div>
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="additional_services"
              render={({ field }) => (
                <FormItem className="space-y-4 mt-8">
                  <FormLabel className="text-lg font-semibold">Additional Services (Optional)</FormLabel>
                  <FormControl>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      {ADDITIONAL_SERVICES.map((service) => (
                        <div
                          key={service.id}
                          className={cn(
                            "flex items-start space-x-3 p-4 rounded-lg border",
                            field.value?.includes(service.id)
                              ? "border-primary bg-primary/5"
                              : "border-border"
                          )}
                        >
                          <Checkbox
                            id={`service-${service.id}`}
                            checked={field.value?.includes(service.id)}
                            onCheckedChange={(checked) => {
                              const updatedValue = checked
                                ? [...(field.value || []), service.id]
                                : (field.value || []).filter((value) => value !== service.id);

                              field.onChange(updatedValue);

                              const total = calculateTotalPrice(
                                watchPackageType,
                                updatedValue,
                                watchMaintenancePlan
                              );
                              setTotalPrice(total);
                              setDepositAmount(Math.round(total * 0.4));
                            }}
                          />
                          <div className="space-y-1">
                            <label
                              htmlFor={`service-${service.id}`}
                              className="font-medium cursor-pointer"
                            >
                              {service.name} - KSh {service.price.toLocaleString()}
                            </label>
                            <p className="text-sm text-muted-foreground">
                              {service.description}
                            </p>
                          </div>
                        </div>
                      ))}
                    </div>
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="maintenance_plan"
              render={({ field }) => (
                <FormItem className="space-y-4 mt-8">
                  <FormLabel className="text-lg font-semibold">Maintenance Plan (Optional)</FormLabel>
                  <FormControl>
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                      <Card
                        className={cn(
                          "cursor-pointer hover:border-primary transition-colors",
                          !field.value && "border-primary bg-primary/5"
                        )}
                        onClick={() => {
                          // Toggle selection - if already null, do nothing
                          if (field.value !== null) {
                            field.onChange(null);

                            // Immediately update price for better responsiveness
                            const total = calculateTotalPrice(
                              watchPackageType,
                              watchAdditionalServices,
                              null
                            );
                            setTotalPrice(total);
                            setDepositAmount(Math.round(total * 0.4));
                          }
                        }}
                      >
                        <CardHeader>
                          <CardTitle>No Maintenance</CardTitle>
                          <CardDescription>KSh 0/month</CardDescription>
                        </CardHeader>
                        <CardContent>
                          <p className="text-sm">No monthly maintenance plan</p>
                        </CardContent>
                        <CardFooter>
                          <Button
                            type="button"
                            variant={!field.value ? "default" : "outline"}
                            className="w-full"
                            onClick={(e) => {
                              e.stopPropagation(); // Prevent double triggering with card click
                              if (field.value !== undefined) {
                                field.onChange(undefined);

                                // Immediately update price for better responsiveness
                                const total = calculateTotalPrice(
                                  watchPackageType,
                                  watchAdditionalServices,
                                  undefined
                                );
                                setTotalPrice(total);
                                setDepositAmount(Math.round(total * 0.4));
                              }
                            }}
                          >
                            {!field.value && <Check className="mr-2 h-4 w-4" />}
                            {!field.value ? 'Selected' : 'Select'}
                          </Button>
                        </CardFooter>
                      </Card>

                      {MAINTENANCE_PLANS.map((plan) => (
                        <Card
                          key={plan.id}
                          className={cn(
                            "cursor-pointer hover:border-primary transition-colors",
                            field.value === plan.id && "border-primary bg-primary/5"
                          )}
                          onClick={() => {
                            // Toggle selection - if already selected, set to null
                            if (field.value === plan.id) {
                              field.onChange(null);

                              // Immediately update price for better responsiveness
                              const total = calculateTotalPrice(
                                watchPackageType,
                                watchAdditionalServices,
                                null
                              );
                              setTotalPrice(total);
                              setDepositAmount(Math.round(total * 0.4));
                            } else {
                              field.onChange(plan.id);

                              // Immediately update price for better responsiveness
                              const total = calculateTotalPrice(
                                watchPackageType,
                                watchAdditionalServices,
                                plan.id
                              );
                              setTotalPrice(total);
                              setDepositAmount(Math.round(total * 0.4));
                            }
                          }}
                        >
                          <CardHeader>
                            <CardTitle>{plan.name} Maintenance</CardTitle>
                            <CardDescription>KSh {plan.price.toLocaleString()}/month</CardDescription>
                          </CardHeader>
                          <CardContent>
                            <p className="text-sm">{plan.description}</p>
                          </CardContent>
                          <CardFooter>
                            <Button
                              type="button"
                              variant={field.value === plan.id ? "default" : "outline"}
                              className="w-full"
                              onClick={(e) => {
                                e.stopPropagation(); // Prevent double triggering with card click
                                if (field.value === plan.id) {
                                  field.onChange(undefined);

                                  // Immediately update price for better responsiveness
                                  const total = calculateTotalPrice(
                                    watchPackageType,
                                    watchAdditionalServices,
                                    undefined
                                  );
                                  setTotalPrice(total);
                                  setDepositAmount(Math.round(total * 0.4));
                                } else {
                                  field.onChange(plan.id);

                                  // Immediately update price for better responsiveness
                                  const total = calculateTotalPrice(
                                    watchPackageType,
                                    watchAdditionalServices,
                                    plan.id
                                  );
                                  setTotalPrice(total);
                                  setDepositAmount(Math.round(total * 0.4));
                                }
                              }}
                            >
                              {field.value === plan.id && <Check className="mr-2 h-4 w-4" />}
                              {field.value === plan.id ? 'Selected' : 'Select'}
                            </Button>
                          </CardFooter>
                        </Card>
                      ))}
                    </div>
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>

          <div className="bg-muted p-6 rounded-lg">
            <div className="flex justify-between items-center mb-2">
              <span className="font-medium">Total Project Cost:</span>
              <span className="font-bold text-lg">KSh {totalPrice.toLocaleString()}</span>
            </div>
            <div className="flex justify-between items-center mb-4">
              <div className="flex items-center">
                <span className="font-medium">Required Deposit (40%):</span>
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <Info className="h-4 w-4 ml-1 text-muted-foreground cursor-help" />
                    </TooltipTrigger>
                    <TooltipContent>
                      <p className="max-w-xs">
                        A 40% deposit is required to begin work on your project.
                        The remaining 60% will be due in two installments:
                        30% at project midpoint and 30% upon completion.
                      </p>
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>
              </div>
              <span className="font-bold text-lg text-primary">KSh {depositAmount.toLocaleString()}</span>
            </div>
            <Button type="submit" className="w-full">Continue to Client Information</Button>
          </div>
        </form>
      </Form>
    </motion.div>
  );
}

interface PackageCardProps {
  id: string;
  title: string;
  price: number;
  description: string;
  features: string[];
  selected: boolean;
  onChange: () => void;
}

function PackageCard({ id, title, price, description, features, selected, onChange }: PackageCardProps) {
  return (
    <Card
      className={cn(
        "cursor-pointer hover:border-primary transition-colors mobile-card",
        selected && "border-primary bg-primary/5"
      )}
      onClick={onChange}
    >
      <CardHeader className="pb-3 sm:pb-6">
        <CardTitle className="text-xl sm:text-2xl">{title}</CardTitle>
        <CardDescription className="text-base sm:text-lg font-medium">
          KSh {price.toLocaleString()}
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4 pb-4">
        <p className="text-sm">{description}</p>
        <ul className="space-y-2">
          {features.map((feature, index) => (
            <li key={index} className="flex items-start">
              <Check className="h-4 w-4 mr-2 mt-1 text-primary flex-shrink-0" />
              <span className="text-sm">{feature}</span>
            </li>
          ))}
        </ul>
      </CardContent>
      <CardFooter>
        <Button
          type="button"
          variant={selected ? "default" : "outline"}
          className="w-full mobile-touch-target h-12"
          onClick={(e) => {
            e.stopPropagation(); // Prevent double triggering with card click
            onChange();
          }}
        >
          {selected && <Check className="mr-2 h-5 w-5" />}
          {selected ? 'Selected' : 'Select'}
        </Button>
      </CardFooter>
    </Card>
  );
}
