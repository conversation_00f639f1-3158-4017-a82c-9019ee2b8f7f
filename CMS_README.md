# Portfolio Headless CMS

This is a headless CMS built for managing the content of your portfolio website. It allows you to update your profile information, projects, skills, and other content without modifying the code.

## Features

- **Content Management**: Edit text content for all sections of your portfolio
- **Image Management**: Upload and manage images, including your profile picture
- **Admin Dashboard**: Secure admin interface for content management
- **Responsive Design**: Works on desktop and mobile devices

## Setup Instructions

### 1. Database Setup

The CMS uses Supabase as the backend. Make sure you have a Supabase project set up with the following tables:

- `portfolio_content`: Stores text content
- `portfolio_images`: Stores image metadata

You also need to set up Supabase Storage with a bucket named `portfolio-images` for storing image files.

### 2. Environment Variables

Create a `.env.local` file in the root directory with the following variables:

```
NEXT_PUBLIC_SUPABASE_URL=your-supabase-url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your-supabase-anon-key
NEXTAUTH_SECRET=your-nextauth-secret
NEXTAUTH_URL=http://localhost:3000
ADMIN_EMAIL=your-admin-email
ADMIN_PASSWORD_HASH=your-bcrypt-hashed-password
```

To generate a password hash, you can use bcrypt. Here's a simple Node.js script to generate one:

```javascript
const bcrypt = require("bcrypt");
const password = "your-password";
const saltRounds = 10;

bcrypt.hash(password, saltRounds, function (err, hash) {
  console.log(hash);
});
```

### 3. Install Dependencies

```bash
npm install
# or
yarn install
```

### 4. Seed Initial Content

Run the seed script to populate the database with initial content:

```bash
npm run seed:cms
# or
yarn seed:cms
```

### 5. Run the Development Server

```bash
npm run dev
# or
yarn dev
```

## Usage

### Accessing the Admin Dashboard

1. Navigate to `/admin/login` in your browser
2. Log in with your admin credentials
3. You'll be redirected to the admin dashboard

### Managing Content

1. Go to the "Content" section in the admin dashboard
2. Select the section you want to edit (Hero, About, Projects, etc.)
3. Edit the content and save changes

### Managing Images

1. Go to the "Images" section in the admin dashboard
2. Upload new images or edit existing ones
3. Set the image type (profile, project, etc.) and provide alt text for accessibility

### Updating Your Profile Picture

1. Go to the "Images" section
2. Upload a new image with the type "profile"
3. The most recent profile image will be used on your portfolio

## Implementation Details

### Database Schema

#### portfolio_content Table

- `id`: UUID (primary key)
- `section`: VARCHAR(50) (e.g., 'hero', 'about', 'projects')
- `key`: VARCHAR(100) (e.g., 'title', 'description', 'project_1_title')
- `content`: TEXT
- `created_at`: TIMESTAMP WITH TIME ZONE
- `updated_at`: TIMESTAMP WITH TIME ZONE

#### portfolio_images Table

- `id`: UUID (primary key)
- `name`: VARCHAR(100)
- `description`: TEXT (nullable)
- `alt_text`: TEXT
- `storage_path`: TEXT
- `public_url`: TEXT
- `type`: VARCHAR(50) (e.g., 'profile', 'project', 'background')
- `created_at`: TIMESTAMP WITH TIME ZONE
- `updated_at`: TIMESTAMP WITH TIME ZONE

### API Routes

- `/api/public/content`: Get content (public)
- `/api/public/images`: Get images (public)
- `/api/cms/content`: Manage content (authenticated)
- `/api/cms/images`: Manage images (authenticated)

### Authentication & Security

The CMS uses NextAuth.js for authentication with enhanced security features:

#### Security Features

- **Server-side route protection** via Next.js middleware
- **Session management** with 8-hour expiry and automatic refresh
- **Rate limiting** on login attempts (5 attempts, 5-minute lockout)
- **Security headers** for XSS, clickjacking, and MIME-sniffing protection
- **Automatic logout** on session expiry
- **Input validation** and sanitization
- **Protected API routes** with session validation

#### Authentication Flow

1. Users must log in at `/admin/login` to access any admin functionality
2. Sessions are validated on every request
3. Expired sessions trigger automatic logout
4. Failed login attempts are tracked and rate-limited

#### Security Testing

Run security tests with: `npm run test:security`

For detailed security information, see `SECURITY.md`.

## Switching to CMS-Powered Pages

To use the CMS-powered version of your portfolio:

1. Rename `app/page.tsx` to `app/page-original.tsx`
2. Rename `app/page-with-cms.tsx` to `app/page.tsx`
3. Rename `app/layout.tsx` to `app/layout-original.tsx`
4. Rename `app/layout-with-cms.tsx` to `app/layout.tsx`

## Troubleshooting

- **Authentication Issues**: Make sure your environment variables are set correctly
- **Image Upload Problems**: Check that your Supabase Storage bucket is properly configured
- **Content Not Updating**: Clear your browser cache or try a hard refresh

## License

This project is licensed under the MIT License.
