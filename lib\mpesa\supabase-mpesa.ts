import { createClient } from '@supabase/supabase-js';
import {
  ClientProject,
  Payment,
  QuestionnaireAccess,
  PackageType,
  generateReferenceCode,
  calculateTotalPrice,
  calculateDepositAmount,
} from './types';

// Create a Supabase client
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || '';
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || '';
const supabase = createClient(supabaseUrl, supabaseAnonKey);

// Create a new client project
export async function createClientProject(
  clientName: string,
  clientEmail: string,
  clientPhone: string,
  packageType: PackageType,
  additionalServices: string[] = [],
  maintenancePlan?: string
): Promise<ClientProject> {
  try {
    // Calculate total and deposit amounts
    const totalAmount = calculateTotalPrice(packageType, additionalServices, maintenancePlan);
    const depositAmount = calculateDepositAmount(totalAmount);
    
    // Generate a unique reference code
    const referenceCode = generateReferenceCode();
    
    // Create the client project record
    const { data, error } = await supabase
      .from('client_projects')
      .insert([{
        client_name: clientName,
        client_email: clientEmail,
        client_phone: clientPhone,
        reference_code: referenceCode,
        package_type: packageType,
        total_amount: totalAmount,
        deposit_amount: depositAmount,
        status: 'pending'
      }])
      .select()
      .single();
    
    if (error) {
      console.error('Error creating client project:', error);
      throw new Error(`Failed to create client project: ${error.message}`);
    }
    
    // Add additional services if any
    if (additionalServices.length > 0) {
      const { data: servicesData, error: servicesError } = await addAdditionalServices(
        data.id,
        additionalServices
      );
      
      if (servicesError) {
        console.error('Error adding additional services:', servicesError);
      }
    }
    
    // Add maintenance plan if selected
    if (maintenancePlan) {
      const { data: planData, error: planError } = await addMaintenancePlan(
        data.id,
        maintenancePlan
      );
      
      if (planError) {
        console.error('Error adding maintenance plan:', planError);
      }
    }
    
    return data;
  } catch (error: any) {
    console.error('Unexpected error creating client project:', error);
    throw new Error(`Failed to create client project: ${error.message}`);
  }
}

// Add additional services to a client project
async function addAdditionalServices(
  clientProjectId: string,
  serviceIds: string[]
) {
  try {
    // Import the services data
    const { ADDITIONAL_SERVICES } = await import('./types');
    
    // Create records for each selected service
    const servicesToInsert = serviceIds.map(serviceId => {
      const service = ADDITIONAL_SERVICES.find(s => s.id === serviceId);
      if (!service) {
        throw new Error(`Service with ID ${serviceId} not found`);
      }
      
      return {
        client_project_id: clientProjectId,
        service_name: service.name,
        service_price: service.price
      };
    });
    
    const { data, error } = await supabase
      .from('additional_services')
      .insert(servicesToInsert)
      .select();
    
    return { data, error };
  } catch (error: any) {
    console.error('Error adding additional services:', error);
    return { data: null, error };
  }
}

// Add maintenance plan to a client project
async function addMaintenancePlan(
  clientProjectId: string,
  planId: string
) {
  try {
    // Import the plans data
    const { MAINTENANCE_PLANS } = await import('./types');
    
    // Find the selected plan
    const plan = MAINTENANCE_PLANS.find(p => p.id === planId);
    if (!plan) {
      throw new Error(`Maintenance plan with ID ${planId} not found`);
    }
    
    const { data, error } = await supabase
      .from('maintenance_plans')
      .insert([{
        client_project_id: clientProjectId,
        plan_name: plan.name,
        plan_price: plan.price
      }])
      .select();
    
    return { data, error };
  } catch (error: any) {
    console.error('Error adding maintenance plan:', error);
    return { data: null, error };
  }
}

// Get client project by reference code
export async function getClientProjectByReferenceCode(referenceCode: string): Promise<ClientProject | null> {
  try {
    const { data, error } = await supabase
      .from('client_projects')
      .select('*')
      .eq('reference_code', referenceCode)
      .single();
    
    if (error) {
      if (error.code === 'PGRST116') { // No rows returned
        return null;
      }
      console.error(`Error fetching client project with reference code ${referenceCode}:`, error);
      throw new Error(`Failed to fetch client project: ${error.message}`);
    }
    
    return data;
  } catch (error: any) {
    console.error(`Unexpected error fetching client project with reference code ${referenceCode}:`, error);
    throw new Error(`Failed to fetch client project: ${error.message}`);
  }
}

// Record a payment
export async function recordPayment(
  clientProjectId: string,
  mpesaCode: string,
  amount: number,
  paymentType: 'deposit' | 'final' = 'deposit'
): Promise<Payment> {
  try {
    const { data, error } = await supabase
      .from('payments')
      .insert([{
        client_project_id: clientProjectId,
        mpesa_code: mpesaCode,
        amount: amount,
        payment_type: paymentType,
        verified: false
      }])
      .select()
      .single();
    
    if (error) {
      console.error('Error recording payment:', error);
      throw new Error(`Failed to record payment: ${error.message}`);
    }
    
    return data;
  } catch (error: any) {
    console.error('Unexpected error recording payment:', error);
    throw new Error(`Failed to record payment: ${error.message}`);
  }
}

// Verify a payment
export async function verifyPayment(paymentId: string): Promise<Payment> {
  try {
    const { data, error } = await supabase
      .from('payments')
      .update({
        verified: true,
        verification_date: new Date().toISOString()
      })
      .eq('id', paymentId)
      .select()
      .single();
    
    if (error) {
      console.error(`Error verifying payment with ID ${paymentId}:`, error);
      throw new Error(`Failed to verify payment: ${error.message}`);
    }
    
    // Update the client project status
    await updateClientProjectStatus(data.client_project_id, 'verified');
    
    return data;
  } catch (error: any) {
    console.error(`Unexpected error verifying payment with ID ${paymentId}:`, error);
    throw new Error(`Failed to verify payment: ${error.message}`);
  }
}

// Update client project status
export async function updateClientProjectStatus(
  clientProjectId: string,
  status: 'pending' | 'verified' | 'completed' | 'cancelled'
): Promise<ClientProject> {
  try {
    const { data, error } = await supabase
      .from('client_projects')
      .update({ status, updated_at: new Date().toISOString() })
      .eq('id', clientProjectId)
      .select()
      .single();
    
    if (error) {
      console.error(`Error updating client project status for ID ${clientProjectId}:`, error);
      throw new Error(`Failed to update client project status: ${error.message}`);
    }
    
    return data;
  } catch (error: any) {
    console.error(`Unexpected error updating client project status for ID ${clientProjectId}:`, error);
    throw new Error(`Failed to update client project status: ${error.message}`);
  }
}

// Generate questionnaire access token
export async function generateQuestionnaireAccess(clientProjectId: string): Promise<QuestionnaireAccess> {
  try {
    // Generate a random access token
    const accessToken = Array.from(
      { length: 32 },
      () => Math.floor(Math.random() * 36).toString(36)
    ).join('');
    
    // Set expiration date (7 days from now)
    const expiresAt = new Date();
    expiresAt.setDate(expiresAt.getDate() + 7);
    
    const { data, error } = await supabase
      .from('questionnaire_access')
      .insert([{
        client_project_id: clientProjectId,
        access_token: accessToken,
        expires_at: expiresAt.toISOString()
      }])
      .select()
      .single();
    
    if (error) {
      console.error('Error generating questionnaire access:', error);
      throw new Error(`Failed to generate questionnaire access: ${error.message}`);
    }
    
    return data;
  } catch (error: any) {
    console.error('Unexpected error generating questionnaire access:', error);
    throw new Error(`Failed to generate questionnaire access: ${error.message}`);
  }
}
