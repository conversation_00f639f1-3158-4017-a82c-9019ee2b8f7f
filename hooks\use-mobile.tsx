import * as React from "react"

const MO<PERSON>LE_BREAKPOINT = 768

export function useIsMobile() {
  // Initialize with false to avoid undefined during first render
  const [isMobile, setIsMobile] = React.useState(false)
  // Track if component is mounted to avoid hydration issues
  const [mounted, setMounted] = React.useState(false)

  React.useEffect(() => {
    // Mark as mounted
    setMounted(true)

    // Set initial value
    setIsMobile(window.innerWidth < MOBILE_BREAKPOINT)

    // Set up event listener
    const handleResize = () => {
      setIsMobile(window.innerWidth < MOBILE_BREAKPOINT)
    }

    // Use resize event instead of matchMedia for better compatibility
    window.addEventListener('resize', handleResize)

    return () => {
      window.removeEventListener('resize', handleResize)
    }
  }, [])

  // Only return the actual value after mounting to avoid hydration mismatch
  return mounted ? isMobile : false
}
