import { NextRequest, NextResponse } from 'next/server';
import {
  getClientProjectByReferenceCode,
  recordPayment,
  verifyPayment,
  generateQuestionnaireAccess,
} from '@/lib/mpesa/supabase-mpesa-fixed';
import { paymentVerificationSchema } from '@/lib/mpesa/types';

export async function POST(request: NextRequest) {
  try {
    // Check if the request is JSON or form data
    const contentType = request.headers.get('content-type') || '';

    let referenceCode: string;
    let mpesaCode: string;

    if (contentType.includes('application/json')) {
      // Parse JSON data
      const body = await request.json();
      referenceCode = body.reference_code;
      mpesaCode = body.mpesa_code;
    } else {
      // Parse form data
      const formData = await request.formData();
      referenceCode = formData.get('reference_code') as string;
      mpesaCode = formData.get('mpesa_code') as string;
    }

    console.log('API route: verifyMpesaPayment called with data:', { referenceCode, mpesaCode });

    // Validate inputs
    if (!referenceCode) {
      return NextResponse.json(
        {
          success: false,
          error: 'Reference code is required',
        },
        { status: 400 }
      );
    }

    if (!mpesaCode) {
      return NextResponse.json(
        {
          success: false,
          error: 'M-Pesa code is required',
        },
        { status: 400 }
      );
    }

    // Validate M-Pesa code format
    try {
      paymentVerificationSchema.parse({
        mpesa_code: mpesaCode,
      });
    } catch (error) {
      console.error('Validation error:', error);
      return NextResponse.json(
        {
          success: false,
          error: 'Invalid M-Pesa code. Please enter a valid 10-character code.',
        },
        { status: 400 }
      );
    }

    // Get client project by reference code
    try {
      const clientProject = await getClientProjectByReferenceCode(referenceCode);
      if (!clientProject) {
        return NextResponse.json(
          {
            success: false,
            error: `Invalid reference code: ${referenceCode}. Please check and try again.`,
          },
          { status: 400 }
        );
      }

      console.log('API route: Found client project:', JSON.stringify(clientProject));

      // Record the payment
      try {
        const payment = await recordPayment(
          clientProject.id,
          mpesaCode,
          clientProject.deposit_amount,
          'deposit'
        );

        console.log('API route: Payment recorded successfully:', JSON.stringify(payment));

        // For this implementation, we'll manually verify the payment
        try {
          // In a production environment, you would integrate with M-Pesa API to verify the payment
          const verifiedPayment = await verifyPayment(payment.id);

          console.log('API route: Payment verified successfully:', JSON.stringify(verifiedPayment));

          // Generate questionnaire access token
          try {
            const questionnaireAccess = await generateQuestionnaireAccess(clientProject.id);

            console.log('API route: Questionnaire access generated successfully:', JSON.stringify(questionnaireAccess));

            return NextResponse.json({
              success: true,
              data: {
                client_project_id: clientProject.id,
                payment_id: verifiedPayment.id,
                questionnaire_access_token: questionnaireAccess.access_token,
              },
            });
          } catch (questionnaireError) {
            console.error('Error generating questionnaire access:', questionnaireError);
            return NextResponse.json(
              {
                success: false,
                error: `Payment verified, but failed to generate questionnaire access: ${questionnaireError instanceof Error ? questionnaireError.message : 'Unknown error'}`,
              },
              { status: 500 }
            );
          }
        } catch (verificationError) {
          console.error('Error verifying payment:', verificationError);
          return NextResponse.json(
            {
              success: false,
              error: `Failed to verify payment: ${verificationError instanceof Error ? verificationError.message : 'Unknown error'}`,
            },
            { status: 500 }
          );
        }
      } catch (paymentError) {
        console.error('Error recording payment:', paymentError);
        return NextResponse.json(
          {
            success: false,
            error: `Failed to record payment: ${paymentError instanceof Error ? paymentError.message : 'Unknown error'}`,
          },
          { status: 500 }
        );
      }
    } catch (clientProjectError) {
      console.error('Error getting client project:', clientProjectError);
      return NextResponse.json(
        {
          success: false,
          error: `Error retrieving client project: ${clientProjectError instanceof Error ? clientProjectError.message : 'Unknown error'}`,
        },
        { status: 500 }
      );
    }
  } catch (error) {
    console.error('Unexpected error in API route verifyMpesaPayment:', error);
    return NextResponse.json(
      {
        success: false,
        error: error instanceof Error ? error.message : 'An unexpected error occurred',
      },
      { status: 500 }
    );
  }
}
