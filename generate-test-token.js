/**
 * Generate Test Questionnaire Token
 *
 * This script creates a test client project and generates a questionnaire access token
 * for testing purposes, bypassing the payment verification process.
 *
 * Run this script with Node.js:
 * node generate-test-token.js
 */

require('dotenv').config({ path: '.env.local' });
const { createClient } = require('@supabase/supabase-js');

// Supabase configuration
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || '';
const supabaseKey = process.env.SUPABASE_SERVICE_ROLE_KEY || process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || '';

if (!supabaseUrl || !supabaseKey) {
  console.error('Error: Supabase URL or key not found in environment variables');
  console.log('Make sure you have a .env.local file with NEXT_PUBLIC_SUPABASE_URL and SUPABASE_SERVICE_ROLE_KEY or NEXT_PUBLIC_SUPABASE_ANON_KEY');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey);

// Generate a reference code
function generateReferenceCode() {
  const prefix = 'ZO';
  const timestamp = Date.now().toString().slice(-6);
  const random = Math.floor(Math.random() * 1000).toString().padStart(3, '0');
  return `${prefix}${timestamp}${random}`;
}

// Generate a random access token
function generateAccessToken() {
  return Array.from(
    { length: 32 },
    () => Math.floor(Math.random() * 36).toString(36)
  ).join('');
}

// Create a test client project and generate a questionnaire access token
async function generateTestToken() {
  try {
    console.log('Creating test client project...');

    // Generate a unique reference code
    const referenceCode = generateReferenceCode();

    // Create client project
    const { data: projectData, error: projectError } = await supabase
      .from('client_projects')
      .insert([{
        client_name: 'Test Client',
        client_email: '<EMAIL>',
        client_phone: '0796564593',
        reference_code: referenceCode,
        package_type: 'basic',
        total_amount: 75000,
        deposit_amount: 30000,
        status: 'verified'
      }])
      .select()
      .single();

    if (projectError) {
      console.error('Error creating client project:', projectError);
      return;
    }

    console.log('Client project created successfully:', projectData);

    // Generate questionnaire access token
    const accessToken = generateAccessToken();

    // Set expiration date (7 days from now)
    const expiresAt = new Date();
    expiresAt.setDate(expiresAt.getDate() + 7);

    // Create questionnaire access record
    const { data: accessData, error: accessError } = await supabase
      .from('questionnaire_access')
      .insert([{
        client_project_id: projectData.id,
        access_token: accessToken,
        expires_at: expiresAt.toISOString()
      }])
      .select()
      .single();

    if (accessError) {
      console.error('Error generating questionnaire access token:', accessError);
      return;
    }

    console.log('Questionnaire access token generated successfully:', accessData);

    // Print the questionnaire URL
    console.log('\n=== TEST QUESTIONNAIRE ACCESS ===');
    console.log(`Token: ${accessToken}`);
    console.log(`URL: ${process.env.NEXT_PUBLIC_URL || 'http://localhost:3000'}/questionnaire/${accessToken}`);
    console.log(`Expires: ${expiresAt.toLocaleString()}`);

    return {
      clientProject: projectData,
      accessToken: accessToken,
      expiresAt: expiresAt
    };
  } catch (error) {
    console.error('Unexpected error:', error);
  }
}

// Run the function
generateTestToken();
