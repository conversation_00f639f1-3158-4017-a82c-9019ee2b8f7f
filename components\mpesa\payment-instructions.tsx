'use client';

import { useState } from 'react';
import { motion } from 'framer-motion';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Copy, Check, ArrowRight, Smartphone, Loader2 } from 'lucide-react';
import { toast } from 'sonner';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';

interface PaymentInstructionsProps {
  referenceCode: string;
  depositAmount: number;
  onContinue: () => void;
  onBack: () => void;
}

export default function PaymentInstructions({
  referenceCode,
  depositAmount,
  onContinue,
  onBack,
}: PaymentInstructionsProps) {
  const [copied, setCopied] = useState(false);
  const [phoneNumber, setPhoneNumber] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [checkoutRequestId, setCheckoutRequestId] = useState<string | null>(null);
  const [transactionStatus, setTransactionStatus] = useState<'pending' | 'success' | 'failed' | null>(null);

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text);
    setCopied(true);
    toast.success('Reference code copied to clipboard');

    setTimeout(() => {
      setCopied(false);
    }, 2000);
  };

  const initiateSTKPush = async () => {
    if (!phoneNumber) {
      toast.error('Please enter your phone number');
      return;
    }

    // Format phone number (remove leading 0 and add country code if needed)
    let formattedPhoneNumber = phoneNumber;
    if (phoneNumber.startsWith('0')) {
      formattedPhoneNumber = `254${phoneNumber.substring(1)}`;
    } else if (phoneNumber.startsWith('+254')) {
      formattedPhoneNumber = phoneNumber.substring(1);
    } else if (!phoneNumber.startsWith('254')) {
      formattedPhoneNumber = `254${phoneNumber}`;
    }

    setIsLoading(true);

    try {
      // Use the simulated endpoint instead of the real one
      const response = await fetch('/api/mpesa/stkpush-simulate', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          phoneNumber: formattedPhoneNumber,
          referenceCode,
        }),
      });

      const result = await response.json();

      if (result.success) {
        setCheckoutRequestId(result.data.CheckoutRequestID);
        toast.success('Payment request sent to your phone. Please check your phone and enter your M-Pesa PIN to complete the payment.');

        // Start polling for transaction status
        pollTransactionStatus(result.data.CheckoutRequestID);
      } else {
        toast.error(result.error || 'Failed to initiate payment. Please try again.');
        setIsLoading(false);
      }
    } catch (error) {
      console.error('Error initiating STK Push:', error);
      toast.error('An unexpected error occurred. Please try again later.');
      setIsLoading(false);
    }
  };

  const pollTransactionStatus = async (checkoutRequestId: string) => {
    // Poll for transaction status every 5 seconds
    const pollInterval = setInterval(async () => {
      try {
        const response = await fetch('/api/mpesa/stkpush-query', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            checkoutRequestId,
          }),
        });

        const result = await response.json();

        if (result.success && result.data.status === 'success') {
          clearInterval(pollInterval);
          setTransactionStatus('success');
          setIsLoading(false);
          toast.success('Payment successful! Proceeding to the next step...');

          // Get a simulated M-Pesa code for verification
          try {
            const codeResponse = await fetch('/api/mpesa/simulate-code');
            const codeResult = await codeResponse.json();

            if (codeResult.success) {
              const mpesaCode = codeResult.data.mpesa_code;

              // Use the simulated code to verify the payment
              const verifyResponse = await fetch('/api/mpesa/verify', {
                method: 'POST',
                headers: {
                  'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                  reference_code: referenceCode,
                  mpesa_code: mpesaCode,
                }),
              });

              const verifyResult = await verifyResponse.json();

              if (verifyResult.success) {
                // Wait a moment before proceeding
                setTimeout(() => {
                  onContinue();
                }, 2000);
              } else {
                console.error('Error verifying payment with simulated code:', verifyResult.error);
                toast.error('Error verifying payment. Please proceed to manual verification.');
              }
            } else {
              console.error('Error getting simulated M-Pesa code:', codeResult.error);
              toast.error('Error generating verification code. Please proceed to manual verification.');
            }
          } catch (verifyError) {
            console.error('Error in automatic verification:', verifyError);
            toast.error('Error in automatic verification. Please proceed to manual verification.');
          }
        } else if (result.success === false && result.data?.status === 'failed') {
          clearInterval(pollInterval);
          setTransactionStatus('failed');
          setIsLoading(false);
          toast.error('Payment failed. Please try again or use manual payment method.');
        }
      } catch (error) {
        console.error('Error polling transaction status:', error);
      }
    }, 5000);

    // Stop polling after 2 minutes (24 attempts)
    setTimeout(() => {
      clearInterval(pollInterval);
      if (transactionStatus !== 'success') {
        setIsLoading(false);
        toast.info('Payment verification timed out. You can proceed to manual verification if you completed the payment.');
      }
    }, 120000);
  };

  return (
    <motion.div
      initial={{ opacity: 0, x: 50 }}
      animate={{ opacity: 1, x: 0 }}
      exit={{ opacity: 0, x: -50 }}
      transition={{ duration: 0.5 }}
      className="w-full max-w-2xl mx-auto"
    >
      <h2 className="text-2xl font-bold text-center mb-6">Payment Instructions</h2>

      <Card className="mb-6">
        <CardContent className="pt-6">
          <div className="space-y-6">
            <div className="text-center">
              <p className="text-lg font-medium mb-2">Please pay the deposit amount of:</p>
              <p className="text-3xl font-bold text-primary">KSh {depositAmount.toLocaleString()}</p>
            </div>

            <div className="bg-muted p-4 rounded-lg">
              <p className="font-medium mb-2">Your reference code:</p>
              <div className="flex items-center justify-between bg-background p-3 rounded border">
                <span className="font-mono text-lg font-bold">{referenceCode}</span>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => copyToClipboard(referenceCode)}
                  className="ml-2"
                >
                  {copied ? <Check className="h-4 w-4" /> : <Copy className="h-4 w-4" />}
                </Button>
              </div>
            </div>

            <div className="bg-blue-50 dark:bg-blue-900/20 text-blue-800 dark:text-blue-200 p-4 rounded-lg">
              <div className="flex items-start">
                <Smartphone className="h-5 w-5 mr-2 flex-shrink-0 mt-0.5" />
                <div>
                  <p className="font-medium">Pay with M-Pesa (Recommended)</p>
                  <p className="text-sm mb-4">
                    Enter your phone number below to receive an M-Pesa payment prompt on your phone.
                  </p>

                  <div className="space-y-3">
                    <div className="space-y-1">
                      <Label htmlFor="phone-number">Phone Number</Label>
                      <Input
                        id="phone-number"
                        type="tel"
                        placeholder="e.g., 0712345678"
                        value={phoneNumber}
                        onChange={(e) => setPhoneNumber(e.target.value)}
                        disabled={isLoading || transactionStatus === 'success'}
                        className="bg-white dark:bg-gray-800"
                      />
                    </div>

                    <Button
                      type="button"
                      onClick={initiateSTKPush}
                      disabled={isLoading || !phoneNumber || transactionStatus === 'success'}
                      className="w-full"
                    >
                      {isLoading ? (
                        <>
                          <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                          Processing...
                        </>
                      ) : (
                        'Pay with M-Pesa'
                      )}
                    </Button>

                    {checkoutRequestId && (
                      <p className="text-xs text-center">
                        {transactionStatus === 'success' ? (
                          <span className="text-green-600 dark:text-green-400">
                            Payment successful! Proceeding to the next step...
                          </span>
                        ) : transactionStatus === 'failed' ? (
                          <span className="text-red-600 dark:text-red-400">
                            Payment failed. Please try again or use manual payment method.
                          </span>
                        ) : (
                          <span>
                            Payment request sent. Please check your phone and enter your M-Pesa PIN.
                          </span>
                        )}
                      </p>
                    )}
                  </div>
                </div>
              </div>
            </div>

            <div className="border-t pt-4">
              <p className="text-sm text-center mb-4">
                If you prefer to pay manually or the automatic payment doesn't work:
              </p>

              <div className="space-y-4">
                <h3 className="text-lg font-semibold">Manual Payment Instructions:</h3>

                <ol className="space-y-3 list-decimal list-inside">
                  <li className="pl-2">Go to your M-Pesa menu on your phone</li>
                  <li className="pl-2">Select "Send Money"</li>
                  <li className="pl-2">Enter the phone number: <span className="font-semibold">0796564593</span></li>
                  <li className="pl-2">Enter the amount: <span className="font-semibold">KSh {depositAmount.toLocaleString()}</span></li>
                  <li className="pl-2">Enter your M-Pesa PIN</li>
                  <li className="pl-2">Confirm the transaction</li>
                  <li className="pl-2">You will receive an M-Pesa confirmation message</li>
                  <li className="pl-2">Keep the M-Pesa confirmation code for verification</li>
                </ol>
              </div>
            </div>

            <div className="bg-yellow-50 dark:bg-yellow-900/20 text-yellow-800 dark:text-yellow-200 p-4 rounded-lg">
              <p className="text-sm">
                <strong>Note:</strong> If you use the manual payment method, you will need to enter the M-Pesa confirmation code
                on the next screen to verify your payment. The code is a 10-character alphanumeric code
                (e.g., QDE12RTYHJ) found in your M-Pesa confirmation message.
              </p>
            </div>
          </div>
        </CardContent>
      </Card>

      <div className="flex justify-between">
        <Button
          type="button"
          variant="outline"
          onClick={onBack}
          disabled={isLoading}
        >
          Back
        </Button>

        <motion.div
          whileHover={{ scale: 1.03 }}
          whileTap={{ scale: 0.97 }}
          transition={{ duration: 0.2 }}
        >
          <Button
            type="button"
            onClick={onContinue}
            disabled={isLoading}
            className="bg-blue-600 hover:bg-blue-700 dark:bg-blue-500 dark:hover:bg-blue-600"
          >
            {transactionStatus === 'success' ? (
              <>
                Continue <ArrowRight className="ml-2 h-4 w-4" />
              </>
            ) : (
              <>
                I've Made the Payment Manually <ArrowRight className="ml-2 h-4 w-4" />
              </>
            )}
          </Button>
        </motion.div>
      </div>
    </motion.div>
  );
}
