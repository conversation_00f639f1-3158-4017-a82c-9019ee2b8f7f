{"name": "my-v0-project", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "seed:cms": "node scripts/seed-cms.js", "generate:password": "node scripts/generate-password-hash.js", "test:email-notifications": "node scripts/test-email-notifications.js", "supabase:login": "npx supabase login", "supabase:link": "npx supabase link --project-ref ca<PERSON>wwkbhhi", "supabase:deploy": "npx supabase functions deploy send-contact-email --project-ref ca<PERSON><PERSON>qwwkbhhi", "supabase:deploy:project-notification": "npx supabase functions deploy send-project-request-notification --project-ref ca<PERSON><PERSON>qwwkbhhi", "supabase:deploy:questionnaire-notification": "npx supabase functions deploy send-questionnaire-completion-notification --project-ref ca<PERSON>qwwkbhhi", "supabase:deploy:all": "npm run supabase:deploy && npm run supabase:deploy:project-notification && npm run supabase:deploy:questionnaire-notification", "supabase:serve": "npx supabase functions serve send-contact-email", "supabase:version": "npx supabase --version", "test:security": "node scripts/test-security.js", "analyze": "ANALYZE=true npm run build && node scripts/analyze-bundle.js", "analyze:server": "BUNDLE_ANALYZE=server npm run build", "analyze:browser": "BUNDLE_ANALYZE=browser npm run build", "analyze:bundle": "node scripts/analyze-bundle.js", "test:performance": "node scripts/performance-test.js", "security:review": "node scripts/security-review.js"}, "dependencies": {"@emotion/is-prop-valid": "latest", "@hookform/resolvers": "^3.9.1", "@radix-ui/react-accordion": "1.2.2", "@radix-ui/react-alert-dialog": "1.1.4", "@radix-ui/react-aspect-ratio": "1.1.1", "@radix-ui/react-avatar": "1.1.2", "@radix-ui/react-checkbox": "1.1.3", "@radix-ui/react-collapsible": "1.1.2", "@radix-ui/react-context-menu": "2.2.4", "@radix-ui/react-dialog": "1.1.4", "@radix-ui/react-dropdown-menu": "2.1.4", "@radix-ui/react-hover-card": "1.1.4", "@radix-ui/react-label": "2.1.1", "@radix-ui/react-menubar": "1.1.4", "@radix-ui/react-navigation-menu": "1.2.3", "@radix-ui/react-popover": "1.1.4", "@radix-ui/react-progress": "1.1.1", "@radix-ui/react-radio-group": "1.2.2", "@radix-ui/react-scroll-area": "1.2.2", "@radix-ui/react-select": "2.1.4", "@radix-ui/react-separator": "1.1.1", "@radix-ui/react-slider": "1.2.2", "@radix-ui/react-slot": "1.1.1", "@radix-ui/react-switch": "1.1.2", "@radix-ui/react-tabs": "1.1.2", "@radix-ui/react-toast": "1.2.4", "@radix-ui/react-toggle": "1.1.1", "@radix-ui/react-toggle-group": "1.1.1", "@radix-ui/react-tooltip": "1.1.6", "@supabase/supabase-js": "^2.49.4", "autoprefixer": "^10.4.20", "bcrypt": "^5.1.1", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "1.0.4", "date-fns": "4.1.0", "embla-carousel-react": "8.5.1", "framer-motion": "latest", "input-otp": "1.4.1", "lucide-react": "^0.454.0", "next": "15.2.4", "next-auth": "^4.24.11", "next-themes": "latest", "react": "^19", "react-day-picker": "8.10.1", "react-dom": "^19", "react-hook-form": "^7.54.1", "react-resizable-panels": "^2.1.7", "recharts": "2.15.0", "sonner": "^1.7.1", "tailwind-merge": "^2.5.5", "tailwindcss-animate": "^1.0.7", "vaul": "^0.9.6", "zod": "^3.24.1"}, "devDependencies": {"@types/node": "^22", "@types/react": "^19", "@types/react-dom": "^19", "dotenv": "^16.4.5", "postcss": "^8", "supabase": "^1.226.4", "tailwindcss": "^3.4.17", "typescript": "^5"}}