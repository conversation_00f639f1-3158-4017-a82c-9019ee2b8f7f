'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { toast } from 'sonner';
import Link from 'next/link';
import { validateToken } from '@/app/actions/questionnaire-actions';
import QuestionnaireForm from '@/components/questionnaire/questionnaire-form';
import { use } from 'react';

export default function QuestionnairePage({
  params,
}: {
  params: { token: string } | Promise<{ token: string }>;
}) {
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(true);
  const [isValid, setIsValid] = useState(false);
  const [clientProjectId, setClientProjectId] = useState<string>('');
  const [packageType, setPackageType] = useState<'basic' | 'standard' | 'premium'>('basic');

  // Unwrap params using React.use()
  const resolvedParams = use(params);
  const token = resolvedParams.token;

  // Validate token on component mount
  useEffect(() => {
    if (!token) return;

    const validateAccessToken = async () => {
      try {
        setIsLoading(true);

        const result = await validateToken(token);

        if (result.isValid && result.clientProjectId) {
          setIsValid(true);
          setClientProjectId(result.clientProjectId);
          setPackageType(result.packageType || 'basic');
        } else {
          setIsValid(false);
          toast.error(result.error || 'Invalid or expired access token');
        }
      } catch (error) {
        console.error('Error validating token:', error);
        toast.error('An unexpected error occurred');
        setIsValid(false);
      } finally {
        setIsLoading(false);
      }
    };

    validateAccessToken();
  }, [token]);

  if (isLoading) {
    return (
      <div className="container mx-auto px-4 py-12">
        <Card className="max-w-3xl mx-auto">
          <CardHeader>
            <CardTitle className="text-center">Loading Questionnaire...</CardTitle>
          </CardHeader>
          <CardContent className="flex justify-center py-12">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary"></div>
          </CardContent>
        </Card>
      </div>
    );
  }

  if (!isValid) {
    return (
      <div className="container mx-auto px-4 py-12">
        <Card className="max-w-3xl mx-auto">
          <CardHeader>
            <CardTitle className="text-center text-destructive">Invalid Access Token</CardTitle>
          </CardHeader>
          <CardContent className="text-center py-6">
            <p className="mb-6">
              The access token is invalid or has expired. Please contact us for assistance.
            </p>
            <Link href="/" passHref>
              <Button>Return to Homepage</Button>
            </Link>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-6 sm:py-12">
      <div className="max-w-3xl mx-auto">
        <div className="mb-4 sm:mb-8">
          <h1 className="text-2xl sm:text-3xl font-bold text-center">Project Questionnaire</h1>
          <p className="text-center text-muted-foreground mt-2 text-sm sm:text-base">
            Please complete this questionnaire to help me understand your project requirements better.
          </p>
        </div>

        <QuestionnaireForm
          clientProjectId={clientProjectId}
          packageType={packageType}
        />
      </div>
    </div>
  );
}
