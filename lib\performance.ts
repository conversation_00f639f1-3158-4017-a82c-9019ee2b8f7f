/**
 * Performance monitoring utilities
 * Provides tools for measuring and tracking application performance
 */

export interface PerformanceMetric {
  name: string
  value: number
  unit: 'ms' | 'bytes' | 'count'
  timestamp: number
  metadata?: Record<string, any>
}

export interface PerformanceReport {
  metrics: PerformanceMetric[]
  summary: {
    totalMetrics: number
    averageResponseTime: number
    slowestOperation: string
    fastestOperation: string
  }
  recommendations: string[]
}

class PerformanceMonitor {
  private metrics: PerformanceMetric[] = []
  private timers: Map<string, number> = new Map()

  /**
   * Start timing an operation
   */
  startTimer(name: string): void {
    if (typeof performance !== 'undefined') {
      this.timers.set(name, performance.now())
    }
  }

  /**
   * End timing an operation and record the metric
   */
  endTimer(name: string, metadata?: Record<string, any>): number {
    const startTime = this.timers.get(name)
    if (!startTime || typeof performance === 'undefined') {
      if (!startTime) console.warn(`Timer "${name}" was not started`)
      return 0
    }

    const duration = performance.now() - startTime
    this.timers.delete(name)

    this.recordMetric({
      name,
      value: duration,
      unit: 'ms',
      timestamp: Date.now(),
      metadata
    })

    return duration
  }

  /**
   * Record a custom metric
   */
  recordMetric(metric: PerformanceMetric): void {
    this.metrics.push(metric)

    // Keep only last 100 metrics to prevent memory leaks
    if (this.metrics.length > 100) {
      this.metrics = this.metrics.slice(-100)
    }
  }

  /**
   * Measure function execution time
   */
  async measureAsync<T>(
    name: string,
    fn: () => Promise<T>,
    metadata?: Record<string, any>
  ): Promise<T> {
    this.startTimer(name)
    try {
      const result = await fn()
      this.endTimer(name, { ...metadata, success: true })
      return result
    } catch (error) {
      this.endTimer(name, { ...metadata, success: false, error: error instanceof Error ? error.message : 'Unknown error' })
      throw error
    }
  }

  /**
   * Measure synchronous function execution time
   */
  measure<T>(
    name: string,
    fn: () => T,
    metadata?: Record<string, any>
  ): T {
    this.startTimer(name)
    try {
      const result = fn()
      this.endTimer(name, { ...metadata, success: true })
      return result
    } catch (error) {
      this.endTimer(name, { ...metadata, success: false, error: error instanceof Error ? error.message : 'Unknown error' })
      throw error
    }
  }

  /**
   * Get all recorded metrics
   */
  getMetrics(): PerformanceMetric[] {
    return [...this.metrics]
  }

  /**
   * Get metrics by name
   */
  getMetricsByName(name: string): PerformanceMetric[] {
    return this.metrics.filter(metric => metric.name === name)
  }

  /**
   * Generate performance report
   */
  generateReport(): PerformanceReport {
    const timeMetrics = this.metrics.filter(m => m.unit === 'ms')
    const totalMetrics = this.metrics.length

    const averageResponseTime = timeMetrics.length > 0
      ? timeMetrics.reduce((sum, m) => sum + m.value, 0) / timeMetrics.length
      : 0

    const sortedByTime = [...timeMetrics].sort((a, b) => b.value - a.value)
    const slowestOperation = sortedByTime[0]?.name || 'None'
    const fastestOperation = sortedByTime[sortedByTime.length - 1]?.name || 'None'

    const recommendations = this.generateRecommendations(timeMetrics)

    return {
      metrics: this.metrics,
      summary: {
        totalMetrics,
        averageResponseTime,
        slowestOperation,
        fastestOperation
      },
      recommendations
    }
  }

  /**
   * Generate performance recommendations
   */
  private generateRecommendations(timeMetrics: PerformanceMetric[]): string[] {
    const recommendations: string[] = []

    // Check for slow operations (> 1000ms)
    const slowOperations = timeMetrics.filter(m => m.value > 1000)
    if (slowOperations.length > 0) {
      recommendations.push(`${slowOperations.length} operations took longer than 1 second. Consider optimization.`)
    }

    // Check for failed operations
    const failedOperations = this.metrics.filter(m => m.metadata?.success === false)
    if (failedOperations.length > 0) {
      recommendations.push(`${failedOperations.length} operations failed. Check error handling.`)
    }

    // Check for frequent operations
    const operationCounts = new Map<string, number>()
    timeMetrics.forEach(m => {
      operationCounts.set(m.name, (operationCounts.get(m.name) || 0) + 1)
    })

    const frequentOperations = Array.from(operationCounts.entries())
      .filter(([_, count]) => count > 10)
      .map(([name]) => name)

    if (frequentOperations.length > 0) {
      recommendations.push(`Consider caching for frequently called operations: ${frequentOperations.join(', ')}`)
    }

    if (recommendations.length === 0) {
      recommendations.push('Performance looks good! No immediate optimizations needed.')
    }

    return recommendations
  }

  /**
   * Clear all metrics
   */
  clear(): void {
    this.metrics = []
    this.timers.clear()
  }

  /**
   * Log performance report to console
   */
  logReport(): void {
    const report = this.generateReport()

    console.group('📊 Performance Report')
    console.log('Summary:', report.summary)
    console.log('Recommendations:', report.recommendations)
    console.groupEnd()
  }
}

// Global performance monitor instance
export const performanceMonitor = new PerformanceMonitor()

/**
 * Decorator for measuring method performance
 */
export function measurePerformance(name?: string) {
  return function (target: any, propertyKey: string, descriptor: PropertyDescriptor) {
    const originalMethod = descriptor.value
    const metricName = name || `${target.constructor.name}.${propertyKey}`

    descriptor.value = async function (...args: any[]) {
      return performanceMonitor.measureAsync(metricName, () => originalMethod.apply(this, args))
    }

    return descriptor
  }
}

/**
 * Hook for measuring React component render performance
 */
export function usePerformanceMonitor(componentName: string) {
  if (typeof window !== 'undefined' && typeof performance !== 'undefined') {
    const startTime = performance.now()

    return () => {
      const endTime = performance.now()
      performanceMonitor.recordMetric({
        name: `React.${componentName}.render`,
        value: endTime - startTime,
        unit: 'ms',
        timestamp: Date.now()
      })
    }
  }

  return () => {} // No-op for SSR
}

/**
 * Utility to measure API response times
 */
export async function measureApiCall<T>(
  url: string,
  fetchFn: () => Promise<T>
): Promise<T> {
  return performanceMonitor.measureAsync(
    `API.${url}`,
    fetchFn,
    { url, type: 'api-call' }
  )
}
