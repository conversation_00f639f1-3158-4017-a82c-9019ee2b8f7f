# Payment System Test Client

This test client helps you test the payment system workflow in your Next.js portfolio project. It provides tools to simulate a client going through the payment process, from package selection to questionnaire access.

## Files Included

1. `test-payment-flow.js` - JavaScript functions to automate form filling
2. `payment-system-test.html` - HTML interface for testing the payment system

## How to Use

### Method 1: Using the HTML Interface

1. Open the `payment-system-test.html` file in your browser
2. Follow the instructions on the page to test the payment system
3. Use the provided test data to fill out forms automatically

### Method 2: Using the Browser Console

1. Navigate to your payment flow page at `/start-project`
2. Open your browser's developer tools (F12 or Ctrl+Shift+I)
3. Copy and paste the entire content of `test-payment-flow.js` into the console
4. Run the test functions as described below

## Test Functions

### Test the Full Flow

```javascript
testFullPaymentFlow(packageType, clientKey, codeIndex);
```

Parameters:

- `packageType`: 'basic', 'standard', or 'premium'
- `clientKey`: 'client1', 'client2', or 'client3'
- `codeIndex`: 0-3 (index of the M-Pesa code to use)

Example:

```javascript
testFullPaymentFlow("basic", "client1", 0);
```

### Test Individual Steps

#### 1. Fill Package Selection Form

```javascript
fillPackageSelectionForm(packageType);
```

Example:

```javascript
fillPackageSelectionForm("standard");
```

#### 2. Fill Client Information Form

```javascript
fillClientInformationForm(clientKey);
```

Example:

```javascript
fillClientInformationForm("client2");
```

#### 3. Fill Payment Verification Form

```javascript
fillPaymentVerificationForm(codeIndex);
```

Example:

```javascript
fillPaymentVerificationForm(1);
```

## Test Data

### Package Types

```javascript
const testPackageData = {
  // Basic Package
  basic: {
    package_type: "basic",
    additional_services: ["api-dev", "custom-components"],
    maintenance_plan: "basic",
  },

  // Standard Package
  standard: {
    package_type: "standard",
    additional_services: ["headless-cms", "performance"],
    maintenance_plan: "standard",
  },

  // Premium Package
  premium: {
    package_type: "premium",
    additional_services: ["pwa", "third-party-api"],
    maintenance_plan: "standard",
  },
};
```

### Client Information

```javascript
const testClientData = {
  // Test Client 1
  client1: {
    client_name: "John Doe",
    client_email: "<EMAIL>",
    client_phone: "0796564593",
  },

  // Test Client 2
  client2: {
    client_name: "Jane Smith",
    client_email: "<EMAIL>",
    client_phone: "0723456789",
  },

  // Test Client 3
  client3: {
    client_name: "Test User",
    client_email: "<EMAIL>",
    client_phone: "0734567890",
  },
};
```

### M-Pesa Codes

```javascript
const testMpesaCodes = ["ABC123DEFG", "XYZ456MNOP", "QWE789RTYU", "ZXC012VBNM"];
```

## Testing Process

1. **Package Selection**: Select a package type and additional services
2. **Client Information**: Enter client details
3. **Payment Instructions**: View payment instructions and reference code
4. **Payment Verification**: Enter M-Pesa confirmation code
5. **Confirmation**: View confirmation page with link to questionnaire

## Notes

- After filling each form using the test functions, you need to manually click the "Continue" or "Verify Payment" buttons to proceed to the next step.
- The test functions only fill the forms; they don't submit them.
- The console will provide instructions on what to do next after each step.
- The M-Pesa codes used in this test client are dummy codes for testing purposes only.

## Troubleshooting

If the test functions don't work as expected:

1. Make sure you're on the correct page (`/start-project`)
2. Use the `inspectFormElements()` function to identify the actual IDs and values of form elements:
   ```javascript
   inspectFormElements();
   ```
   This will print detailed information about all form elements to help you debug issues.
3. Check that the form field names match those in the test script
4. Look for error messages in the console
5. Try running the functions one by one instead of using the full flow test
6. If specific elements aren't being found, you may need to modify the selectors in the test script based on your actual HTML structure

## Customization

You can modify the test data in `test-payment-flow.js` to add more test cases or change the existing ones.
