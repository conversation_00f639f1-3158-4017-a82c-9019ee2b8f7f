"use client";

import { useState, useEffect } from "react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";

export default function DebugPage() {
  const [projectName, setProjectName] = useState("");
  const [debugData, setDebugData] = useState<any>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const fetchDebugData = async () => {
    try {
      setIsLoading(true);
      setError(null);
      
      const url = projectName
        ? `/api/public/images/debug?projectName=${encodeURIComponent(projectName)}`
        : "/api/public/images/debug";
      
      const res = await fetch(url);
      
      if (!res.ok) {
        throw new Error(`Error: ${res.status}`);
      }
      
      const data = await res.json();
      setDebugData(data);
    } catch (err: any) {
      console.error("Error fetching debug data:", err);
      setError(err.message || "Failed to fetch debug data");
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchDebugData();
  }, []);

  return (
    <div className="container mx-auto py-8 px-4">
      <h1 className="text-3xl font-bold mb-6">Image Debug Tool</h1>
      
      <div className="mb-6">
        <div className="flex gap-2 mb-4">
          <Input
            placeholder="Enter project name to test matching"
            value={projectName}
            onChange={(e) => setProjectName(e.target.value)}
            className="max-w-md"
          />
          <Button onClick={fetchDebugData} disabled={isLoading}>
            {isLoading ? "Loading..." : "Test"}
          </Button>
        </div>
        
        {error && (
          <div className="p-4 mb-4 bg-red-100 text-red-700 rounded-md">
            {error}
          </div>
        )}
      </div>
      
      {debugData && (
        <div className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>All Project Images ({debugData.totalImages})</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="overflow-x-auto">
                <table className="w-full border-collapse">
                  <thead>
                    <tr className="bg-gray-100 dark:bg-gray-800">
                      <th className="p-2 text-left">ID</th>
                      <th className="p-2 text-left">Name</th>
                      <th className="p-2 text-left">Type</th>
                      <th className="p-2 text-left">Created At</th>
                    </tr>
                  </thead>
                  <tbody>
                    {debugData.allImages.map((image: any) => (
                      <tr key={image.id} className="border-t">
                        <td className="p-2">{image.id.substring(0, 8)}...</td>
                        <td className="p-2 font-medium">{image.name}</td>
                        <td className="p-2">{image.type}</td>
                        <td className="p-2">{new Date(image.created_at).toLocaleString()}</td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </CardContent>
          </Card>
          
          {debugData.searchResults && (
            <>
              <Card>
                <CardHeader>
                  <CardTitle>Search Results for "{debugData.searchResults.query}"</CardTitle>
                </CardHeader>
                <CardContent>
                  <h3 className="text-lg font-medium mb-2">Exact Match</h3>
                  {debugData.searchResults.exactMatch ? (
                    <div className="p-3 bg-green-100 dark:bg-green-900 rounded-md mb-4">
                      <p><strong>Name:</strong> {debugData.searchResults.exactMatch.name}</p>
                      <p><strong>ID:</strong> {debugData.searchResults.exactMatch.id}</p>
                      <p className="mt-2">
                        <img 
                          src={debugData.searchResults.exactMatch.public_url} 
                          alt={debugData.searchResults.exactMatch.name}
                          className="max-h-32 rounded-md"
                        />
                      </p>
                    </div>
                  ) : (
                    <p className="text-red-500 mb-4">No exact match found</p>
                  )}
                  
                  <h3 className="text-lg font-medium mb-2">Partial Matches</h3>
                  {debugData.searchResults.partialMatches.length > 0 ? (
                    <div className="space-y-3">
                      {debugData.searchResults.partialMatches.map((match: any) => (
                        <div key={match.id} className="p-3 bg-blue-50 dark:bg-blue-900/30 rounded-md">
                          <p><strong>Name:</strong> {match.name}</p>
                          <p><strong>Match Score:</strong> {match.nameMatchScore}%</p>
                          <p className="mt-2">
                            <img 
                              src={match.public_url} 
                              alt={match.name}
                              className="max-h-32 rounded-md"
                            />
                          </p>
                        </div>
                      ))}
                    </div>
                  ) : (
                    <p className="text-red-500">No partial matches found</p>
                  )}
                </CardContent>
              </Card>
            </>
          )}
        </div>
      )}
    </div>
  );
}
