#!/usr/bin/env node

/**
 * Security Test Script
 * 
 * This script tests the basic security features of the CMS
 * Run with: node scripts/test-security.js
 */

const https = require('https');
const http = require('http');

// Configuration
const BASE_URL = process.env.NEXTAUTH_URL || 'http://localhost:3000';
const ADMIN_ROUTES = [
  '/admin',
  '/admin/content',
  '/admin/images',
  '/admin/project-requests',
  '/admin/questionnaires',
  '/admin/settings',
  '/admin/diagnostics'
];

const API_ROUTES = [
  '/api/cms/content',
  '/api/cms/images',
  '/api/cms/diagnostics'
];

console.log('🔒 Starting Security Tests for Portfolio CMS');
console.log(`Testing against: ${BASE_URL}`);
console.log('=' * 50);

/**
 * Make HTTP request
 */
function makeRequest(url, options = {}) {
  return new Promise((resolve, reject) => {
    const protocol = url.startsWith('https') ? https : http;
    
    const req = protocol.request(url, {
      method: 'GET',
      ...options
    }, (res) => {
      let data = '';
      res.on('data', chunk => data += chunk);
      res.on('end', () => {
        resolve({
          statusCode: res.statusCode,
          headers: res.headers,
          body: data
        });
      });
    });

    req.on('error', reject);
    req.end();
  });
}

/**
 * Test admin route protection
 */
async function testAdminRouteProtection() {
  console.log('\n📋 Testing Admin Route Protection...');
  
  for (const route of ADMIN_ROUTES) {
    try {
      const response = await makeRequest(`${BASE_URL}${route}`);
      
      if (response.statusCode === 200) {
        console.log(`❌ ${route} - VULNERABLE: Returns 200 without authentication`);
      } else if (response.statusCode === 302 || response.statusCode === 307) {
        const location = response.headers.location;
        if (location && location.includes('/admin/login')) {
          console.log(`✅ ${route} - PROTECTED: Redirects to login`);
        } else {
          console.log(`⚠️  ${route} - PARTIAL: Redirects but not to login (${location})`);
        }
      } else {
        console.log(`⚠️  ${route} - Status: ${response.statusCode}`);
      }
    } catch (error) {
      console.log(`❌ ${route} - ERROR: ${error.message}`);
    }
  }
}

/**
 * Test API route protection
 */
async function testAPIRouteProtection() {
  console.log('\n🔌 Testing API Route Protection...');
  
  for (const route of API_ROUTES) {
    try {
      const response = await makeRequest(`${BASE_URL}${route}`);
      
      if (response.statusCode === 401) {
        console.log(`✅ ${route} - PROTECTED: Returns 401 Unauthorized`);
      } else if (response.statusCode === 200) {
        console.log(`❌ ${route} - VULNERABLE: Returns 200 without authentication`);
      } else {
        console.log(`⚠️  ${route} - Status: ${response.statusCode}`);
      }
    } catch (error) {
      console.log(`❌ ${route} - ERROR: ${error.message}`);
    }
  }
}

/**
 * Test security headers
 */
async function testSecurityHeaders() {
  console.log('\n🛡️  Testing Security Headers...');
  
  const testRoute = '/admin';
  try {
    const response = await makeRequest(`${BASE_URL}${testRoute}`);
    const headers = response.headers;
    
    const securityHeaders = {
      'x-frame-options': 'DENY',
      'x-content-type-options': 'nosniff',
      'referrer-policy': 'strict-origin-when-cross-origin',
      'x-xss-protection': '1; mode=block'
    };
    
    for (const [header, expectedValue] of Object.entries(securityHeaders)) {
      if (headers[header]) {
        if (headers[header].toLowerCase().includes(expectedValue.toLowerCase())) {
          console.log(`✅ ${header}: ${headers[header]}`);
        } else {
          console.log(`⚠️  ${header}: ${headers[header]} (expected: ${expectedValue})`);
        }
      } else {
        console.log(`❌ ${header}: Missing`);
      }
    }
  } catch (error) {
    console.log(`❌ Security headers test failed: ${error.message}`);
  }
}

/**
 * Test login page accessibility
 */
async function testLoginPageAccess() {
  console.log('\n🔑 Testing Login Page Access...');
  
  try {
    const response = await makeRequest(`${BASE_URL}/admin/login`);
    
    if (response.statusCode === 200) {
      console.log('✅ Login page is accessible');
      
      // Check if it contains login form elements
      if (response.body.includes('email') && response.body.includes('password')) {
        console.log('✅ Login form elements detected');
      } else {
        console.log('⚠️  Login form elements not detected in response');
      }
    } else {
      console.log(`❌ Login page returns status: ${response.statusCode}`);
    }
  } catch (error) {
    console.log(`❌ Login page test failed: ${error.message}`);
  }
}

/**
 * Run all tests
 */
async function runAllTests() {
  try {
    await testLoginPageAccess();
    await testAdminRouteProtection();
    await testAPIRouteProtection();
    await testSecurityHeaders();
    
    console.log('\n' + '=' * 50);
    console.log('🔒 Security Tests Completed');
    console.log('\nNote: These are basic tests. For production, consider:');
    console.log('- Penetration testing');
    console.log('- Automated security scanning');
    console.log('- Regular security audits');
    console.log('- HTTPS enforcement testing');
    
  } catch (error) {
    console.error('Test suite failed:', error);
    process.exit(1);
  }
}

// Run tests if this script is executed directly
if (require.main === module) {
  runAllTests();
}

module.exports = {
  testAdminRouteProtection,
  testAPIRouteProtection,
  testSecurityHeaders,
  testLoginPageAccess,
  runAllTests
};
