# Manual Deployment Guide for Supabase Edge Function

Since we're having issues with the Supabase CLI, this guide will walk you through manually deploying the Edge Function through the Supabase web interface.

## Step 1: Access the Supabase Dashboard

1. Go to the Supabase Dashboard: https://supabase.com/dashboard/project/caogszaytzuiqwwkbhhi
2. Log in with your Supabase account credentials

## Step 2: Create the Edge Function

1. In the left sidebar, click on "Edge Functions"
2. Click the "Create a new function" button
3. Enter "send-contact-email" as the function name
4. Click "Create function"

## Step 3: Set Up the Shared CORS File

1. In the Edge Functions section, click on "New file"
2. Enter "_shared/cors.ts" as the file path
3. Paste the following code:

```typescript
export const corsHeaders = {
  "Access-Control-Allow-Origin": "*",
  "Access-Control-Allow-Headers": "authorization, x-client-info, apikey, content-type",
  "Access-Control-Allow-Methods": "POST, OPTIONS",
};
```

4. Click "Save"

## Step 4: Create the Edge Function Code

1. In the Edge Functions section, click on the "send-contact-email" function
2. Click on "index.ts" to edit the file
3. Replace the default code with the following:

```typescript
// Follow this setup guide to integrate the Deno runtime into your application:
// https://deno.land/manual/examples/deploy_node_server

import { serve } from "https://deno.land/std@0.168.0/http/server.ts";
import { corsHeaders } from "../_shared/cors.ts";

interface EmailPayload {
  name: string;
  email: string;
  subject: string;
  message: string;
}

const RESEND_API_KEY = Deno.env.get("RESEND_API_KEY") || "";
const RECIPIENT_EMAIL = Deno.env.get("RECIPIENT_EMAIL") || "<EMAIL>";

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === "OPTIONS") {
    return new Response("ok", { headers: corsHeaders });
  }

  try {
    // Get the request payload
    const payload: EmailPayload = await req.json();
    
    // Validate the payload
    if (!payload.name || !payload.email || !payload.subject || !payload.message) {
      return new Response(
        JSON.stringify({
          success: false,
          error: "Missing required fields",
        }),
        {
          headers: { ...corsHeaders, "Content-Type": "application/json" },
          status: 400,
        }
      );
    }

    // Send email using Resend
    const res = await fetch("https://api.resend.com/emails", {
      method: "POST",
      headers: {
        "Authorization": `Bearer ${RESEND_API_KEY}`,
        "Content-Type": "application/json",
      },
      body: JSON.stringify({
        from: "Portfolio Contact Form <<EMAIL>>",
        to: RECIPIENT_EMAIL,
        subject: `Portfolio Contact: ${payload.subject}`,
        html: `
          <h2>New Contact Form Submission</h2>
          <p><strong>Name:</strong> ${payload.name}</p>
          <p><strong>Email:</strong> ${payload.email}</p>
          <p><strong>Subject:</strong> ${payload.subject}</p>
          <p><strong>Message:</strong></p>
          <p>${payload.message.replace(/\n/g, "<br>")}</p>
        `,
      }),
    });

    const data = await res.json();

    if (res.status >= 400) {
      console.error("Error sending email:", data);
      return new Response(
        JSON.stringify({
          success: false,
          error: "Failed to send email",
        }),
        {
          headers: { ...corsHeaders, "Content-Type": "application/json" },
          status: 500,
        }
      );
    }

    return new Response(
      JSON.stringify({
        success: true,
        data,
      }),
      {
        headers: { ...corsHeaders, "Content-Type": "application/json" },
        status: 200,
      }
    );
  } catch (error) {
    console.error("Error in send-contact-email function:", error);
    return new Response(
      JSON.stringify({
        success: false,
        error: "Internal server error",
      }),
      {
        headers: { ...corsHeaders, "Content-Type": "application/json" },
        status: 500,
      }
    );
  }
});
```

4. Click "Save"

## Step 5: Set Environment Variables

1. In the Edge Functions section, click on the "send-contact-email" function
2. Click on the "Environment variables" tab
3. Add the following variables:
   - Key: `RESEND_API_KEY`, Value: `re_bHXZQij1_KrBUYTuM943sN4ViHBi5qaW3`
   - Key: `RECIPIENT_EMAIL`, Value: `<EMAIL>`
4. Click "Save"

## Step 6: Deploy the Function

1. Click on the "Deploy" button
2. Wait for the deployment to complete

## Step 7: Test the Function

1. Go to your portfolio website
2. Fill out and submit the contact form
3. You should receive an email <NAME_EMAIL>

## Troubleshooting

If you encounter any issues:

1. Check the Edge Function logs in the Supabase Dashboard
2. Verify that the environment variables are set correctly
3. Make sure the Edge Function is deployed and active
