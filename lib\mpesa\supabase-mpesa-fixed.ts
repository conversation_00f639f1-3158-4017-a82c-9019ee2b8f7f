import { createClient } from '@supabase/supabase-js';
import {
  ClientProject,
  Payment,
  QuestionnaireAccess,
  PackageType,
  generateReferenceCode,
  calculateTotalPrice,
  calculateDepositAmount,
} from './types';

// Create a Supabase client
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || '';
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || '';
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY || '';

// Create regular client with anon key
const supabase = createClient(supabaseUrl, supabaseAnonKey);

// Create admin client with service role key for operations that need to bypass RLS
const supabaseAdmin = supabaseServiceKey
  ? createClient(supabaseUrl, supabaseServiceKey)
  : supabase; // Fallback to regular client if no service role key

// Debug function to check if a payment exists
export async function checkPaymentExists(paymentId: string): Promise<boolean> {
  try {
    console.log(`Checking if payment with ID ${paymentId} exists...`);

    const { data, error, count } = await supabase
      .from('payments')
      .select('*', { count: 'exact' })
      .eq('id', paymentId);

    if (error) {
      console.error(`Error checking if payment exists: ${error.message}`);
      return false;
    }

    const exists = count !== null && count > 0;
    console.log(`Payment with ID ${paymentId} exists: ${exists}`);

    if (exists && data && data.length > 0) {
      console.log(`Payment details:`, JSON.stringify(data[0]));
    }

    return exists;
  } catch (error) {
    console.error(`Unexpected error checking if payment exists: ${error instanceof Error ? error.message : 'Unknown error'}`);
    return false;
  }
}

// Create a new client project
export async function createClientProject(
  clientName: string,
  clientEmail: string,
  clientPhone: string,
  packageType: PackageType,
  additionalServices: string[] = [],
  maintenancePlan?: string
): Promise<ClientProject> {
  try {
    // Calculate total and deposit amounts
    const totalAmount = calculateTotalPrice(packageType, additionalServices, maintenancePlan);
    const depositAmount = calculateDepositAmount(totalAmount);

    // Generate a unique reference code
    const referenceCode = generateReferenceCode();

    // Create the client project record - use admin client to bypass RLS
    const { data, error } = await supabaseAdmin
      .from('client_projects')
      .insert([{
        client_name: clientName,
        client_email: clientEmail,
        client_phone: clientPhone,
        reference_code: referenceCode,
        package_type: packageType,
        total_amount: totalAmount,
        deposit_amount: depositAmount,
        status: 'pending'
      }])
      .select()
      .single();

    if (error) {
      console.error('Error creating client project:', error);
      throw new Error(`Failed to create client project: ${error.message}`);
    }

    // Add additional services if any
    if (additionalServices.length > 0) {
      const { data: servicesData, error: servicesError } = await addAdditionalServices(
        data.id,
        additionalServices
      );

      if (servicesError) {
        console.error('Error adding additional services:', servicesError);
      }
    }

    // Add maintenance plan if selected
    if (maintenancePlan) {
      const { data: planData, error: planError } = await addMaintenancePlan(
        data.id,
        maintenancePlan
      );

      if (planError) {
        console.error('Error adding maintenance plan:', planError);
      }
    }

    return data;
  } catch (error: any) {
    console.error('Unexpected error creating client project:', error);
    throw new Error(`Failed to create client project: ${error.message}`);
  }
}

// Add additional services to a client project
async function addAdditionalServices(
  clientProjectId: string,
  serviceIds: string[]
) {
  try {
    // Import the services data
    const { ADDITIONAL_SERVICES } = await import('./types');

    // Create records for each selected service
    const servicesToInsert = serviceIds.map(serviceId => {
      const service = ADDITIONAL_SERVICES.find(s => s.id === serviceId);
      if (!service) {
        throw new Error(`Service with ID ${serviceId} not found`);
      }

      return {
        client_project_id: clientProjectId,
        service_name: service.name,
        service_price: service.price
      };
    });

    const { data, error } = await supabaseAdmin
      .from('additional_services')
      .insert(servicesToInsert)
      .select();

    return { data, error };
  } catch (error: any) {
    console.error('Error adding additional services:', error);
    return { data: null, error };
  }
}

// Add maintenance plan to a client project
async function addMaintenancePlan(
  clientProjectId: string,
  planId: string
) {
  try {
    // Import the plans data
    const { MAINTENANCE_PLANS } = await import('./types');

    // Find the selected plan
    const plan = MAINTENANCE_PLANS.find(p => p.id === planId);
    if (!plan) {
      throw new Error(`Maintenance plan with ID ${planId} not found`);
    }

    const { data, error } = await supabaseAdmin
      .from('maintenance_plans')
      .insert([{
        client_project_id: clientProjectId,
        plan_name: plan.name,
        plan_price: plan.price
      }])
      .select();

    return { data, error };
  } catch (error: any) {
    console.error('Error adding maintenance plan:', error);
    return { data: null, error };
  }
}

// Get client project by reference code
export async function getClientProjectByReferenceCode(referenceCode: string): Promise<ClientProject | null> {
  try {
    // First check if the reference code exists - use admin client to bypass RLS
    const { count, error: countError } = await supabaseAdmin
      .from('client_projects')
      .select('*', { count: 'exact', head: true })
      .eq('reference_code', referenceCode);

    if (countError) {
      console.error(`Error checking client project with reference code ${referenceCode}:`, countError);
      throw new Error(`Failed to check client project: ${countError.message}`);
    }

    // If no records found, return null
    if (count === 0) {
      console.log(`No client project found with reference code ${referenceCode}`);
      return null;
    }

    // If multiple records found (shouldn't happen with unique reference codes), log warning
    if (count && count > 1) {
      console.warn(`Multiple client projects found with reference code ${referenceCode}. Using the first one.`);
    }

    // Get the client project - use admin client to bypass RLS
    const { data, error } = await supabaseAdmin
      .from('client_projects')
      .select('*')
      .eq('reference_code', referenceCode)
      .order('created_at', { ascending: false })
      .limit(1)
      .single();

    if (error) {
      console.error(`Error fetching client project with reference code ${referenceCode}:`, error);
      throw new Error(`Failed to fetch client project: ${error.message}`);
    }

    return data;
  } catch (error: any) {
    console.error(`Unexpected error fetching client project with reference code ${referenceCode}:`, error);
    throw new Error(`Failed to fetch client project: ${error.message}`);
  }
}

// Record a payment
export async function recordPayment(
  clientProjectId: string,
  mpesaCode: string,
  amount: number,
  paymentType: 'deposit' | 'final' = 'deposit'
): Promise<Payment> {
  try {
    console.log(`Recording payment for client project ID ${clientProjectId} with M-Pesa code ${mpesaCode}`);

    // First check if the client project exists - use admin client to bypass RLS
    const { data: clientProject, error: clientError } = await supabaseAdmin
      .from('client_projects')
      .select('*')
      .eq('id', clientProjectId)
      .limit(1);

    if (clientError) {
      console.error(`Error checking client project with ID ${clientProjectId}:`, clientError);
      throw new Error(`Failed to check client project: ${clientError.message}`);
    }

    if (!clientProject || clientProject.length === 0) {
      console.error(`No client project found with ID ${clientProjectId}`);
      throw new Error(`No client project found with ID ${clientProjectId}`);
    }

    console.log(`Found client project:`, JSON.stringify(clientProject[0]));

    // Check if a payment with this M-Pesa code already exists - use admin client to bypass RLS
    const { data: existingPayment, error: checkError } = await supabaseAdmin
      .from('payments')
      .select('*')
      .eq('mpesa_code', mpesaCode)
      .order('created_at', { ascending: false })
      .limit(1);

    if (checkError) {
      console.error('Error checking existing payment:', checkError);
      throw new Error(`Failed to check existing payment: ${checkError.message}`);
    }

    // If payment already exists, return it
    if (existingPayment && existingPayment.length > 0) {
      console.log(`Payment with M-Pesa code ${mpesaCode} already exists. Returning existing payment:`, JSON.stringify(existingPayment[0]));
      return existingPayment[0];
    }

    // Insert new payment - use admin client to bypass RLS
    const { data, error } = await supabaseAdmin
      .from('payments')
      .insert([{
        client_project_id: clientProjectId,
        mpesa_code: mpesaCode,
        amount: amount,
        payment_type: paymentType,
        verified: false
      }])
      .select()
      .order('created_at', { ascending: false })
      .limit(1);

    if (error) {
      console.error('Error recording payment:', error);
      throw new Error(`Failed to record payment: ${error.message}`);
    }

    if (!data || data.length === 0) {
      throw new Error('Payment was inserted but no data was returned');
    }

    console.log(`Successfully recorded payment:`, JSON.stringify(data[0]));
    return data[0];
  } catch (error: any) {
    console.error('Unexpected error recording payment:', error);
    throw new Error(`Failed to record payment: ${error.message}`);
  }
}

// Verify a payment
export async function verifyPayment(paymentId: string): Promise<Payment> {
  try {
    console.log(`Verifying payment with ID ${paymentId}...`);

    // Get the payment details first - use admin client to bypass RLS
    const { data: payment, error: getError } = await supabaseAdmin
      .from('payments')
      .select('*')
      .eq('id', paymentId)
      .single();

    if (getError) {
      console.error(`Error getting payment with ID ${paymentId}:`, getError);
      throw new Error(`Failed to get payment: ${getError.message}`);
    }

    console.log(`Found payment with ID ${paymentId}:`, JSON.stringify(payment));

    // Check if payment is already verified
    if (payment.verified) {
      console.log(`Payment with ID ${paymentId} is already verified`);
      return payment;
    }

    // Update the payment - use admin client to bypass RLS
    const { error: updateError } = await supabaseAdmin
      .from('payments')
      .update({
        verified: true,
        verification_date: new Date().toISOString()
      })
      .eq('id', paymentId);

    if (updateError) {
      console.error(`Error updating payment with ID ${paymentId}:`, updateError);
      throw new Error(`Failed to update payment: ${updateError.message}`);
    }

    console.log(`Payment with ID ${paymentId} marked as verified in database`);

    // Create a verified payment object based on the original payment
    const verifiedPayment: Payment = {
      ...payment,
      verified: true,
      verification_date: new Date().toISOString()
    };

    console.log(`Using verified payment data:`, JSON.stringify(verifiedPayment));

    // Update the client project status
    await updateClientProjectStatus(verifiedPayment.client_project_id, 'verified');

    return verifiedPayment;
  } catch (error: any) {
    console.error(`Unexpected error verifying payment with ID ${paymentId}:`, error);
    throw new Error(`Failed to verify payment: ${error.message}`);
  }
}

// Update client project status
export async function updateClientProjectStatus(
  clientProjectId: string,
  status: 'pending' | 'verified' | 'completed' | 'cancelled'
): Promise<ClientProject> {
  try {
    const { data, error } = await supabaseAdmin
      .from('client_projects')
      .update({ status, updated_at: new Date().toISOString() })
      .eq('id', clientProjectId)
      .select()
      .maybeSingle();

    if (error) {
      console.error(`Error updating client project status for ID ${clientProjectId}:`, error);
      throw new Error(`Failed to update client project status: ${error.message}`);
    }

    if (!data) {
      throw new Error(`No client project found with ID ${clientProjectId}`);
    }

    return data;
  } catch (error: any) {
    console.error(`Unexpected error updating client project status for ID ${clientProjectId}:`, error);
    throw new Error(`Failed to update client project status: ${error.message}`);
  }
}

// Generate questionnaire access token
export async function generateQuestionnaireAccess(clientProjectId: string): Promise<QuestionnaireAccess> {
  try {
    // Generate a random access token
    const accessToken = Array.from(
      { length: 32 },
      () => Math.floor(Math.random() * 36).toString(36)
    ).join('');

    // Set expiration date (7 days from now)
    const expiresAt = new Date();
    expiresAt.setDate(expiresAt.getDate() + 7);

    const { data, error } = await supabaseAdmin
      .from('questionnaire_access')
      .insert([{
        client_project_id: clientProjectId,
        access_token: accessToken,
        expires_at: expiresAt.toISOString()
      }])
      .select()
      .maybeSingle();

    if (error) {
      console.error('Error generating questionnaire access:', error);
      throw new Error(`Failed to generate questionnaire access: ${error.message}`);
    }

    if (!data) {
      throw new Error('Questionnaire access was created but no data was returned');
    }

    return data;
  } catch (error: any) {
    console.error('Unexpected error generating questionnaire access:', error);
    throw new Error(`Failed to generate questionnaire access: ${error.message}`);
  }
}
