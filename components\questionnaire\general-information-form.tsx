'use client';

import { useState } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { motion } from 'framer-motion';
import {
  GeneralInformationFormData,
  generalInformationSchema,
  PROJECT_GOALS,
  COLOR_SCHEMES,
  TIMELINE_OPTIONS
} from '@/lib/questionnaire/types';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
  FormDescription
} from '@/components/ui/form';
import { Checkbox } from '@/components/ui/checkbox';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { toast } from 'sonner';
import { submitGeneralInformation } from '@/app/actions/questionnaire-actions';
import { QuestionnaireResponse } from '@/lib/questionnaire/types';

interface GeneralInformationFormProps {
  responseId: string;
  initialData?: Partial<GeneralInformationFormData>;
  onComplete: (data: QuestionnaireResponse) => void;
}

export default function GeneralInformationForm({
  responseId,
  initialData,
  onComplete
}: GeneralInformationFormProps) {
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Initialize form with react-hook-form and zod validation
  const form = useForm<GeneralInformationFormData>({
    resolver: zodResolver(generalInformationSchema),
    defaultValues: {
      project_name: initialData?.project_name || '',
      project_description: initialData?.project_description || '',
      target_audience: initialData?.target_audience || '',
      project_goals: initialData?.project_goals || [],
      color_scheme: initialData?.color_scheme || 'light',
      has_logo_or_brand_guidelines: initialData?.has_logo_or_brand_guidelines || false,
      preferred_launch_timeline: initialData?.preferred_launch_timeline || 'asap'
    }
  });

  // Handle form submission
  const onSubmit = async (data: GeneralInformationFormData) => {
    setIsSubmitting(true);

    try {
      console.log('Submitting general information with data:', {
        responseId,
        ...data
      });

      // Always submit to database, but show additional message if checkbox is checked
      if (data.has_logo_or_brand_guidelines) {
        // Show a more detailed message about contacting directly
        toast.info(
          "Your information has been saved. Please contact Zachary <NAME_EMAIL> to provide additional information about your logo and brand guidelines.",
          { duration: 8000 }
        );
      }

      // Normal submission flow
      try {
        const result = await submitGeneralInformation({
          responseId,
          ...data
        });

        if (result.success && result.data) {
          toast.success('General information saved successfully!');
          onComplete(result.data);
        } else {
          console.error('Server returned error:', result.error);

          // If there's a database error, still allow the user to proceed
          if (result.error && result.error.includes("Failed to update general information: fetch failed")) {
            toast.warning('There was an issue saving your information, but you can continue with the questionnaire. Please contact <NAME_EMAIL> if you encounter further issues.');

            // Create a mock response to allow continuing
            onComplete({
              id: responseId,
              ...data,
              last_section_completed: 1,
              completion_status: 'in_progress',
              created_at: new Date().toISOString(),
              updated_at: new Date().toISOString()
            } as QuestionnaireResponse);
          } else {
            toast.error(result.error || 'Failed to save information. Please try again.');
          }
        }
      } catch (submitError) {
        console.error('Error in submission:', submitError);

        // Allow user to continue despite the error
        toast.warning('There was an issue saving your information, but you can continue with the questionnaire. Please contact <NAME_EMAIL> if you encounter further issues.');

        // Create a mock response to allow continuing
        onComplete({
          id: responseId,
          ...data,
          last_section_completed: 1,
          completion_status: 'in_progress',
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        } as QuestionnaireResponse);
      }
    } catch (error) {
      console.error('Error submitting general information:', error);
      // More detailed error logging
      if (error instanceof Error) {
        console.error('Error name:', error.name);
        console.error('Error message:', error.message);
        console.error('Error stack:', error.stack);
      }

      toast.error('An unexpected error occurred. Please try again or contact Zachary <NAME_EMAIL>.');
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
      className="w-full"
    >
      <div className="mb-4 sm:mb-6">
        <h2 className="text-xl sm:text-2xl font-bold">General Information</h2>
        <p className="text-muted-foreground text-sm sm:text-base">
          Let's start with some basic information about your project.
        </p>
      </div>

      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="mobile-spacing">
          <FormField
            control={form.control}
            name="project_name"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Project Name/Title</FormLabel>
                <FormControl>
                  <Input
                    placeholder="Enter a name for your project"
                    {...field}
                    className="mobile-input"
                  />
                </FormControl>
                <FormDescription>
                  This will be used as a reference for your project.
                </FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="project_description"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Project Description</FormLabel>
                <FormControl>
                  <Textarea
                    placeholder="Briefly describe your project (2-3 sentences)"
                    {...field}
                    rows={3}
                    className="mobile-input"
                  />
                </FormControl>
                <FormDescription>
                  Provide a brief overview of what you want to achieve with this website.
                </FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="target_audience"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Target Audience</FormLabel>
                <FormControl>
                  <Textarea
                    placeholder="Who is your website primarily for?"
                    {...field}
                    rows={2}
                    className="mobile-input"
                  />
                </FormControl>
                <FormDescription>
                  Describe the primary users or visitors of your website.
                </FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="project_goals"
            render={() => (
              <FormItem>
                <div className="mb-4">
                  <FormLabel>Project Goals</FormLabel>
                  <FormDescription>
                    Select up to 3 primary goals for your website.
                  </FormDescription>
                </div>
                <div className="grid grid-cols-1 sm:grid-cols-2 gap-2 sm:gap-3">
                  {PROJECT_GOALS.map((goal) => (
                    <FormField
                      key={goal.id}
                      control={form.control}
                      name="project_goals"
                      render={({ field }) => {
                        return (
                          <FormItem
                            key={goal.id}
                            className="flex flex-row items-start space-x-3 space-y-0 rounded-md border p-4"
                          >
                            <FormControl>
                              <Checkbox
                                checked={field.value?.includes(goal.id)}
                                onCheckedChange={(checked) => {
                                  const currentGoals = field.value || [];

                                  // Limit to 3 selections
                                  if (checked && currentGoals.length >= 3 && !currentGoals.includes(goal.id)) {
                                    toast.info('You can select up to 3 goals');
                                    return;
                                  }

                                  return checked
                                    ? field.onChange([...currentGoals, goal.id])
                                    : field.onChange(
                                        currentGoals.filter((value) => value !== goal.id)
                                      );
                                }}
                              />
                            </FormControl>
                            <FormLabel className="font-normal cursor-pointer">
                              {goal.label}
                            </FormLabel>
                          </FormItem>
                        );
                      }}
                    />
                  ))}
                </div>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="color_scheme"
            render={({ field }) => (
              <FormItem className="space-y-3">
                <FormLabel>Preferred Color Scheme</FormLabel>
                <FormControl>
                  <RadioGroup
                    onValueChange={field.onChange}
                    defaultValue={field.value}
                    className="flex flex-col space-y-1"
                  >
                    {COLOR_SCHEMES.map((scheme) => (
                      <FormItem
                        key={scheme.id}
                        className="flex items-center space-x-3 space-y-0"
                      >
                        <FormControl>
                          <RadioGroupItem value={scheme.id} />
                        </FormControl>
                        <FormLabel className="font-normal cursor-pointer">
                          {scheme.label}
                        </FormLabel>
                      </FormItem>
                    ))}
                  </RadioGroup>
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="has_logo_or_brand_guidelines"
            render={({ field }) => (
              <FormItem className="flex flex-row items-start space-x-3 space-y-0 rounded-md border p-4">
                <FormControl>
                  <Checkbox
                    checked={field.value}
                    onCheckedChange={(checked) => {
                      field.onChange(checked);
                      if (checked) {
                        toast.info(
                          "Please contact Zachary <NAME_EMAIL> to provide additional information about your logo and brand guidelines.",
                          { duration: 8000 }
                        );
                      }
                    }}
                  />
                </FormControl>
                <div className="space-y-1 leading-none">
                  <FormLabel className="font-normal cursor-pointer">
                    I have a logo and/or brand guidelines
                  </FormLabel>
                  <FormDescription>
                    Check this if you already have brand assets that should be incorporated into the design.
                    You'll need to contact me directly to provide these assets.
                  </FormDescription>
                </div>
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="preferred_launch_timeline"
            render={({ field }) => (
              <FormItem className="space-y-3">
                <FormLabel>Preferred Launch Timeline</FormLabel>
                <FormControl>
                  <RadioGroup
                    onValueChange={field.onChange}
                    defaultValue={field.value}
                    className="flex flex-col space-y-1"
                  >
                    {TIMELINE_OPTIONS.map((option) => (
                      <FormItem
                        key={option.id}
                        className="flex items-center space-x-3 space-y-0"
                      >
                        <FormControl>
                          <RadioGroupItem value={option.id} />
                        </FormControl>
                        <FormLabel className="font-normal cursor-pointer">
                          {option.label}
                        </FormLabel>
                      </FormItem>
                    ))}
                  </RadioGroup>
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <div className="pt-4 sm:pt-6 flex justify-end">
            <Button
              type="submit"
              disabled={isSubmitting}
              className="w-full sm:w-auto mobile-button"
            >
              {isSubmitting ? 'Saving...' : 'Continue to Next Section'}
            </Button>
          </div>
        </form>
      </Form>
    </motion.div>
  );
}
