"use client";

import { useEffect, useState } from "react";
import Link from "next/link";
import { toast } from "sonner";
import { Edit, Plus, Trash2 } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";
import { PortfolioContent, ContentSection } from "@/lib/cms/types";

export default function ContentPage() {
  const [content, setContent] = useState<PortfolioContent[]>([]);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [selectedSection, setSelectedSection] = useState<ContentSection | "all">("all");

  useEffect(() => {
    fetchContent();
  }, [selectedSection]);

  const fetchContent = async () => {
    setIsLoading(true);
    try {
      const url = selectedSection === "all"
        ? "/api/cms/content"
        : `/api/cms/content?section=${selectedSection}`;

      const res = await fetch(url);
      if (!res.ok) throw new Error("Failed to fetch content");

      const data = await res.json();
      setContent(data);
    } catch (error) {
      console.error("Error fetching content:", error);
      toast.error("Failed to load content");
    } finally {
      setIsLoading(false);
    }
  };

  const handleDeleteContent = async (id: string) => {
    try {
      const res = await fetch(`/api/cms/content/${id}`, {
        method: "DELETE",
      });

      if (!res.ok) throw new Error("Failed to delete content");

      toast.success("Content deleted successfully");
      fetchContent();
    } catch (error) {
      console.error("Error deleting content:", error);
      toast.error("Failed to delete content");
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4">
        <h1 className="text-2xl md:text-3xl font-bold">Content Management</h1>
        <Link href="/admin/content/new">
          <Button className="w-full sm:w-auto">
            <Plus className="mr-2 h-4 w-4" />
            Add New Content
          </Button>
        </Link>
      </div>

      <Card>
        <CardHeader className="p-4 md:p-6">
          <CardTitle className="text-lg md:text-xl">Filter Content</CardTitle>
        </CardHeader>
        <CardContent className="p-4 md:p-6 pt-0">
          <div className="flex items-center">
            <div className="w-full sm:w-[200px]">
              <Select
                value={selectedSection}
                onValueChange={(value) => setSelectedSection(value as ContentSection | "all")}
              >
                <SelectTrigger className="h-10">
                  <SelectValue placeholder="Select section" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Sections</SelectItem>
                  <SelectItem value="hero">Hero</SelectItem>
                  <SelectItem value="about">About</SelectItem>
                  <SelectItem value="projects">Projects</SelectItem>
                  <SelectItem value="skills">Skills</SelectItem>
                  <SelectItem value="contact">Contact</SelectItem>
                  <SelectItem value="general">General</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader className="p-4 md:p-6">
          <CardTitle className="text-lg md:text-xl">Content Items</CardTitle>
        </CardHeader>
        <CardContent className="p-4 md:p-6 pt-0">
          {isLoading ? (
            <div className="flex justify-center py-8">
              <div className="h-8 w-8 animate-spin rounded-full border-b-2 border-t-2 border-blue-500"></div>
            </div>
          ) : content.length === 0 ? (
            <div className="flex flex-col items-center justify-center py-8 text-center">
              <p className="mb-4 text-gray-500">No content found</p>
              <Link href="/admin/content/new">
                <Button variant="outline">Add Content</Button>
              </Link>
            </div>
          ) : (
            <div className="overflow-x-auto -mx-4 md:mx-0">
              <Table className="min-w-full">
                <TableHeader>
                  <TableRow>
                    <TableHead className="hidden sm:table-cell">Section</TableHead>
                    <TableHead>Key</TableHead>
                    <TableHead className="hidden md:table-cell">Content Preview</TableHead>
                    <TableHead className="hidden sm:table-cell">Last Updated</TableHead>
                    <TableHead className="text-right">Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {content.map((item) => (
                    <TableRow key={item.id}>
                      <TableCell className="hidden sm:table-cell font-medium capitalize">
                        {item.section}
                      </TableCell>
                      <TableCell>
                        <div className="sm:hidden text-xs text-gray-500 capitalize mb-1">{item.section}</div>
                        {item.key}
                      </TableCell>
                      <TableCell className="hidden md:table-cell max-w-xs truncate">
                        {item.content.length > 50
                          ? `${item.content.substring(0, 50)}...`
                          : item.content}
                      </TableCell>
                      <TableCell className="hidden sm:table-cell">
                        {new Date(item.updated_at).toLocaleDateString()}
                      </TableCell>
                      <TableCell className="text-right">
                        <div className="flex justify-end space-x-2">
                          <Link href={`/admin/content/edit/${item.id}`}>
                            <Button variant="outline" size="icon" className="h-9 w-9">
                              <Edit className="h-4 w-4" />
                            </Button>
                          </Link>
                          <AlertDialog>
                            <AlertDialogTrigger asChild>
                              <Button variant="outline" size="icon" className="h-9 w-9">
                                <Trash2 className="h-4 w-4" />
                              </Button>
                            </AlertDialogTrigger>
                            <AlertDialogContent className="max-w-[90vw] sm:max-w-lg">
                              <AlertDialogHeader>
                                <AlertDialogTitle>
                                  Are you sure you want to delete this content?
                                </AlertDialogTitle>
                                <AlertDialogDescription>
                                  This action cannot be undone. This will permanently delete the
                                  content item.
                                </AlertDialogDescription>
                              </AlertDialogHeader>
                              <AlertDialogFooter className="flex-col sm:flex-row gap-2 sm:gap-0">
                                <AlertDialogCancel className="mt-2 sm:mt-0">Cancel</AlertDialogCancel>
                                <AlertDialogAction
                                  onClick={() => handleDeleteContent(item.id)}
                                  className="sm:ml-2"
                                >
                                  Delete
                                </AlertDialogAction>
                              </AlertDialogFooter>
                            </AlertDialogContent>
                          </AlertDialog>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
