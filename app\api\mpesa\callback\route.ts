import { NextRequest, NextResponse } from 'next/server';
import { STKPushCallback } from '@/lib/mpesa/daraja-api';
import { recordPayment, verifyPayment, getClientProjectByReferenceCode } from '@/lib/mpesa/supabase-mpesa-fixed';

export async function POST(request: NextRequest) {
  try {
    // Parse the callback data
    const callbackData: STKPushCallback = await request.json();
    
    console.log('Received M-Pesa callback:', JSON.stringify(callbackData));
    
    // Extract the result code
    const { ResultCode, ResultDesc, MerchantRequestID, CheckoutRequestID } = callbackData.Body.stkCallback;
    
    // Check if the transaction was successful
    if (ResultCode !== 0) {
      console.error(`M-Pesa transaction failed: ${ResultDesc}`);
      return NextResponse.json(
        {
          success: false,
          error: `M-Pesa transaction failed: ${ResultDesc}`,
        },
        { status: 400 }
      );
    }
    
    // Extract transaction details from callback metadata
    const callbackMetadata = callbackData.Body.stkCallback.CallbackMetadata;
    if (!callbackMetadata) {
      console.error('No callback metadata found in the response');
      return NextResponse.json(
        {
          success: false,
          error: 'No callback metadata found in the response',
        },
        { status: 400 }
      );
    }
    
    // Extract transaction details
    let mpesaReceiptNumber = '';
    let transactionDate = '';
    let phoneNumber = '';
    let amount = 0;
    
    callbackMetadata.Item.forEach((item) => {
      if (item.Name === 'MpesaReceiptNumber' && item.Value) {
        mpesaReceiptNumber = item.Value.toString();
      } else if (item.Name === 'TransactionDate' && item.Value) {
        transactionDate = item.Value.toString();
      } else if (item.Name === 'PhoneNumber' && item.Value) {
        phoneNumber = item.Value.toString();
      } else if (item.Name === 'Amount' && item.Value) {
        amount = Number(item.Value);
      }
    });
    
    // Validate required fields
    if (!mpesaReceiptNumber || !phoneNumber || !amount) {
      console.error('Missing required fields in callback metadata');
      return NextResponse.json(
        {
          success: false,
          error: 'Missing required fields in callback metadata',
        },
        { status: 400 }
      );
    }
    
    // Extract the account reference (which should be the reference code)
    // This assumes you've set the AccountReference to the reference code when initiating the STK push
    const accountReference = callbackData.Body.stkCallback.CallbackMetadata?.Item.find(
      (item) => item.Name === 'AccountReference'
    )?.Value?.toString() || '';
    
    // If we don't have the reference code in the callback, we need to find it another way
    // This is a simplified example - in a real implementation, you might need to store
    // the CheckoutRequestID and reference code mapping when initiating the STK push
    let referenceCode = accountReference;
    
    // If we don't have a reference code, we can't proceed
    if (!referenceCode) {
      console.error('No reference code found in the callback data');
      return NextResponse.json(
        {
          success: false,
          error: 'No reference code found in the callback data',
        },
        { status: 400 }
      );
    }
    
    // Get client project by reference code
    const clientProject = await getClientProjectByReferenceCode(referenceCode);
    if (!clientProject) {
      console.error(`Invalid reference code: ${referenceCode}`);
      return NextResponse.json(
        {
          success: false,
          error: `Invalid reference code: ${referenceCode}`,
        },
        { status: 400 }
      );
    }
    
    console.log('Found client project:', JSON.stringify(clientProject));
    
    // Record the payment
    try {
      const payment = await recordPayment(
        clientProject.id,
        mpesaReceiptNumber,
        amount,
        'deposit'
      );
      
      console.log('Payment recorded successfully:', JSON.stringify(payment));
      
      // Verify the payment automatically
      try {
        const verifiedPayment = await verifyPayment(payment.id);
        
        console.log('Payment verified successfully:', JSON.stringify(verifiedPayment));
        
        return NextResponse.json({
          success: true,
          data: {
            ResultCode: 0,
            ResultDesc: 'Payment processed successfully',
            MerchantRequestID,
            CheckoutRequestID,
            client_project_id: clientProject.id,
            payment_id: verifiedPayment.id,
          },
        });
      } catch (verificationError) {
        console.error('Error verifying payment:', verificationError);
        return NextResponse.json(
          {
            success: false,
            error: `Failed to verify payment: ${verificationError instanceof Error ? verificationError.message : 'Unknown error'}`,
          },
          { status: 500 }
        );
      }
    } catch (paymentError) {
      console.error('Error recording payment:', paymentError);
      return NextResponse.json(
        {
          success: false,
          error: `Failed to record payment: ${paymentError instanceof Error ? paymentError.message : 'Unknown error'}`,
        },
        { status: 500 }
      );
    }
  } catch (error) {
    console.error('Error processing M-Pesa callback:', error);
    return NextResponse.json(
      {
        success: false,
        error: `Failed to process M-Pesa callback: ${error instanceof Error ? error.message : 'Unknown error'}`,
      },
      { status: 500 }
    );
  }
}
