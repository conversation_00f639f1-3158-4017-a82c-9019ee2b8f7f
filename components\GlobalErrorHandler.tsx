'use client'

import { useEffect } from 'react'
import { performanceMonitor } from '@/lib/performance'

interface GlobalErrorHandlerProps {
  onError?: (error: Error, source: string) => void
}

export default function GlobalErrorHandler({ onError }: GlobalErrorHandlerProps) {
  useEffect(() => {
    // Only run in browser environment
    if (typeof window === 'undefined') return
    // Handle unhandled JavaScript errors
    const handleError = (event: ErrorEvent) => {
      const error = new Error(event.message)
      error.stack = `${event.filename}:${event.lineno}:${event.colno}`

      // Record performance metric
      performanceMonitor.recordMetric({
        name: 'Error.Unhandled',
        value: 1,
        unit: 'count',
        timestamp: Date.now(),
        metadata: {
          message: event.message,
          filename: event.filename,
          lineno: event.lineno,
          colno: event.colno,
          stack: error.stack
        }
      })

      // Log error
      console.error('Unhandled error:', {
        message: event.message,
        filename: event.filename,
        line: event.lineno,
        column: event.colno,
        error: event.error
      })

      // Call custom error handler
      if (onError) {
        onError(error, 'unhandled-error')
      }

      // Send to monitoring service in production
      if (process.env.NODE_ENV === 'production') {
        reportToMonitoringService({
          type: 'unhandled-error',
          message: event.message,
          filename: event.filename,
          lineno: event.lineno,
          colno: event.colno,
          stack: error.stack,
          userAgent: navigator.userAgent,
          url: window.location.href,
          timestamp: new Date().toISOString()
        })
      }
    }

    // Handle unhandled promise rejections
    const handleUnhandledRejection = (event: PromiseRejectionEvent) => {
      const error = event.reason instanceof Error
        ? event.reason
        : new Error(String(event.reason))

      // Record performance metric
      performanceMonitor.recordMetric({
        name: 'Error.UnhandledRejection',
        value: 1,
        unit: 'count',
        timestamp: Date.now(),
        metadata: {
          reason: String(event.reason),
          stack: error.stack
        }
      })

      // Log error
      console.error('Unhandled promise rejection:', event.reason)

      // Call custom error handler
      if (onError) {
        onError(error, 'unhandled-rejection')
      }

      // Send to monitoring service in production
      if (process.env.NODE_ENV === 'production') {
        reportToMonitoringService({
          type: 'unhandled-rejection',
          reason: String(event.reason),
          stack: error.stack,
          userAgent: navigator.userAgent,
          url: window.location.href,
          timestamp: new Date().toISOString()
        })
      }

      // Prevent the default browser behavior
      event.preventDefault()
    }

    // Handle resource loading errors
    const handleResourceError = (event: Event) => {
      const target = event.target as HTMLElement
      const tagName = target?.tagName?.toLowerCase()
      const src = (target as any)?.src || (target as any)?.href

      // Record performance metric
      performanceMonitor.recordMetric({
        name: 'Error.Resource',
        value: 1,
        unit: 'count',
        timestamp: Date.now(),
        metadata: {
          tagName,
          src,
          type: 'resource-load-error'
        }
      })

      // Log error
      console.error('Resource loading error:', {
        tagName,
        src,
        target
      })

      // Send to monitoring service in production
      if (process.env.NODE_ENV === 'production') {
        reportToMonitoringService({
          type: 'resource-error',
          tagName,
          src,
          userAgent: navigator.userAgent,
          url: window.location.href,
          timestamp: new Date().toISOString()
        })
      }
    }

    // Add event listeners
    window.addEventListener('error', handleError)
    window.addEventListener('unhandledrejection', handleUnhandledRejection)
    window.addEventListener('error', handleResourceError, true) // Capture phase for resource errors

    // Cleanup
    return () => {
      window.removeEventListener('error', handleError)
      window.removeEventListener('unhandledrejection', handleUnhandledRejection)
      window.removeEventListener('error', handleResourceError, true)
    }
  }, [onError])

  // This component doesn't render anything
  return null
}

// Function to report errors to monitoring service
function reportToMonitoringService(errorData: any) {
  // Example implementation - replace with your monitoring service
  try {
    // Option 1: Send to your own API endpoint
    fetch('/api/errors', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(errorData),
    }).catch(console.error)

    // Option 2: Send to external service (uncomment and configure as needed)
    // if (window.gtag) {
    //   window.gtag('event', 'exception', {
    //     description: errorData.message || errorData.reason,
    //     fatal: false
    //   })
    // }

    // Option 3: Send to Sentry (if using Sentry)
    // if (window.Sentry) {
    //   window.Sentry.captureException(new Error(errorData.message || errorData.reason))
    // }

  } catch (error) {
    console.error('Failed to report error to monitoring service:', error)
  }
}

// Utility function to manually report errors
export function reportError(error: Error, context?: string) {
  performanceMonitor.recordMetric({
    name: 'Error.Manual',
    value: 1,
    unit: 'count',
    timestamp: Date.now(),
    metadata: {
      message: error.message,
      stack: error.stack,
      context
    }
  })

  console.error('Manual error report:', error, context)

  if (process.env.NODE_ENV === 'production') {
    reportToMonitoringService({
      type: 'manual-error',
      message: error.message,
      stack: error.stack,
      context,
      userAgent: typeof window !== 'undefined' ? navigator.userAgent : 'unknown',
      url: typeof window !== 'undefined' ? window.location.href : 'unknown',
      timestamp: new Date().toISOString()
    })
  }
}

// Hook for error reporting in components
export function useErrorReporting() {
  return {
    reportError,
    reportInfo: (message: string, data?: any) => {
      performanceMonitor.recordMetric({
        name: 'Info.Report',
        value: 1,
        unit: 'count',
        timestamp: Date.now(),
        metadata: { message, data }
      })

      console.info('Info report:', message, data)
    },
    reportWarning: (message: string, data?: any) => {
      performanceMonitor.recordMetric({
        name: 'Warning.Report',
        value: 1,
        unit: 'count',
        timestamp: Date.now(),
        metadata: { message, data }
      })

      console.warn('Warning report:', message, data)
    }
  }
}
