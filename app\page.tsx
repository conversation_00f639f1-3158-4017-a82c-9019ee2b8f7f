"use client"

import Link from "next/link"
import Image from "next/image"
import { <PERSON>R<PERSON>, Github, Linkedin, Mail, Download, Eye } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import ProjectCard from "@/components/project-card"
import ProjectCardWithDynamicImage from "@/components/project-card-with-dynamic-image"
import SkillBadge from "@/components/skill-badge"
import ContactForm from "@/components/contact-form"
import { motion } from "framer-motion"
import { useEffect, useState } from "react"
import { useTheme } from "next-themes"
import ParticlesBackground from "@/components/particles-background"
import AnimatedButton from "@/components/animated-button"
import { useTextAnimation } from "@/hooks/useTextAnimation"
import { useSmoothScroll } from "@/hooks/useSmoothScroll"
import ScrollToTop from "@/components/scroll-to-top"
import MobileMenu from "@/components/mobile-menu"
import { useCmsContent, useCmsSectionContent, useCmsImage } from "@/hooks/useCmsContent"
import { textToParagraphs } from "@/lib/utils"

export default function Home() {
  const { setTheme, resolvedTheme } = useTheme()
  const [mounted, setMounted] = useState(false)
  const { scrollToElement } = useSmoothScroll({
    offset: 80, // Offset for header height
    duration: 1000, // Longer duration for smoother effect
  })

  // Fetch CMS content
  const { content: heroTitle } = useCmsContent('hero', 'title')
  const { content: heroSubtitle } = useCmsContent('hero', 'subtitle')
  const { content: heroDescription } = useCmsContent('hero', 'description')

  const { content: aboutContent } = useCmsSectionContent('about')
  const { content: projectsContent } = useCmsSectionContent('projects')
  const { content: skillsContent } = useCmsSectionContent('skills')
  const { content: contactContent } = useCmsSectionContent('contact')

  // Fetch profile image
  const { imageUrl: profileImageUrl, altText: profileAltText } = useCmsImage('profile')

  // Parse project tags
  const getProjectTags = (tagsString: string): string[] => {
    return tagsString ? tagsString.split(',').map(tag => tag.trim()) : []
  }

  // Handle resume download
  const handleDownloadResume = () => {
    const link = document.createElement('a');
    link.href = '/Zachary_Odero_Resume.pdf';
    link.download = 'Zachary_Odero_Resume.pdf';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  // Text animation for the name
  // Extract just the name part from the title (removing "Hi, I'm" if present)
  const fullName = heroTitle?.replace(/^Hi,\s+I'm\s+/i, "") || "Zachary Odero";
  const { displayedText: animatedName, isComplete } = useTextAnimation({
    text: fullName,
    typingSpeed: 80,
    startDelay: 1000
  })

  // Handle initial theme based on system preference
  useEffect(() => {
    const isDarkMode = window.matchMedia && window.matchMedia("(prefers-color-scheme: dark)").matches
    if (isDarkMode) {
      document.documentElement.classList.add("dark")
    }
    setMounted(true)
  }, [])

  const toggleTheme = () => {
    setTheme(resolvedTheme === 'dark' ? 'light' : 'dark')
  }

  return (
    <div className="min-h-screen bg-white dark:bg-gray-950 theme-transition will-animate">
      {/* Scroll to top button */}
      <ScrollToTop />

      {/* Navigation */}
      <motion.header
        className="sticky top-0 z-50 bg-white/90 dark:bg-gray-950/90 backdrop-blur-sm shadow-sm will-animate"
        initial={{ y: -100 }}
        animate={{ y: 0 }}
        transition={{ type: 'spring', stiffness: 300, damping: 30 }}
        style={{ transform: 'translateZ(0)' }} // Force hardware acceleration
      >
        <div className="container mx-auto px-4 py-4">
          <nav className="flex items-center justify-between">
            <motion.div
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
            >
              <Link href="/" className="text-xl font-bold text-black dark:text-white">
                Portfolio
              </Link>
            </motion.div>

            {/* Desktop Navigation */}
            <div className="hidden md:flex items-center gap-6">
              <motion.a
                href="#about"
                onClick={(e) => {
                  e.preventDefault();
                  scrollToElement('about');
                }}
                className="text-sm font-medium text-gray-600 hover:text-blue-600 dark:text-gray-300 dark:hover:text-blue-400 cursor-pointer"
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
              >
                About
              </motion.a>
              <motion.a
                href="#projects"
                onClick={(e) => {
                  e.preventDefault();
                  scrollToElement('projects');
                }}
                className="text-sm font-medium text-gray-600 hover:text-blue-600 dark:text-gray-300 dark:hover:text-blue-400 cursor-pointer"
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
              >
                Projects
              </motion.a>
              <motion.a
                href="#skills"
                onClick={(e) => {
                  e.preventDefault();
                  scrollToElement('skills');
                }}
                className="text-sm font-medium text-gray-600 hover:text-blue-600 dark:text-gray-300 dark:hover:text-blue-400 cursor-pointer"
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
              >
                Skills
              </motion.a>
              <motion.a
                href="#contact"
                onClick={(e) => {
                  e.preventDefault();
                  scrollToElement('contact');
                }}
                className="text-sm font-medium text-gray-600 hover:text-blue-600 dark:text-gray-300 dark:hover:text-blue-400 cursor-pointer"
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
              >
                Contact
              </motion.a>
              <motion.div
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
              >
                <Link href="/start-project" className="px-4 py-2 rounded-md bg-blue-600 text-white hover:bg-blue-700 dark:bg-blue-500 dark:hover:bg-blue-600 transition-colors">
                  Start a Project
                </Link>
              </motion.div>
            </div>

            {/* Mobile Menu */}
            <div className="flex items-center gap-3">
              <button
                className="rounded-md p-2 bg-gray-100 text-gray-600 hover:text-blue-600 dark:bg-gray-800 dark:text-gray-300 dark:hover:text-blue-400 transition-colors"
                onClick={toggleTheme}
                aria-label="Toggle dark mode"
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="20"
                  height="20"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  className="hidden dark:block" // Sun icon (shows in dark mode)
                >
                  <circle cx="12" cy="12" r="5"></circle>
                  <line x1="12" y1="1" x2="12" y2="3"></line>
                  <line x1="12" y1="21" x2="12" y2="23"></line>
                  <line x1="4.22" y1="4.22" x2="5.64" y2="5.64"></line>
                  <line x1="18.36" y1="18.36" x2="19.78" y2="19.78"></line>
                  <line x1="1" y1="12" x2="3" y2="12"></line>
                  <line x1="21" y1="12" x2="23" y2="12"></line>
                  <line x1="4.22" y1="19.78" x2="5.64" y2="18.36"></line>
                  <line x1="18.36" y1="5.64" x2="19.78" y2="4.22"></line>
                </svg>
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="20"
                  height="20"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  className="block dark:hidden" // Moon icon (shows in light mode)
                >
                  <path d="M21 12.79A9 9 0 1 1 11.21 3 7 7 0 0 0 21 12.79z"></path>
                </svg>
              </button>
              <MobileMenu scrollToElement={scrollToElement} />
            </div>
          </nav>
        </div>
      </motion.header>

      <main>
        {/* Hero Section */}
        <section className="relative container mx-auto px-4 py-12 md:py-20 overflow-hidden min-h-[80vh] flex items-center">
          {/* Particle background */}
          <div className="absolute inset-0 z-0">
            <ParticlesBackground />
          </div>

          {/* Content */}
          <div className="relative z-10 w-full">
            <motion.div
              className="flex flex-col items-start gap-4 md:gap-6 w-full md:w-2/3"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ duration: 0.8 }}
            >
              {/* Animated heading with staggered children */}
              <motion.div
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ duration: 0.5, delay: 0.2 }}
                className="w-full"
              >
                <motion.h1
                  className="text-4xl sm:text-5xl font-bold text-black dark:text-white md:text-6xl leading-tight"
                  initial={{ y: 20 }}
                  animate={{ y: 0 }}
                  transition={{
                    type: "spring",
                    stiffness: 100,
                    damping: 15
                  }}
                >
                  Hi, I'm{" "}
                  <motion.span
                    className="text-blue-600 dark:text-blue-400 inline-block"
                    initial={{ opacity: 0 }}
                    animate={{
                      opacity: 1,
                      transition: { delay: 0.5, duration: 0.5 }
                    }}
                    whileHover={{
                      scale: 1.05,
                      color: "#3b82f6",
                      transition: { duration: 0.2 }
                    }}
                  >
                    {animatedName}
                    {!isComplete && animatedName && (
                      <motion.span
                        initial={{ opacity: 0 }}
                        animate={{ opacity: [0, 1, 0] }}
                        transition={{
                          repeat: Infinity,
                          duration: 1,
                          repeatDelay: 0.2
                        }}
                        className="ml-1 inline-block"
                      >
                        |
                      </motion.span>
                    )}
                  </motion.span>
                </motion.h1>
              </motion.div>

              {/* Animated paragraph */}
              <motion.p
                className="text-lg sm:text-xl text-gray-600 dark:text-gray-300"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{
                  duration: 0.5,
                  delay: 0.8,
                  ease: "easeOut"
                }}
              >
                {heroDescription}
              </motion.p>

              {/* Animated button */}
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: 1.2 }}
                className="w-full sm:w-auto"
              >
                <AnimatedButton
                  className="mt-4 w-full sm:w-auto bg-blue-600 hover:bg-blue-700 dark:bg-blue-500 dark:hover:bg-blue-600"
                  onClick={() => scrollToElement("projects")}
                >
                  View My Work <ArrowRight className="ml-2 h-4 w-4" />
                </AnimatedButton>
              </motion.div>
            </motion.div>
          </div>

          {/* Decorative elements */}
          <motion.div
            className="absolute bottom-0 right-0 w-48 md:w-64 h-48 md:h-64 bg-blue-500 rounded-full filter blur-3xl opacity-10 dark:opacity-20 z-0"
            animate={{
              scale: [1, 1.2, 1],
              opacity: [0.1, 0.15, 0.1],
            }}
            transition={{
              duration: 8,
              repeat: Infinity,
              repeatType: "reverse"
            }}
          />

          <motion.div
            className="absolute top-20 right-10 md:right-20 w-24 md:w-32 h-24 md:h-32 bg-purple-500 rounded-full filter blur-3xl opacity-10 dark:opacity-15 z-0"
            animate={{
              scale: [1, 1.3, 1],
              opacity: [0.1, 0.2, 0.1],
            }}
            transition={{
              duration: 6,
              repeat: Infinity,
              repeatType: "reverse",
              delay: 1
            }}
          />
        </section>

        {/* About Section */}
        <section id="about" className="bg-gray-50 dark:bg-gray-900 py-12 md:py-20 transition-colors duration-300">
          <div className="container mx-auto px-4">
            <motion.h2
              className="mb-8 md:mb-12 text-2xl md:text-3xl font-bold text-black dark:text-white text-center md:text-left"
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.5 }}
            >
              About Me
            </motion.h2>
            <div className="flex flex-col gap-8 md:flex-row">
              <motion.div
                className="w-full md:w-1/3 max-w-[250px] mx-auto md:mx-0"
                initial={{ opacity: 0, x: -50 }}
                whileInView={{ opacity: 1, x: 0 }}
                viewport={{ once: true }}
                transition={{ duration: 0.6 }}
              >
                <motion.div
                  className="aspect-square w-full overflow-hidden rounded-md bg-gray-200 dark:bg-gray-800 shadow-md"
                  whileHover={{
                    scale: 1.03,
                    boxShadow: "0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 8px 10px -6px rgba(0, 0, 0, 0.1)"
                  }}
                  transition={{ duration: 0.3 }}
                >
                  {profileImageUrl ? (
                    <Image
                      src={profileImageUrl}
                      alt={profileAltText || "Profile"}
                      width={250}
                      height={250}
                      className="h-full w-full object-cover"
                    />
                  ) : (
                    <img
                      src="/images/profile.jpg"
                      alt="Profile"
                      className="h-full w-full object-cover"
                    />
                  )}
                </motion.div>
              </motion.div>
              <motion.div
                className="w-full md:w-2/3"
                initial={{ opacity: 0, x: 50 }}
                whileInView={{ opacity: 1, x: 0 }}
                viewport={{ once: true }}
                transition={{ duration: 0.6 }}
              >
                {/* Bio content with paragraph support */}
                {textToParagraphs(aboutContent.bio).map((paragraph, index) => (
                  <motion.p
                    key={`bio-${index}`}
                    className="mb-4 text-gray-600 dark:text-gray-300 text-sm md:text-base p-2 rounded-md"
                    whileHover={{
                      backgroundColor: "rgba(59, 130, 246, 0.05)",
                      x: 5
                    }}
                    transition={{ duration: 0.2 }}
                  >
                    {paragraph}
                  </motion.p>
                ))}
                <motion.p
                  className="mb-4 text-gray-600 dark:text-gray-300 text-sm md:text-base p-2 rounded-md"
                  whileHover={{
                    backgroundColor: "rgba(59, 130, 246, 0.05)",
                    x: 5
                  }}
                  transition={{ duration: 0.2 }}
                >
                  {aboutContent.education}
                </motion.p>
                <motion.p
                  className="text-gray-600 dark:text-gray-300 text-sm md:text-base p-2 rounded-md"
                  whileHover={{
                    backgroundColor: "rgba(59, 130, 246, 0.05)",
                    x: 5
                  }}
                  transition={{ duration: 0.2 }}
                >
                  {aboutContent.experience}
                </motion.p>
                <div className="flex flex-col sm:flex-row gap-3 mt-6">
                  <Link href="/cv">
                    <Button
                      variant="outline"
                      className="w-full sm:w-auto border-blue-600 text-blue-600 hover:bg-blue-600 hover:text-white dark:border-blue-400 dark:text-blue-400 dark:hover:bg-blue-500 dark:hover:text-white"
                    >
                      <Eye className="h-4 w-4 mr-2" />
                      View Resume
                    </Button>
                  </Link>
                  <Button
                    onClick={handleDownloadResume}
                    className="w-full sm:w-auto bg-blue-600 hover:bg-blue-700 dark:bg-blue-500 dark:hover:bg-blue-600"
                  >
                    <Download className="h-4 w-4 mr-2" />
                    Download Resume
                  </Button>
                </div>
              </motion.div>
            </div>
          </div>
        </section>
        {/* Projects Section */}
        <section id="projects" className="py-12 md:py-20 dark:bg-gray-950 transition-colors duration-300">
          <div className="container mx-auto px-4">
            <motion.h2
              className="mb-8 md:mb-12 text-2xl md:text-3xl font-bold text-black dark:text-white text-center md:text-left"
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.5 }}
            >
              My Projects
            </motion.h2>
            <motion.div
              className="grid gap-6 sm:grid-cols-1 md:grid-cols-2 lg:grid-cols-3"
              initial={{ opacity: 0 }}
              whileInView={{ opacity: 1 }}
              viewport={{ once: true }}
              transition={{ staggerChildren: 0.1 }}
            >
              {projectsContent.project_1_title && (
                <ProjectCardWithDynamicImage
                  title={projectsContent.project_1_title}
                  description={projectsContent.project_1_description || ""}
                  tags={getProjectTags(projectsContent.project_1_tags || "")}
                  projectName={projectsContent.project_1_title}
                  fallbackImageUrl="/images/projects/ecommerce-neon.jpeg"
                  githubUrl={projectsContent.project_1_github || ""}
                  demoUrl={projectsContent.project_1_demo || ""}
                />
              )}
              {projectsContent.project_2_title && (
                <ProjectCardWithDynamicImage
                  title={projectsContent.project_2_title}
                  description={projectsContent.project_2_description || ""}
                  tags={getProjectTags(projectsContent.project_2_tags || "")}
                  projectName={projectsContent.project_2_title}
                  fallbackImageUrl="/images/projects/task-management.jpeg"
                  githubUrl={projectsContent.project_2_github || ""}
                  demoUrl={projectsContent.project_2_demo || ""}
                />
              )}
              {projectsContent.project_3_title && (
                <ProjectCardWithDynamicImage
                  title={projectsContent.project_3_title}
                  description={projectsContent.project_3_description || ""}
                  tags={getProjectTags(projectsContent.project_3_tags || "")}
                  projectName={projectsContent.project_3_title}
                  fallbackImageUrl="/images/projects/portfolio.jpeg"
                  githubUrl={projectsContent.project_3_github || ""}
                  demoUrl={projectsContent.project_3_demo || ""}
                />
              )}
              {projectsContent.project_4_title && (
                <ProjectCardWithDynamicImage
                  title={projectsContent.project_4_title}
                  description={projectsContent.project_4_description || ""}
                  tags={getProjectTags(projectsContent.project_4_tags || "")}
                  projectName={projectsContent.project_4_title}
                  fallbackImageUrl="/images/projects/default-project.jpeg"
                  githubUrl={projectsContent.project_4_github || ""}
                  demoUrl={projectsContent.project_4_demo || ""}
                />
              )}
            </motion.div>
          </div>
        </section>

        {/* Skills Section */}
        <section id="skills" className="bg-gray-50 dark:bg-gray-900 py-12 md:py-20 transition-colors duration-300 overflow-hidden">
          <div className="container mx-auto px-4">
            <motion.h2
              className="mb-8 md:mb-12 text-2xl md:text-3xl font-bold text-black dark:text-white text-center md:text-left"
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.5 }}
            >
              Skills
            </motion.h2>

            {/* Skills container with continuous sliding animation */}
            <div className="relative">
              {/* First row of skills - continuous left to right */}
              <div className="overflow-hidden mb-6 py-2">
                <motion.div
                  className="flex gap-4 md:gap-6"
                  animate={{
                    x: ["-15%", "0%", "-15%"]
                  }}
                  transition={{
                    repeat: Infinity,
                    duration: 30,
                    ease: "linear",
                  }}
                >
                  {/* Double the skills to create seamless loop */}
                  {[...Array(2)].flatMap((_, i) =>
                    (skillsContent.frontend_skills || "JavaScript,TypeScript,React,Next.js,Node.js")
                      .split(',')
                      .map(skill => skill.trim())
                      .map((skill) => (
                        <SkillBadge key={`${skill}-${i}`} name={skill} />
                      ))
                  )}
                </motion.div>
              </div>

              {/* Second row of skills - continuous right to left */}
              <div className="overflow-hidden py-2">
                <motion.div
                  className="flex gap-4 md:gap-6"
                  animate={{
                    x: ["0%", "-15%", "0%"]
                  }}
                  transition={{
                    repeat: Infinity,
                    duration: 30,
                    ease: "linear",
                  }}
                >
                  {/* Double the skills to create seamless loop */}
                  {[...Array(2)].flatMap((_, i) =>
                    (skillsContent.backend_skills || "Express,MongoDB,PostgreSQL,Firebase,AWS")
                      .split(',')
                      .map(skill => skill.trim())
                      .map((skill) => (
                        <SkillBadge key={`${skill}-${i}`} name={skill} />
                      ))
                  )}
                </motion.div>
              </div>

              {/* Third row of skills - continuous left to right */}
              <div className="overflow-hidden py-2">
                <motion.div
                  className="flex gap-4 md:gap-6"
                  animate={{
                    x: ["-10%", "0%", "-10%"]
                  }}
                  transition={{
                    repeat: Infinity,
                    duration: 25,
                    ease: "linear",
                  }}
                >
                  {/* Double the skills to create seamless loop */}
                  {[...Array(2)].flatMap((_, i) =>
                    (skillsContent.other_skills || "Git,GitHub,Docker,Jest,Tailwind CSS")
                      .split(',')
                      .map(skill => skill.trim())
                      .map((skill) => (
                        <SkillBadge key={`${skill}-${i}`} name={skill} />
                      ))
                  )}
                </motion.div>
              </div>
            </div>
          </div>
        </section>

        {/* Contact Section */}
        <section id="contact" className="py-12 md:py-20 dark:bg-gray-950 transition-colors duration-300">
          <div className="container mx-auto px-4">
            <motion.h2
              className="mb-8 md:mb-12 text-2xl md:text-3xl font-bold text-black dark:text-white text-center md:text-left"
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.5 }}
            >
              Contact Me
            </motion.h2>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
              <motion.div
                initial={{ opacity: 0, x: -50 }}
                whileInView={{ opacity: 1, x: 0 }}
                viewport={{ once: true }}
                transition={{ duration: 0.6 }}
              >
                <h3 className="text-xl font-semibold mb-4 text-black dark:text-white">Get in Touch</h3>
                <p className="text-gray-600 dark:text-gray-300 mb-6">
                  {contactContent.contact_intro || "Feel free to reach out to me through any of the following channels:"}
                </p>
                <div className="space-y-4">
                  <motion.a
                    href={`mailto:${contactContent.email || "<EMAIL>"}`}
                    className="flex items-center text-gray-600 hover:text-blue-600 dark:text-gray-300 dark:hover:text-blue-400"
                    whileHover={{ x: 5 }}
                    transition={{ duration: 0.2 }}
                  >
                    <Mail className="mr-2 h-5 w-5" />
                    <span>{contactContent.email || "<EMAIL>"}</span>
                  </motion.a>
                  <motion.a
                    href={contactContent.linkedin || "https://www.linkedin.com/in/zacharyodero"}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="flex items-center text-gray-600 hover:text-blue-600 dark:text-gray-300 dark:hover:text-blue-400"
                    whileHover={{ x: 5 }}
                    transition={{ duration: 0.2 }}
                  >
                    <Linkedin className="mr-2 h-5 w-5" />
                    <span>LinkedIn</span>
                  </motion.a>
                  <motion.a
                    href={contactContent.github || "https://github.com/ZacharyOdero"}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="flex items-center text-gray-600 hover:text-blue-600 dark:text-gray-300 dark:hover:text-blue-400"
                    whileHover={{ x: 5 }}
                    transition={{ duration: 0.2 }}
                  >
                    <Github className="mr-2 h-5 w-5" />
                    <span>GitHub</span>
                  </motion.a>
                </div>
              </motion.div>
              <motion.div
                initial={{ opacity: 0, x: 50 }}
                whileInView={{ opacity: 1, x: 0 }}
                viewport={{ once: true }}
                transition={{ duration: 0.6 }}
              >
                <ContactForm />
              </motion.div>
            </div>
          </div>
        </section>

        {/* Footer */}
        <footer className="bg-gray-50 dark:bg-gray-900 py-8 transition-colors duration-300">
          <div className="container mx-auto px-4">
            <div className="flex flex-col md:flex-row justify-between items-center">
              <p className="text-gray-600 dark:text-gray-300 text-sm mb-4 md:mb-0">
                © {new Date().getFullYear()} Zachary Odero. All rights reserved.
              </p>
              <div className="flex space-x-4">
                <motion.a
                  href={contactContent.github || "https://github.com/ZacharyOdero"}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="text-gray-600 hover:text-blue-600 dark:text-gray-300 dark:hover:text-blue-400"
                  whileHover={{ y: -3 }}
                  transition={{ duration: 0.2 }}
                >
                  <Github className="h-5 w-5" />
                </motion.a>
                <motion.a
                  href={contactContent.linkedin || "https://www.linkedin.com/in/zacharyodero"}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="text-gray-600 hover:text-blue-600 dark:text-gray-300 dark:hover:text-blue-400"
                  whileHover={{ y: -3 }}
                  transition={{ duration: 0.2 }}
                >
                  <Linkedin className="h-5 w-5" />
                </motion.a>
                <motion.a
                  href={`mailto:${contactContent.email || "<EMAIL>"}`}
                  className="text-gray-600 hover:text-blue-600 dark:text-gray-300 dark:hover:text-blue-400"
                  whileHover={{ y: -3 }}
                  transition={{ duration: 0.2 }}
                >
                  <Mail className="h-5 w-5" />
                </motion.a>
              </div>
            </div>
          </div>
        </footer>
      </main>
    </div>
  )
}
