import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@supabase/supabase-js';
import { ImageType } from '@/lib/cms/types';

// Create a Supabase client for public routes
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || '';
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || '';
const supabase = createClient(supabaseUrl, supabaseAnonKey);

export async function GET(request: NextRequest) {
  try {
    // Check if Supabase URL and key are available
    if (!supabaseUrl || !supabaseAnonKey) {
      console.warn('Missing Supabase credentials in images API route');
      return NextResponse.json(
        { error: 'Database configuration error' },
        { status: 500 }
      );
    }

    const { searchParams } = new URL(request.url);
    const type = searchParams.get('type');
    const id = searchParams.get('id');

    // Add cache control headers to the response
    const headers = new Headers({
      'Cache-Control': 'no-cache, no-store, must-revalidate',
      'Pragma': 'no-cache',
      'Expires': '0'
    });

    if (id) {
      // Get image by ID
      try {
        const { data, error } = await supabase
          .from('portfolio_images')
          .select('*')
          .eq('id', id)
          .single();

        if (error) {
          console.error(`Error fetching image with id ${id}:`, error);
          return NextResponse.json(
            { error: `Image with id ${id} not found` },
            { status: 404, headers }
          );
        }

        return NextResponse.json(data, { headers });
      } catch (idError) {
        console.error(`Unexpected error fetching image with id ${id}:`, idError);
        return NextResponse.json(
          { error: 'Database error', details: idError instanceof Error ? idError.message : 'Unknown error' },
          { status: 500, headers }
        );
      }
    } else if (type) {
      // Get images by type
      try {
        const { data, error } = await supabase
          .from('portfolio_images')
          .select('*')
          .eq('type', type)
          .order('created_at', { ascending: false });

        if (error) {
          console.error(`Error fetching images of type ${type}:`, error);
          return NextResponse.json(
            { error: `Failed to fetch images of type ${type}` },
            { status: 500, headers }
          );
        }

        // Return empty array instead of null
        return NextResponse.json(data || [], { headers });
      } catch (typeError) {
        console.error(`Unexpected error fetching images of type ${type}:`, typeError);
        return NextResponse.json(
          { error: 'Database error', details: typeError instanceof Error ? typeError.message : 'Unknown error' },
          { status: 500, headers }
        );
      }
    } else {
      // Get all images
      try {
        const { data, error } = await supabase
          .from('portfolio_images')
          .select('*')
          .order('created_at', { ascending: false });

        if (error) {
          console.error('Error fetching all images:', error);
          return NextResponse.json(
            { error: 'Failed to fetch images' },
            { status: 500, headers }
          );
        }

        // Return empty array instead of null
        return NextResponse.json(data || [], { headers });
      } catch (allError) {
        console.error('Unexpected error fetching all images:', allError);
        return NextResponse.json(
          { error: 'Database error', details: allError instanceof Error ? allError.message : 'Unknown error' },
          { status: 500, headers }
        );
      }
    }
  } catch (error: any) {
    console.error('Error in public images GET route:', error);
    return NextResponse.json(
      { error: error.message || 'Failed to fetch images' },
      { status: 500 }
    );
  }
}
