import type { <PERSON><PERSON><PERSON> } from 'next';
import Link from 'next/link';
import { ArrowLeft } from 'lucide-react';
import { Button } from '@/components/ui/button';

export const metadata: Metadata = {
  title: 'Start a Project - <PERSON>',
  description: 'Start a web development project with <PERSON>',
};

export default function StartProjectLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <div className="min-h-screen bg-background">
      <header className="border-b">
        <div className="container mx-auto px-4 py-4 flex items-center justify-between">
          <Link href="/" passHref>
            <Button variant="ghost" size="sm" className="gap-2">
              <ArrowLeft className="h-4 w-4" />
              Back to Portfolio
            </Button>
          </Link>
          
          <div className="text-xl font-bold"><PERSON></div>
        </div>
      </header>
      
      <main>{children}</main>
      
      <footer className="border-t py-6 mt-12">
        <div className="container mx-auto px-4 text-center text-sm text-muted-foreground">
          &copy; {new Date().getFullYear()} <PERSON>. All rights reserved.
        </div>
      </footer>
    </div>
  );
}
