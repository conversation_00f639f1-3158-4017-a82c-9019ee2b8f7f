/**
 * Payment Verification Debug Script
 * 
 * This script helps debug issues with the payment verification process.
 * It provides detailed logging and alternative verification methods.
 */

// =============================================
// Debug Functions
// =============================================

// Function to log detailed information about the verification process
function logVerificationDetails() {
  console.log('=== PAYMENT VERIFICATION DEBUG INFO ===');
  
  // Get reference code from the page
  const referenceCodeElement = document.querySelector('.bg-muted p + div .font-mono');
  const referenceCode = referenceCodeElement ? referenceCodeElement.textContent.trim() : 'Not found';
  
  console.log(`Reference Code: ${referenceCode}`);
  
  // Get M-Pesa code input
  const mpesaCodeInput = document.querySelector('input[name="mpesa_code"]');
  const mpesaCode = mpesaCodeInput ? mpesaCodeInput.value : 'Not found';
  
  console.log(`M-Pesa Code: ${mpesaCode}`);
  
  // Check if the form is in submitting state
  const submitButton = document.querySelector('button[type="submit"]');
  const isSubmitting = submitButton && submitButton.textContent.includes('Verifying');
  
  console.log(`Form is submitting: ${isSubmitting}`);
  
  // Check for error messages
  const errorMessages = Array.from(document.querySelectorAll('[role="alert"]')).map(el => el.textContent);
  
  if (errorMessages.length > 0) {
    console.log('Error messages:', errorMessages);
  } else {
    console.log('No visible error messages');
  }
  
  return {
    referenceCode,
    mpesaCode,
    isSubmitting,
    errorMessages
  };
}

// Function to monitor network requests
function monitorNetworkRequests() {
  console.log('=== MONITORING NETWORK REQUESTS ===');
  console.log('Check the Network tab in DevTools for requests to verifyMpesaPayment');
  console.log('Look for POST requests to /api/mpesa/verify or similar endpoints');
}

// Function to bypass the normal verification process
async function bypassVerification(referenceCode, mpesaCode) {
  console.log('=== ATTEMPTING VERIFICATION BYPASS ===');
  
  try {
    // This assumes your verifyMpesaPayment function is accessible globally
    // If it's not, this won't work and you'll need to modify your code
    if (typeof window.verifyMpesaPayment === 'function') {
      console.log('Found verifyMpesaPayment function, attempting direct call...');
      
      const result = await window.verifyMpesaPayment({
        reference_code: referenceCode,
        mpesa_code: mpesaCode
      });
      
      console.log('Verification result:', result);
      return result;
    } else {
      console.log('verifyMpesaPayment function not found in global scope');
      
      // Try to find the form and submit it programmatically
      const form = document.querySelector('form');
      if (form) {
        console.log('Found form, attempting to submit...');
        form.dispatchEvent(new Event('submit', { bubbles: true, cancelable: true }));
        console.log('Form submitted programmatically');
      } else {
        console.log('Form not found');
      }
    }
  } catch (error) {
    console.error('Error in bypass verification:', error);
  }
}

// =============================================
// Test M-Pesa Codes
// =============================================

const testMpesaCodes = [
  'ABC123DEFG',  // Original test code
  'XYZ456MNOP',  // Original test code
  'QWE789RTYU',  // Original test code
  'ZXC012VBNM',  // Original test code
  'MPESA12345',  // Additional test code with MPESA prefix
  'M1P2E3S4A5',  // Additional test code with M-Pesa-like pattern
  '1234567890',  // Numeric only code
  'ABCDEFGHIJ'   // Alphabetic only code
];

// =============================================
// Debug Actions
// =============================================

// Function to try all test codes sequentially
async function tryAllTestCodes() {
  console.log('=== TRYING ALL TEST CODES ===');
  
  const details = logVerificationDetails();
  const referenceCode = details.referenceCode;
  
  for (let i = 0; i < testMpesaCodes.length; i++) {
    const code = testMpesaCodes[i];
    console.log(`Trying code ${i+1}/${testMpesaCodes.length}: ${code}`);
    
    // Fill the input
    const codeInput = document.querySelector('input[name="mpesa_code"]');
    if (codeInput) {
      codeInput.value = code;
      codeInput.dispatchEvent(new Event('input', { bubbles: true }));
      
      // Wait a bit before submitting
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // Click the submit button
      const submitButton = document.querySelector('button[type="submit"]');
      if (submitButton && !submitButton.disabled) {
        submitButton.click();
        console.log(`Submitted form with code: ${code}`);
        
        // Wait for response
        await new Promise(resolve => setTimeout(resolve, 5000));
        
        // Check if we're still on the verification page
        const stillOnVerificationPage = document.querySelector('input[name="mpesa_code"]') !== null;
        if (!stillOnVerificationPage) {
          console.log(`Success! Code ${code} was accepted.`);
          return true;
        } else {
          console.log(`Code ${code} was rejected or verification is still processing.`);
        }
      } else {
        console.log('Submit button not found or disabled');
      }
    } else {
      console.log('M-Pesa code input not found');
      break;
    }
    
    // Wait between attempts
    await new Promise(resolve => setTimeout(resolve, 2000));
  }
  
  console.log('All codes tried without success.');
  return false;
}

// Function to check Supabase connection
function checkSupabaseConnection() {
  console.log('=== CHECKING SUPABASE CONNECTION ===');
  
  // This assumes your Supabase client is accessible globally
  if (typeof window.supabase !== 'undefined') {
    console.log('Supabase client found in global scope');
    
    // Try a simple query to check connection
    window.supabase
      .from('client_projects')
      .select('count')
      .limit(1)
      .then(response => {
        console.log('Supabase connection test result:', response);
        if (response.error) {
          console.error('Supabase connection error:', response.error);
        } else {
          console.log('Supabase connection successful');
        }
      })
      .catch(error => {
        console.error('Error testing Supabase connection:', error);
      });
  } else {
    console.log('Supabase client not found in global scope');
  }
}

// =============================================
// Usage Instructions
// =============================================

console.log('=== PAYMENT VERIFICATION DEBUG SCRIPT LOADED ===');
console.log('To debug payment verification issues, run:');
console.log('1. logVerificationDetails() - Get detailed info about the current verification state');
console.log('2. monitorNetworkRequests() - Instructions for monitoring network requests');
console.log('3. tryAllTestCodes() - Try all test codes sequentially');
console.log('4. checkSupabaseConnection() - Check if Supabase connection is working');
console.log('5. bypassVerification(referenceCode, mpesaCode) - Try to bypass normal verification');
