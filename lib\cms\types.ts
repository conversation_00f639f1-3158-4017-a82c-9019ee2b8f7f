export interface PortfolioContent {
  id: string;
  section: string;
  key: string;
  content: string;
  created_at: string;
  updated_at: string;
}

export interface PortfolioImage {
  id: string;
  name: string;
  description: string | null;
  alt_text: string;
  storage_path: string;
  public_url: string;
  type: string;
  created_at: string;
  updated_at: string;
}

export type ContentSection = 'hero' | 'about' | 'projects' | 'skills' | 'contact' | 'general';

export type ContentKey =
  // Hero section
  | 'title'
  | 'subtitle'
  | 'description'

  // About section
  | 'bio'
  | 'education'
  | 'experience'

  // Projects section
  | 'project_intro'
  | 'project_1_title'
  | 'project_1_description'
  | 'project_1_tags'
  | 'project_1_github'
  | 'project_1_demo'
  | 'project_2_title'
  | 'project_2_description'
  | 'project_2_tags'
  | 'project_2_github'
  | 'project_2_demo'
  | 'project_3_title'
  | 'project_3_description'
  | 'project_3_tags'
  | 'project_3_github'
  | 'project_3_demo'
  | 'project_4_title'
  | 'project_4_description'
  | 'project_4_tags'
  | 'project_4_github'
  | 'project_4_demo'

  // Skills section
  | 'skills_intro'
  | 'frontend_skills'
  | 'backend_skills'
  | 'other_skills'

  // Contact section
  | 'contact_intro'
  | 'email'
  | 'linkedin'
  | 'github'

  // General
  | 'site_title'
  | 'site_description'
  | 'meta_keywords';

export type ImageType = 'profile' | 'project' | 'background' | 'logo' | 'other';

export interface CreateContentPayload {
  section: ContentSection;
  key: ContentKey;
  content: string;
}

export interface UpdateContentPayload {
  content: string;
}

export interface CreateImagePayload {
  name: string;
  description?: string;
  alt_text: string;
  type: ImageType;
  file: File;
}

export interface UpdateImagePayload {
  name?: string;
  description?: string;
  alt_text?: string;
  type?: ImageType;
  file?: File;
}
