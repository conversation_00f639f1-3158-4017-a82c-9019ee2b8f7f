'use client';

import { useEffect } from 'react';

interface SmoothScrollOptions {
  offset?: number;
  duration?: number;
  easing?: (t: number) => number;
}

// Easing functions
const easeInOutQuad = (t: number): number => {
  return t < 0.5 ? 2 * t * t : 1 - Math.pow(-2 * t + 2, 2) / 2;
};

const easeOutQuint = (t: number): number => {
  return 1 - Math.pow(1 - t, 5);
};

export function useSmoothScroll(options: SmoothScrollOptions = {}) {
  const {
    offset = 0,
    duration = 800,
    easing = easeOutQuint
  } = options;

  useEffect(() => {
    // Handle anchor links for smooth scrolling
    const handleAnchorClick = (e: MouseEvent) => {
      const target = e.target as HTMLElement;
      const anchor = target.closest('a');

      if (!anchor) return;

      const href = anchor.getAttribute('href');

      // Only process anchor links
      if (!href || !href.startsWith('#')) return;

      // Prevent default behavior
      e.preventDefault();

      // Get the target element
      const targetId = href.substring(1);
      const targetElement = document.getElementById(targetId);

      if (!targetElement) return;

      // Get the target position
      const targetPosition = targetElement.getBoundingClientRect().top + window.scrollY - offset;
      const startPosition = window.scrollY;
      const distance = targetPosition - startPosition;

      let startTime: number | null = null;

      // Optimized animation function
      const animateScroll = (currentTime: number) => {
        if (startTime === null) startTime = currentTime;
        const timeElapsed = currentTime - startTime;
        const progress = Math.min(timeElapsed / duration, 1);
        const easedProgress = easing(progress);

        // Use optimized scrolling
        const newPosition = startPosition + distance * easedProgress;
        window.scrollTo({ top: newPosition, behavior: 'auto' });

        if (timeElapsed < duration) {
          requestAnimationFrame(animateScroll);
        }
      };

      // Start animation
      requestAnimationFrame(animateScroll);
    };

    // Add event listener with passive option for better performance
    document.addEventListener('click', handleAnchorClick, { passive: false });

    // Clean up
    return () => {
      document.removeEventListener('click', handleAnchorClick);
    };
  }, [offset, duration, easing]);

  // Function to programmatically scroll to an element
  const scrollToElement = (elementId: string) => {
    const targetElement = document.getElementById(elementId);

    if (!targetElement) return;

    const targetPosition = targetElement.getBoundingClientRect().top + window.scrollY - offset;
    const startPosition = window.scrollY;
    const distance = targetPosition - startPosition;

    let startTime: number | null = null;

    // Optimized animation function
    const animateScroll = (currentTime: number) => {
      if (startTime === null) startTime = currentTime;
      const timeElapsed = currentTime - startTime;
      const progress = Math.min(timeElapsed / duration, 1);
      const easedProgress = easing(progress);

      // Use optimized scrolling
      const newPosition = startPosition + distance * easedProgress;
      window.scrollTo({ top: newPosition, behavior: 'auto' });

      if (timeElapsed < duration) {
        requestAnimationFrame(animateScroll);
      }
    };

    // Start animation
    requestAnimationFrame(animateScroll);
  };

  return { scrollToElement };
}
