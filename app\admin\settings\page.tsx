"use client";

import { useState, useEffect } from "react";
import { toast } from "sonner";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { PortfolioContent } from "@/lib/cms/types";

export default function SettingsPage() {
  const [siteTitle, setSiteTitle] = useState("");
  const [siteDescription, setSiteDescription] = useState("");
  const [metaKeywords, setMetaKeywords] = useState("");
  const [isLoading, setIsLoading] = useState(true);
  const [isSaving, setIsSaving] = useState(false);

  useEffect(() => {
    const fetchSettings = async () => {
      try {
        // Fetch all general content at once
        const generalContentRes = await fetch("/api/cms/content?section=general");

        if (generalContentRes.ok) {
          const generalContent: PortfolioContent[] = await generalContentRes.json();

          // Find each setting in the returned data
          const titleContent = generalContent.find(item => item.key === "site_title");
          if (titleContent) {
            setSiteTitle(titleContent.content);
          }

          const descContent = generalContent.find(item => item.key === "site_description");
          if (descContent) {
            setSiteDescription(descContent.content);
          }

          const keywordsContent = generalContent.find(item => item.key === "meta_keywords");
          if (keywordsContent) {
            setMetaKeywords(keywordsContent.content);
          }
        } else if (generalContentRes.status === 401) {
          // Handle unauthorized
          toast.error("You must be logged in to view settings");
        } else {
          // Handle other errors
          console.error("Error fetching general content:", generalContentRes.status);
          toast.error("Failed to load settings");
        }
      } catch (error) {
        console.error("Error fetching settings:", error);
        toast.error("Failed to load settings");
      } finally {
        setIsLoading(false);
      }
    };

    fetchSettings();
  }, []);

  const saveContent = async (section: string, key: string, content: string): Promise<boolean> => {
    try {
      // First, try to get all content for this section to check if it exists
      const allContentRes = await fetch(`/api/cms/content?section=${section}`);

      if (!allContentRes.ok) {
        if (allContentRes.status === 401) {
          throw new Error("You must be logged in to save settings");
        }
        throw new Error(`Failed to fetch content: ${allContentRes.status}`);
      }

      const allContent: PortfolioContent[] = await allContentRes.json();

      // Find the content item with the matching key
      const existingContent = allContent.find(item => item.key === key);

      if (existingContent) {
        // Content exists, update it
        console.log(`Updating existing content: ${section}.${key} with ID ${existingContent.id}`);

        const updateRes = await fetch(`/api/cms/content/${existingContent.id}`, {
          method: "PUT",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({ content }),
        });

        if (!updateRes.ok) {
          const errorData = await updateRes.json().catch(() => ({ error: `Server error: ${updateRes.status}` }));
          throw new Error(errorData.error || `Failed to update ${key}`);
        }

        return true;
      } else {
        // Content doesn't exist, create it
        console.log(`Creating new content: ${section}.${key}`);

        const createRes = await fetch("/api/cms/content", {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            section,
            key,
            content,
          }),
        });

        if (!createRes.ok) {
          const errorData = await createRes.json().catch(() => ({ error: `Server error: ${createRes.status}` }));
          throw new Error(errorData.error || `Failed to create ${key}`);
        }

        return true;
      }
    } catch (error: any) {
      console.error(`Error saving ${key}:`, error);
      toast.error(error.message || `Failed to save ${key}`);
      return false;
    }
  };

  const handleSaveSettings = async () => {
    setIsSaving(true);

    try {
      // Validate inputs
      if (!siteTitle.trim()) {
        toast.error("Site title cannot be empty");
        setIsSaving(false);
        return;
      }

      // Save settings one at a time to better handle errors
      let successCount = 0;
      let totalSettings = 3; // Total number of settings we're saving

      // Save site title
      console.log("Saving site title...");
      const titleResult = await saveContent("general", "site_title", siteTitle);
      if (titleResult) successCount++;

      // Save site description
      console.log("Saving site description...");
      const descResult = await saveContent("general", "site_description", siteDescription);
      if (descResult) successCount++;

      // Save meta keywords
      console.log("Saving meta keywords...");
      const keywordsResult = await saveContent("general", "meta_keywords", metaKeywords);
      if (keywordsResult) successCount++;

      // Show appropriate message based on how many settings were saved
      if (successCount === totalSettings) {
        toast.success("All settings saved successfully");
      } else if (successCount > 0) {
        toast.warning(`Saved ${successCount} out of ${totalSettings} settings`);
      } else {
        toast.error("Failed to save any settings");
      }
    } catch (error: any) {
      console.error("Error saving settings:", error);
      toast.error(error.message || "Failed to save settings");
    } finally {
      setIsSaving(false);
    }
  };

  if (isLoading) {
    return (
      <div className="flex justify-center py-8">
        <div className="h-8 w-8 animate-spin rounded-full border-b-2 border-t-2 border-blue-500"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-3xl font-bold">Settings</h1>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>SEO Settings</CardTitle>
          <CardDescription>
            Configure your portfolio's SEO settings
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="siteTitle">Site Title</Label>
            <Input
              id="siteTitle"
              value={siteTitle}
              onChange={(e) => setSiteTitle(e.target.value)}
              placeholder="Your portfolio title"
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="siteDescription">Site Description</Label>
            <Textarea
              id="siteDescription"
              value={siteDescription}
              onChange={(e) => setSiteDescription(e.target.value)}
              placeholder="A brief description of your portfolio"
              rows={3}
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="metaKeywords">Meta Keywords</Label>
            <Input
              id="metaKeywords"
              value={metaKeywords}
              onChange={(e) => setMetaKeywords(e.target.value)}
              placeholder="Comma-separated keywords"
            />
            <p className="text-xs text-gray-500">
              Separate keywords with commas (e.g., web development, design, portfolio)
            </p>
          </div>

          <Button
            onClick={handleSaveSettings}
            disabled={isSaving}
          >
            {isSaving ? "Saving..." : "Save Settings"}
          </Button>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>Help & Resources</CardTitle>
          <CardDescription>
            Learn how to use the CMS effectively
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="rounded-md bg-gray-100 p-4 dark:bg-gray-800">
            <h3 className="mb-2 font-medium">Content Management</h3>
            <p className="text-sm text-gray-500">
              Use the Content section to manage text content for your portfolio.
              Each content item belongs to a section (like Hero or About) and has a unique key.
            </p>
          </div>

          <div className="rounded-md bg-gray-100 p-4 dark:bg-gray-800">
            <h3 className="mb-2 font-medium">Image Management</h3>
            <p className="text-sm text-gray-500">
              Use the Images section to upload and manage images for your portfolio.
              Make sure to provide descriptive alt text for accessibility.
            </p>
          </div>

          <div className="rounded-md bg-gray-100 p-4 dark:bg-gray-800">
            <h3 className="mb-2 font-medium">Profile Picture</h3>
            <p className="text-sm text-gray-500">
              To update your profile picture, upload a new image with the type "profile".
              The most recent profile image will be used on your portfolio.
            </p>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
