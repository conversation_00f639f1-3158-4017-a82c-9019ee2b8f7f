'use client';

import { useState, useEffect, useRef } from 'react';

interface TextAnimationOptions {
  text: string;
  typingSpeed?: number;
  startDelay?: number;
  loop?: boolean;
  loopDelay?: number;
}

export function useTextAnimation({
  text,
  typingSpeed = 100,
  startDelay = 500,
  loop = false,
  loopDelay = 2000
}: TextAnimationOptions) {
  const [displayedText, setDisplayedText] = useState('');
  const [isComplete, setIsComplete] = useState(false);
  const intervalRef = useRef<NodeJS.Timeout | null>(null);
  const timeoutRef = useRef<NodeJS.Timeout | null>(null);

  useEffect(() => {
    // Function to start typing animation
    const startTyping = () => {
      let currentIndex = 0;
      setDisplayedText('');
      setIsComplete(false);

      // Clear any existing intervals
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
      }

      // Set up new interval for typing
      intervalRef.current = setInterval(() => {
        if (currentIndex < text.length) {
          setDisplayedText(text.substring(0, currentIndex + 1));
          currentIndex++;
        } else {
          if (intervalRef.current) {
            clearInterval(intervalRef.current);
            intervalRef.current = null;
          }
          setIsComplete(true);

          // If looping is enabled, restart after delay
          if (loop) {
            timeoutRef.current = setTimeout(() => {
              startTyping();
            }, loopDelay);
          }
        }
      }, typingSpeed);
    };

    // Initial delay before starting the animation
    timeoutRef.current = setTimeout(startTyping, startDelay);

    // Cleanup function
    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
      }
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
    };
  }, [text, typingSpeed, startDelay, loop, loopDelay]);

  // Function to manually restart the animation
  const restart = () => {
    if (intervalRef.current) {
      clearInterval(intervalRef.current);
    }
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
    }

    setDisplayedText('');
    setIsComplete(false);

    timeoutRef.current = setTimeout(() => {
      let currentIndex = 0;

      intervalRef.current = setInterval(() => {
        if (currentIndex < text.length) {
          setDisplayedText(text.substring(0, currentIndex + 1));
          currentIndex++;
        } else {
          if (intervalRef.current) {
            clearInterval(intervalRef.current);
            intervalRef.current = null;
          }
          setIsComplete(true);
        }
      }, typingSpeed);
    }, startDelay);
  };

  return { displayedText, isComplete, restart };
}
