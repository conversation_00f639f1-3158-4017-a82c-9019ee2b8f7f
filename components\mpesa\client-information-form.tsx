'use client';

import { useState } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { motion } from 'framer-motion';
import { ClientInformationFormData, clientInformationSchema } from '@/lib/mpesa/types';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage, FormDescription } from '@/components/ui/form';
import { CheckCircle, AlertCircle, Mail, Phone } from 'lucide-react';

interface ClientInformationFormProps {
  onSubmit: (data: ClientInformationFormData) => void;
  onBack: () => void;
}

export default function ClientInformationForm({ onSubmit, onBack }: ClientInformationFormProps) {
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Initialize form with react-hook-form and zod validation
  const form = useForm<ClientInformationFormData>({
    resolver: zodResolver(clientInformationSchema),
    mode: 'onChange', // Enable real-time validation
    defaultValues: {
      client_name: '',
      client_email: '',
      client_phone: '',
    },
  });

  // Watch form fields for real-time validation feedback
  const emailValue = form.watch('client_email');
  const nameValue = form.watch('client_name');
  const phoneValue = form.watch('client_phone');

  const emailError = form.formState.errors.client_email;
  const nameError = form.formState.errors.client_name;
  const phoneError = form.formState.errors.client_phone;

  const isEmailValid = emailValue && !emailError && emailValue.length > 0;
  const isNameValid = nameValue && !nameError && nameValue.length > 0;
  const isPhoneValid = phoneValue && !phoneError && phoneValue.length > 0;

  // Handle form submission
  const handleSubmit = (data: ClientInformationFormData) => {
    setIsSubmitting(true);

    try {
      onSubmit(data);
    } catch (error) {
      console.error('Error submitting client information:', error);
      setIsSubmitting(false);
    }
  };

  return (
    <motion.div
      initial={{ opacity: 0, x: 50 }}
      animate={{ opacity: 1, x: 0 }}
      exit={{ opacity: 0, x: -50 }}
      transition={{ duration: 0.5 }}
      className="w-full max-w-2xl mx-auto"
    >
      <h2 className="text-2xl font-bold text-center mb-4 sm:mb-6">Your Information</h2>

      <Form {...form}>
        <form onSubmit={form.handleSubmit(handleSubmit)} className="mobile-spacing">
          <FormField
            control={form.control}
            name="client_name"
            render={({ field }) => (
              <FormItem>
                <FormLabel className="text-base flex items-center gap-2">
                  Full Name
                  {isNameValid && (
                    <CheckCircle className="h-4 w-4 text-green-500" />
                  )}
                  {nameError && nameValue && (
                    <AlertCircle className="h-4 w-4 text-red-500" />
                  )}
                </FormLabel>
                <FormControl>
                  <motion.div
                    whileHover={{ scale: 1.01 }}
                    transition={{ duration: 0.2 }}
                    className="relative"
                  >
                    <Input
                      placeholder="Enter your full name (e.g., John Doe)"
                      {...field}
                      className={`mobile-input rounded-md border bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 focus:outline-none transition-colors ${
                        nameError && nameValue
                          ? 'border-red-500 focus:border-red-500 dark:border-red-500'
                          : isNameValid
                          ? 'border-green-500 focus:border-green-500 dark:border-green-500'
                          : 'border-gray-300 dark:border-gray-700 focus:border-blue-600 dark:focus:border-blue-400'
                      }`}
                      autoComplete="name"
                    />
                    {isNameValid && (
                      <motion.div
                        initial={{ opacity: 0, scale: 0.8 }}
                        animate={{ opacity: 1, scale: 1 }}
                        className="absolute right-3 top-1/2 transform -translate-y-1/2"
                      >
                        <CheckCircle className="h-5 w-5 text-green-500" />
                      </motion.div>
                    )}
                  </motion.div>
                </FormControl>
                <FormMessage className="text-sm" />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="client_email"
            render={({ field }) => (
              <FormItem>
                <FormLabel className="text-base flex items-center gap-2">
                  <Mail className="h-4 w-4" />
                  Email Address
                  {isEmailValid && (
                    <CheckCircle className="h-4 w-4 text-green-500" />
                  )}
                  {emailError && emailValue && (
                    <AlertCircle className="h-4 w-4 text-red-500" />
                  )}
                </FormLabel>
                <FormControl>
                  <motion.div
                    whileHover={{ scale: 1.01 }}
                    transition={{ duration: 0.2 }}
                    className="relative"
                  >
                    <Input
                      type="email"
                      placeholder="Enter your email address (e.g., <EMAIL>)"
                      {...field}
                      className={`mobile-input rounded-md border bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 focus:outline-none transition-colors ${
                        emailError && emailValue
                          ? 'border-red-500 focus:border-red-500 dark:border-red-500'
                          : isEmailValid
                          ? 'border-green-500 focus:border-green-500 dark:border-green-500'
                          : 'border-gray-300 dark:border-gray-700 focus:border-blue-600 dark:focus:border-blue-400'
                      }`}
                      autoComplete="email"
                      spellCheck="false"
                    />
                    {isEmailValid && (
                      <motion.div
                        initial={{ opacity: 0, scale: 0.8 }}
                        animate={{ opacity: 1, scale: 1 }}
                        className="absolute right-3 top-1/2 transform -translate-y-1/2"
                      >
                        <CheckCircle className="h-5 w-5 text-green-500" />
                      </motion.div>
                    )}
                  </motion.div>
                </FormControl>
                <FormDescription className="text-sm text-muted-foreground">
                  We'll use this email to send you project updates and important information.
                </FormDescription>
                <FormMessage className="text-sm" />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="client_phone"
            render={({ field }) => (
              <FormItem>
                <FormLabel className="text-base flex items-center gap-2">
                  <Phone className="h-4 w-4" />
                  Phone Number
                  {isPhoneValid && (
                    <CheckCircle className="h-4 w-4 text-green-500" />
                  )}
                  {phoneError && phoneValue && (
                    <AlertCircle className="h-4 w-4 text-red-500" />
                  )}
                </FormLabel>
                <FormControl>
                  <motion.div
                    whileHover={{ scale: 1.01 }}
                    transition={{ duration: 0.2 }}
                    className="relative"
                  >
                    <Input
                      type="tel"
                      placeholder="Enter your phone number (e.g., 0712345678)"
                      {...field}
                      className={`mobile-input rounded-md border bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 focus:outline-none transition-colors ${
                        phoneError && phoneValue
                          ? 'border-red-500 focus:border-red-500 dark:border-red-500'
                          : isPhoneValid
                          ? 'border-green-500 focus:border-green-500 dark:border-green-500'
                          : 'border-gray-300 dark:border-gray-700 focus:border-blue-600 dark:focus:border-blue-400'
                      }`}
                      autoComplete="tel"
                    />
                    {isPhoneValid && (
                      <motion.div
                        initial={{ opacity: 0, scale: 0.8 }}
                        animate={{ opacity: 1, scale: 1 }}
                        className="absolute right-3 top-1/2 transform -translate-y-1/2"
                      >
                        <CheckCircle className="h-5 w-5 text-green-500" />
                      </motion.div>
                    )}
                  </motion.div>
                </FormControl>
                <FormDescription className="text-sm text-muted-foreground">
                  We'll use this number for project communication and M-Pesa payment verification.
                </FormDescription>
                <FormMessage className="text-sm" />
              </FormItem>
            )}
          />

          <div className="flex flex-col sm:flex-row justify-between gap-4 pt-4">
            <Button
              type="button"
              variant="outline"
              onClick={onBack}
              disabled={isSubmitting}
              className="mobile-button order-2 sm:order-1"
            >
              Back
            </Button>

            <motion.div
              whileHover={{ scale: 1.03 }}
              whileTap={{ scale: 0.97 }}
              transition={{ duration: 0.2 }}
              className="w-full sm:w-auto order-1 sm:order-2"
            >
              <Button
                type="submit"
                disabled={isSubmitting || !form.formState.isValid}
                className={`mobile-button w-full transition-colors ${
                  form.formState.isValid && !isSubmitting
                    ? 'bg-blue-600 hover:bg-blue-700 dark:bg-blue-500 dark:hover:bg-blue-600'
                    : 'bg-gray-400 dark:bg-gray-600 cursor-not-allowed'
                }`}
              >
                {isSubmitting ? (
                  <div className="flex items-center gap-2">
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                    Processing...
                  </div>
                ) : form.formState.isValid ? (
                  <div className="flex items-center gap-2">
                    <CheckCircle className="h-4 w-4" />
                    Continue to Confirmation
                  </div>
                ) : (
                  'Please fill all fields correctly'
                )}
              </Button>
            </motion.div>
          </div>
        </form>
      </Form>
    </motion.div>
  );
}
