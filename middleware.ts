import { NextRequest, NextResponse } from 'next/server';
import { getToken } from 'next-auth/jwt';

export async function middleware(request: NextRequest) {
  const { pathname } = request.nextUrl;

  console.log(`Middleware triggered for: ${pathname}`);

  // Apply security headers to all admin routes
  const response = NextResponse.next();
  if (pathname.startsWith('/admin')) {
    response.headers.set('X-Frame-Options', 'DENY');
    response.headers.set('X-Content-Type-Options', 'nosniff');
    response.headers.set('Referrer-Policy', 'strict-origin-when-cross-origin');
    response.headers.set('X-XSS-Protection', '1; mode=block');
  }

  // Allow access to login page
  if (pathname === '/admin/login') {
    console.log('Login page - allowing access');
    return response;
  }

  // Check authentication for admin routes
  if (pathname === '/admin' || pathname.startsWith('/admin/')) {
    console.log('Admin route detected, checking authentication...');

    try {
      const token = await getToken({
        req: request,
        secret: process.env.NEXTAUTH_SECRET
      });

      console.log(`Token found: ${!!token}`);

      if (!token) {
        console.log('No token found, redirecting to login');
        const loginUrl = new URL('/admin/login', request.url);
        return NextResponse.redirect(loginUrl);
      }

      // Check token expiry
      if (token.exp && Date.now() >= token.exp * 1000) {
        console.log('Token expired, redirecting to login');
        const loginUrl = new URL('/admin/login', request.url);
        return NextResponse.redirect(loginUrl);
      }

      console.log('Authentication successful, allowing access');
      return response;
    } catch (error) {
      console.error('Authentication error:', error);
      const loginUrl = new URL('/admin/login', request.url);
      return NextResponse.redirect(loginUrl);
    }
  }

  // Check authentication for CMS API routes
  if (pathname.startsWith('/api/cms/')) {
    console.log('CMS API route detected, checking authentication...');

    try {
      const token = await getToken({
        req: request,
        secret: process.env.NEXTAUTH_SECRET
      });

      if (!token) {
        console.log('No token found for API route, returning 401');
        return NextResponse.json(
          { error: 'Unauthorized' },
          { status: 401 }
        );
      }

      // Check token expiry
      if (token.exp && Date.now() >= token.exp * 1000) {
        console.log('Token expired for API route, returning 401');
        return NextResponse.json(
          { error: 'Token expired' },
          { status: 401 }
        );
      }

      return response;
    } catch (error) {
      console.error('API authentication error:', error);
      return NextResponse.json(
        { error: 'Authentication failed' },
        { status: 401 }
      );
    }
  }

  return response;
}

export const config = {
  matcher: [
    '/admin',
    '/admin/:path*',
    '/api/cms/:path*',
  ],
};
