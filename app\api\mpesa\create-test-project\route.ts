import { NextResponse } from 'next/server';
import { createClient } from '@supabase/supabase-js';
import { generateReferenceCode } from '@/lib/mpesa/types';

// Create a Supabase client
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || '';
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || '';
const supabase = createClient(supabaseUrl, supabaseAnonKey);

export async function GET() {
  try {
    // Generate a unique reference code
    const referenceCode = generateReferenceCode();
    
    // Create a test client project
    const { data: projectData, error: projectError } = await supabase
      .from('client_projects')
      .insert([{
        client_name: 'Test Client',
        client_email: '<EMAIL>',
        client_phone: '0796564593',
        reference_code: referenceCode,
        package_type: 'basic',
        total_amount: 75000,
        deposit_amount: 30000,
        status: 'pending'
      }])
      .select();
    
    if (projectError) {
      console.error('Error creating test client project:', projectError);
      return NextResponse.json(
        {
          success: false,
          error: `Failed to create test client project: ${projectError.message}`
        },
        { status: 500 }
      );
    }
    
    return NextResponse.json({
      success: true,
      data: {
        client_project: projectData[0],
        reference_code: referenceCode,
        test_mpesa_codes: [
          'ABC123DEFG',
          'XYZ456MNOP',
          'QWE789RTYU',
          'ZXC012VBNM'
        ]
      }
    });
  } catch (error) {
    console.error('Error in create-test-project:', error);
    return NextResponse.json(
      {
        success: false,
        error: error instanceof Error ? error.message : 'An unexpected error occurred'
      },
      { status: 500 }
    );
  }
}
