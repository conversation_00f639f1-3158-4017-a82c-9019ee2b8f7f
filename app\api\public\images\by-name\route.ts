import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@supabase/supabase-js';

// Create a Supabase client for public routes
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || '';
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || '';
const supabase = createClient(supabaseUrl, supabaseAnonKey);

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const name = searchParams.get('name');
    const debug = searchParams.get('debug') === 'true';

    if (!name) {
      return NextResponse.json(
        { error: 'Name parameter is required' },
        { status: 400 }
      );
    }

    console.log(`[by-name API] Searching for image with name: "${name}"`);

    // Get all project images first
    const { data: allProjectImages, error: allImagesError } = await supabase
      .from('portfolio_images')
      .select('*')
      .eq('type', 'project')
      .order('created_at', { ascending: false });

    if (allImagesError) {
      console.error(`[by-name API] Error fetching all project images:`, allImagesError);
      return NextResponse.json(
        { error: `Failed to fetch images: ${allImagesError.message}` },
        { status: 500 }
      );
    }

    if (!allProjectImages || allProjectImages.length === 0) {
      console.log(`[by-name API] No project images found in the database`);
      return NextResponse.json(null);
    }

    console.log(`[by-name API] Found ${allProjectImages.length} project images in total`);

    // Log all image names for debugging
    if (debug) {
      console.log(`[by-name API] All project image names:`);
      allProjectImages.forEach(img => {
        console.log(`- ${img.name} (ID: ${img.id.substring(0, 8)}...)`);
      });
    }

    // Try different matching strategies

    // 1. Exact match (case insensitive)
    const exactMatch = allProjectImages.find(img =>
      img.name.toLowerCase() === name.toLowerCase()
    );

    if (exactMatch) {
      console.log(`[by-name API] Found exact match: "${exactMatch.name}"`);
      return NextResponse.json(exactMatch);
    }

    // 2. Project name is contained in image name
    const containedInImageName = allProjectImages.find(img =>
      img.name.toLowerCase().includes(name.toLowerCase())
    );

    if (containedInImageName) {
      console.log(`[by-name API] Found match where image name contains project name: "${containedInImageName.name}"`);
      return NextResponse.json(containedInImageName);
    }

    // 3. Image name is contained in project name
    const containsImageName = allProjectImages.find(img =>
      name.toLowerCase().includes(img.name.toLowerCase())
    );

    if (containsImageName) {
      console.log(`[by-name API] Found match where project name contains image name: "${containsImageName.name}"`);
      return NextResponse.json(containsImageName);
    }

    // 4. Word-level matching (more flexible)
    const projectWords = name.toLowerCase().split(/\s+/).filter(w => w.length > 2);

    if (projectWords.length > 0) {
      // Find images that have at least one matching word
      const wordMatches = allProjectImages.filter(img => {
        const imageWords = img.name.toLowerCase().split(/\s+/).filter(w => w.length > 2);
        return projectWords.some(pWord =>
          imageWords.some(iWord => iWord.includes(pWord) || pWord.includes(iWord))
        );
      });

      if (wordMatches.length > 0) {
        console.log(`[by-name API] Found ${wordMatches.length} word-level matches, using first: "${wordMatches[0].name}"`);
        return NextResponse.json(wordMatches[0]);
      }
    }

    // 5. Fallback to most recent project image
    console.log(`[by-name API] No matches found for "${name}", returning most recent project image: "${allProjectImages[0].name}"`);
    return NextResponse.json(allProjectImages[0]);

  } catch (error: any) {
    console.error('[by-name API] Error in public images by-name GET route:', error);
    return NextResponse.json(
      { error: error.message || 'Failed to fetch images' },
      { status: 500 }
    );
  }
}
