'use server';

import { createClient } from '@supabase/supabase-js';

// Create a Supabase client with service role key to bypass RLS
const supabaseAdmin = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL || '',
  process.env.SUPABASE_SERVICE_ROLE_KEY || ''
);

type TokenGenerationParams = {
  clientName: string;
  clientEmail: string;
  clientPhone: string;
  packageType: 'basic' | 'standard' | 'premium';
  referenceCode: string;
  totalAmount: number;
  depositAmount: number;
};

export async function generateAccessToken({
  clientName,
  clientEmail,
  clientPhone,
  packageType,
  referenceCode,
  totalAmount,
  depositAmount,
}: TokenGenerationParams) {
  try {
    // First create a client project
    const { data: projectData, error: projectError } = await supabaseAdmin
      .from('client_projects')
      .insert([{
        client_name: clientName,
        client_email: clientEmail,
        client_phone: clientPhone || '',
        package_type: packageType,
        reference_code: referenceCode,
        total_amount: totalAmount,
        deposit_amount: depositAmount,
        status: 'pending'
      }])
      .select()
      .single();

    if (projectError) {
      console.error('Error creating client project:', projectError);
      
      if (projectError.code === '23505') {
        return { success: false, error: 'A client with this email already exists' };
      }
      
      return { 
        success: false, 
        error: projectError.message || 'Failed to create client project' 
      };
    }

    // Generate a random access token with timestamp to ensure uniqueness
    const timestamp = Date.now().toString(36);
    const randomPart = Array.from(
      { length: 24 },
      () => Math.floor(Math.random() * 36).toString(36)
    ).join('');
    const accessToken = `${timestamp}-${randomPart}`;

    // Set expiration date (14 days from now)
    const expiresAt = new Date();
    expiresAt.setDate(expiresAt.getDate() + 14);

    // Create questionnaire access record
    const { data: accessData, error: accessError } = await supabaseAdmin
      .from('questionnaire_access')
      .insert([{
        client_project_id: projectData.id,
        access_token: accessToken,
        expires_at: expiresAt.toISOString()
      }])
      .select()
      .single();

    if (accessError) {
      console.error('Error generating questionnaire access token:', accessError);
      
      if (accessError.code === '23505') {
        return { success: false, error: 'Token generation conflict. Please try again.' };
      }
      
      return { 
        success: false, 
        error: accessError.message || 'Failed to generate access token' 
      };
    }

    return { 
      success: true, 
      accessToken, 
      expiresAt: expiresAt.toISOString() 
    };
  } catch (error) {
    console.error('Unexpected error in token generation:', error);
    return { 
      success: false, 
      error: 'An unexpected error occurred during token generation' 
    };
  }
}
