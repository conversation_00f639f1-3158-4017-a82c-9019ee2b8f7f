const crypto = require('crypto');

// Function to generate a password hash using PBKDF2
function generatePasswordHash(password) {
  // Generate a random salt
  const salt = crypto.randomBytes(16).toString('hex');

  // Hash the password with the salt using PBKDF2
  const hash = crypto.pbkdf2Sync(password, salt, 1000, 64, 'sha256').toString('hex');

  // Return the salt and hash combined
  return `${salt}:${hash}`;
}

// Using the provided secure password
const password = 'wasekaoloch04';

// Generate and print the hash
const passwordHash = generatePasswordHash(password);

console.log('\n--- COPY THIS HASH TO YOUR .ENV.LOCAL FILE ---\n');
console.log(passwordHash);
console.log('\n--- END OF HASH ---\n');

console.log('Add this to your .env.local file as ADMIN_PASSWORD_HASH');
