import { NextRequest, NextResponse } from 'next/server';
import {
  getImageById,
  updateImage,
  deleteImage
} from '@/lib/cms/supabase-cms';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    console.log(`GET request for image with id: ${params.id}`);

    const session = await getServerSession(authOptions);
    console.log(`Session status: ${session ? 'Authenticated' : 'Not authenticated'}`);

    // Check if user is authenticated for read operations
    if (!session) {
      console.error('Unauthorized access attempt');
      return NextResponse.json(
        { error: 'Unauthorized - Please log in to access this resource' },
        { status: 401 }
      );
    }

    const id = params.id;

    if (!id || typeof id !== 'string') {
      console.error(`Invalid image ID: ${id}`);
      return NextResponse.json(
        { error: 'Invalid image ID provided' },
        { status: 400 }
      );
    }

    console.log(`Fetching image with ID: ${id} from database`);
    const image = await getImageById(id);

    if (!image) {
      console.error(`Image with id ${id} not found`);
      return NextResponse.json(
        { error: `Image with id ${id} not found` },
        { status: 404 }
      );
    }

    console.log(`Successfully retrieved image: ${image.name}`);
    return NextResponse.json(image);
  } catch (error: any) {
    console.error(`Error in images GET[id] route:`, error);
    // Include more detailed error information
    return NextResponse.json(
      {
        error: error.message || 'Failed to fetch image',
        details: error.stack ? error.stack.split('\n')[0] : 'No additional details'
      },
      { status: 500 }
    );
  }
}

export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions);

    // Check if user is authenticated for write operations
    if (!session) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const id = params.id;
    const formData = await request.formData();

    const name = formData.get('name') as string;
    const description = formData.get('description') as string;
    const alt_text = formData.get('alt_text') as string;
    const type = formData.get('type') as string;
    const file = formData.get('file') as File | null;

    // At least one field must be provided
    if (!name && !description && !alt_text && !type && !file) {
      return NextResponse.json(
        { error: 'At least one field must be provided for update' },
        { status: 400 }
      );
    }

    // Log file details if present
    if (file) {
      console.log('Received file for update:', file.name, file.type, file.size);
    }

    const payload: any = {};
    if (name) payload.name = name;
    if (description !== undefined) payload.description = description;
    if (alt_text) payload.alt_text = alt_text;
    if (type) payload.type = type;
    if (file) payload.file = file;

    try {
      const image = await updateImage(id, payload);
      return NextResponse.json(image);
    } catch (updateError: any) {
      console.error('Error updating image in Supabase:', updateError);
      return NextResponse.json(
        { error: `Update failed: ${updateError.message}` },
        { status: 500 }
      );
    }
  } catch (error: any) {
    console.error(`Error in images PUT[id] route:`, error);
    return NextResponse.json(
      { error: error.message || 'Failed to update image' },
      { status: 500 }
    );
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions);

    // Check if user is authenticated for write operations
    if (!session) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const id = params.id;
    await deleteImage(id);

    return NextResponse.json(
      { message: 'Image deleted successfully' },
      { status: 200 }
    );
  } catch (error: any) {
    console.error(`Error in images DELETE[id] route:`, error);
    return NextResponse.json(
      { error: error.message || 'Failed to delete image' },
      { status: 500 }
    );
  }
}
