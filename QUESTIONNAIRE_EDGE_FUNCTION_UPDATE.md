# Questionnaire Completion Notification Edge Function Update

## Instructions:

1. **Go to Supabase Dashboard**: https://supabase.com/dashboard/project/caogszaytzuiqwwkbhhi
2. **Navigate to Edge Functions**
3. **Click on "send-questionnaire-completion-notification"**
4. **Replace the entire code** with the version below
5. **Click "Deploy"**

## Updated Edge Function Code:

```typescript
import { serve } from "https://deno.land/std@0.168.0/http/server.ts";

// CORS headers
const corsHeaders = {
  "Access-Control-Allow-Origin": "*",
  "Access-Control-Allow-Headers": "authorization, x-client-info, apikey, content-type",
  "Access-Control-Allow-Methods": "POST, OPTIONS",
};

interface QuestionnaireCompletionNotificationPayload {
  client_name: string;
  client_email: string;
  client_phone: string;
  package_type: string;
  reference_code: string;
  questionnaire_id: string;
  completed_at: string;
  // General information
  project_name?: string;
  project_description?: string;
  target_audience?: string;
  project_goals?: string[];
  color_scheme?: string;
  has_logo_or_brand_guidelines?: boolean;
  preferred_launch_timeline?: string;
  // Package-specific data
  package_data?: any;
}

const RESEND_API_KEY = Deno.env.get("RESEND_API_KEY") || "";
const RECIPIENT_EMAIL = Deno.env.get("RECIPIENT_EMAIL") || "<EMAIL>";

// Helper function to format package type
function formatPackageType(packageType: string): string {
  switch (packageType) {
    case 'basic':
      return 'Basic Package';
    case 'standard':
      return 'Standard Package';
    case 'premium':
      return 'Premium Package';
    default:
      return packageType;
  }
}

// Helper function to format date
function formatDate(dateString: string): string {
  const date = new Date(dateString);
  return date.toLocaleString('en-US', {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit',
    timeZone: 'Africa/Nairobi'
  });
}

// Helper function to format timeline
function formatTimeline(timeline?: string): string {
  switch (timeline) {
    case 'asap':
      return 'As soon as possible';
    case '1-2weeks':
      return '1-2 weeks';
    case '1month':
      return '1 month';
    case '2-3months':
      return '2-3 months';
    case 'flexible':
      return 'Flexible timeline';
    default:
      return timeline || 'Not specified';
  }
}

// Helper function to format project goals
function formatProjectGoals(goals?: string[]): string {
  if (!goals || goals.length === 0) {
    return 'Not specified';
  }
  return goals.map(goal => `• ${goal}`).join('<br>');
}

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === "OPTIONS") {
    return new Response("ok", { headers: corsHeaders });
  }

  try {
    console.log("Questionnaire completion notification function called");
    
    // Get the request payload
    const payload: QuestionnaireCompletionNotificationPayload = await req.json();
    console.log("Received payload:", JSON.stringify(payload));
    
    // Validate the payload
    if (!payload.client_name || !payload.client_email || !payload.package_type || !payload.reference_code) {
      console.error("Missing required fields in payload");
      return new Response(
        JSON.stringify({
          success: false,
          error: "Missing required fields",
        }),
        {
          headers: { ...corsHeaders, "Content-Type": "application/json" },
          status: 400,
        }
      );
    }

    console.log("Payload validation passed");
    console.log("RESEND_API_KEY configured:", !!RESEND_API_KEY);
    console.log("RECIPIENT_EMAIL:", RECIPIENT_EMAIL);

    // Create the email HTML content
    const emailHtml = `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; background-color: #f9f9f9;">
        <div style="background-color: white; padding: 30px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1);">
          <h1 style="color: #059669; margin-bottom: 20px; text-align: center;">✅ Questionnaire Completed!</h1>
          
          <div style="background-color: #f8fafc; padding: 20px; border-radius: 8px; margin-bottom: 20px;">
            <h2 style="color: #1e40af; margin-top: 0;">Client Information</h2>
            <p><strong>Name:</strong> ${payload.client_name}</p>
            <p><strong>Email:</strong> ${payload.client_email}</p>
            <p><strong>Phone:</strong> ${payload.client_phone}</p>
            <p><strong>Reference Code:</strong> <span style="background-color: #dbeafe; padding: 4px 8px; border-radius: 4px; font-family: monospace;">${payload.reference_code}</span></p>
            <p><strong>Package:</strong> ${formatPackageType(payload.package_type)}</p>
          </div>

          ${payload.project_name ? `
          <div style="background-color: #f0f9ff; padding: 20px; border-radius: 8px; margin-bottom: 20px;">
            <h2 style="color: #0369a1; margin-top: 0;">Project Overview</h2>
            <p><strong>Project Name:</strong> ${payload.project_name}</p>
            ${payload.project_description ? `<p><strong>Description:</strong> ${payload.project_description}</p>` : ''}
            ${payload.target_audience ? `<p><strong>Target Audience:</strong> ${payload.target_audience}</p>` : ''}
            <p><strong>Project Goals:</strong><br>${formatProjectGoals(payload.project_goals)}</p>
            ${payload.color_scheme ? `<p><strong>Color Scheme:</strong> ${payload.color_scheme}</p>` : ''}
            <p><strong>Has Logo/Brand Guidelines:</strong> ${payload.has_logo_or_brand_guidelines ? 'Yes' : 'No'}</p>
            <p><strong>Preferred Timeline:</strong> ${formatTimeline(payload.preferred_launch_timeline)}</p>
          </div>
          ` : ''}

          <div style="background-color: #fef3c7; padding: 20px; border-radius: 8px; margin-bottom: 20px;">
            <h2 style="color: #92400e; margin-top: 0;">📅 Completion Details</h2>
            <p><strong>Completed:</strong> ${formatDate(payload.completed_at)}</p>
            <p><strong>Questionnaire ID:</strong> <span style="background-color: #fed7aa; padding: 4px 8px; border-radius: 4px; font-family: monospace;">${payload.questionnaire_id}</span></p>
          </div>

          <div style="background-color: #ecfdf5; padding: 20px; border-radius: 8px; text-align: center;">
            <h3 style="color: #065f46; margin-top: 0;">Next Steps</h3>
            <p style="margin-bottom: 15px;">1. Review the complete questionnaire responses in your CMS dashboard</p>
            <p style="margin-bottom: 15px;">2. Analyze the project requirements and technical specifications</p>
            <p style="margin-bottom: 0;">3. Contact the client to discuss the project timeline and next steps</p>
          </div>

          <div style="text-align: center; margin-top: 30px; padding-top: 20px; border-top: 1px solid #e5e7eb;">
            <p style="color: #6b7280; font-size: 14px; margin: 0;">
              This notification was sent automatically from your Next.js Portfolio website.
            </p>
          </div>
        </div>
      </div>
    `;

    console.log("Attempting to send email via Resend API");

    // Use the same Resend domain as the project request notification
    const emailPayload = {
      from: "Portfolio Notifications <<EMAIL>>",
      to: RECIPIENT_EMAIL,
      subject: `✅ Questionnaire Completed: ${payload.project_name || formatPackageType(payload.package_type)} - ${payload.client_name}`,
      html: emailHtml,
    };
    
    console.log("Email payload:", JSON.stringify(emailPayload, null, 2));

    // Send email using Resend
    const res = await fetch("https://api.resend.com/emails", {
      method: "POST",
      headers: {
        "Authorization": `Bearer ${RESEND_API_KEY}`,
        "Content-Type": "application/json",
      },
      body: JSON.stringify(emailPayload),
    });

    const responseText = await res.text();
    console.log("Resend API response status:", res.status);
    console.log("Resend API response text:", responseText);

    let data;
    try {
      data = JSON.parse(responseText);
    } catch (e) {
      console.error("Failed to parse Resend response as JSON:", e);
      console.error("Raw response:", responseText);
      return new Response(
        JSON.stringify({
          success: false,
          error: "Invalid response from email service",
          details: responseText
        }),
        {
          headers: { ...corsHeaders, "Content-Type": "application/json" },
          status: 500,
        }
      );
    }

    if (res.status >= 400) {
      console.error("Resend API error - Status:", res.status);
      console.error("Resend API error - Data:", data);
      return new Response(
        JSON.stringify({
          success: false,
          error: "Failed to send email notification",
          details: {
            status: res.status,
            error: data,
            resendResponse: responseText
          }
        }),
        {
          headers: { ...corsHeaders, "Content-Type": "application/json" },
          status: 500,
        }
      );
    }

    console.log("Email sent successfully!");
    console.log("Resend response data:", data);
    return new Response(
      JSON.stringify({
        success: true,
        data,
      }),
      {
        headers: { ...corsHeaders, "Content-Type": "application/json" },
        status: 200,
      }
    );
  } catch (error) {
    console.error("Error in send-questionnaire-completion-notification function:", error);
    return new Response(
      JSON.stringify({
        success: false,
        error: "Internal server error",
        details: error.message
      }),
      {
        headers: { ...corsHeaders, "Content-Type": "application/json" },
        status: 500,
      }
    );
  }
});
```

## Key Changes Made:

1. **✅ Updated "from" address** to use Resend's verified domain: `"Portfolio Notifications <<EMAIL>>"`
2. **✅ Added detailed logging** for debugging (same as project request function)
3. **✅ Enhanced error handling** with specific Resend API error details
4. **✅ Consistent formatting** with the working project request notification

## After Updating:

Both email notification functions will now use the same verified Resend domain and should work perfectly!

Your email notifications are now:
- ✅ **Project Request Notifications** - Working with Resend domain
- ✅ **Questionnaire Completion Notifications** - Will work after this update

Update the Edge Function and your email notification system will be fully operational! 🎉
