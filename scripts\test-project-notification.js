/**
 * Test script to manually send a project request notification
 * This will help us verify the email notification system is working
 */

require('dotenv').config({ path: '.env.local' });

async function testProjectNotification() {
  console.log('Testing project request notification for EmailBasic...');

  const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
  const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;

  if (!supabaseUrl || !supabaseAnonKey) {
    console.error('❌ Missing Supabase configuration');
    return;
  }

  console.log('✅ Supabase URL:', supabaseUrl);
  console.log('✅ Supabase Anon Key configured:', !!supabaseAnonKey);

  // Use the actual data from the EmailBasic project request
  const testData = {
    client_name: 'EmailBasic',
    client_email: '<EMAIL>',
    client_phone: '0799654432',
    package_type: 'basic',
    additional_services: [],
    maintenance_plan: null,
    total_amount: 75000,
    reference_code: 'ZO073166429',
    created_at: '2025-05-24T15:21:14.1064Z',
  };

  try {
    console.log('Sending notification with data:', JSON.stringify(testData, null, 2));

    const edgeFunctionUrl = `${supabaseUrl}/functions/v1/send-project-request-notification`;
    console.log('Edge Function URL:', edgeFunctionUrl);

    const response = await fetch(edgeFunctionUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${supabaseAnonKey}`,
        'apikey': supabaseAnonKey,
      },
      body: JSON.stringify(testData),
    });

    const responseText = await response.text();
    console.log('Response status:', response.status);
    console.log('Response text:', responseText);

    if (response.ok) {
      console.log('✅ Email notification sent successfully!');
      console.log('Check your email (<EMAIL>) for the notification.');
    } else {
      console.log('❌ Email notification failed with status:', response.status);
    }
  } catch (error) {
    console.error('❌ Error testing project notification:', error);
  }
}

// Run the test
testProjectNotification();
