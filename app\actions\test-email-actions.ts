'use server';

import { sendProjectRequestNotification } from '@/lib/email-notifications';
import { createClient } from '@supabase/supabase-js';

// Create Supabase client for admin operations
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || '';
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY || '';
const supabaseAdmin = createClient(supabaseUrl, supabaseServiceKey);

// Test email notification for a specific project request
export async function testProjectRequestNotification(projectRequestId: string) {
  try {
    console.log('Testing email notification for project request:', projectRequestId);

    // Get the project request data
    const { data: projectRequest, error } = await supabaseAdmin
      .from('project_requests')
      .select('*')
      .eq('id', projectRequestId)
      .single();

    if (error || !projectRequest) {
      console.error('Error fetching project request:', error);
      return {
        success: false,
        error: `Project request not found: ${error?.message || 'Unknown error'}`,
      };
    }

    console.log('Found project request:', projectRequest);

    // Send email notification
    try {
      console.log('Sending email notification...');
      const emailResult = await sendProjectRequestNotification({
        client_name: projectRequest.client_name,
        client_email: projectRequest.client_email,
        client_phone: projectRequest.client_phone,
        package_type: projectRequest.package_type,
        additional_services: projectRequest.additional_services,
        maintenance_plan: projectRequest.maintenance_plan,
        total_amount: projectRequest.total_amount,
        reference_code: projectRequest.reference_code,
        created_at: projectRequest.created_at,
      });

      console.log('Email notification result:', emailResult);

      if (emailResult.success) {
        return {
          success: true,
          message: 'Email notification sent successfully!',
        };
      } else {
        console.error('Email notification failed with error:', emailResult.error);
        return {
          success: false,
          error: `Email notification failed: ${emailResult.error}`,
        };
      }
    } catch (emailError) {
      console.error('Error sending email notification:', emailError);
      return {
        success: false,
        error: `Email sending failed: ${emailError instanceof Error ? emailError.message : 'Unknown error'}`,
      };
    }
  } catch (error) {
    console.error('Error in testProjectRequestNotification:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'An unexpected error occurred',
    };
  }
}

// Test email notification for EmailBasic project (direct test without DB lookup)
export async function testEmailBasicNotification() {
  try {
    console.log('Testing email notification with hardcoded data...');

    // Use hardcoded data to bypass database issues
    const testData = {
      client_name: 'EmailBasic',
      client_email: '<EMAIL>',
      client_phone: '0799654432',
      package_type: 'basic' as const,
      additional_services: [] as string[],
      maintenance_plan: null,
      total_amount: 75000,
      reference_code: 'ZO073166429',
      created_at: '2025-05-24T15:21:14.1064Z',
    };

    console.log('Sending email notification with test data:', testData);

    const emailResult = await sendProjectRequestNotification(testData);
    console.log('Email notification result:', emailResult);

    if (emailResult.success) {
      return {
        success: true,
        message: 'Email notification sent successfully!',
      };
    } else {
      console.error('Email notification failed with error:', emailResult.error);
      return {
        success: false,
        error: `Email notification failed: ${emailResult.error}`,
      };
    }
  } catch (error) {
    console.error('Error in testEmailBasicNotification:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'An unexpected error occurred',
    };
  }
}

// Test email notification for EmailBasicTwo project (direct test without DB lookup)
export async function testEmailBasicTwoNotification() {
  try {
    console.log('Testing email notification with hardcoded data...');

    // Use hardcoded data to bypass database issues
    const testData = {
      client_name: 'EmailBasicTwo',
      client_email: '<EMAIL>',
      client_phone: '0796543432',
      package_type: 'basic' as const,
      additional_services: [] as string[],
      maintenance_plan: null,
      total_amount: 75000,
      reference_code: 'ZO270601267',
      created_at: '2025-05-24T15:24:31.175461Z',
    };

    console.log('Sending email notification with test data:', testData);

    const emailResult = await sendProjectRequestNotification(testData);
    console.log('Email notification result:', emailResult);

    if (emailResult.success) {
      return {
        success: true,
        message: 'Email notification sent successfully!',
      };
    } else {
      console.error('Email notification failed with error:', emailResult.error);
      return {
        success: false,
        error: `Email notification failed: ${emailResult.error}`,
      };
    }
  } catch (error) {
    console.error('Error in testEmailBasicTwoNotification:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'An unexpected error occurred',
    };
  }
}
