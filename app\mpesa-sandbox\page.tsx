'use client';

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { toast } from 'sonner';

export default function MpesaSandboxPage() {
  const [checkoutRequestId, setCheckoutRequestId] = useState('');
  const [merchantRequestId, setMerchantRequestId] = useState('');
  const [phoneNumber, setPhoneNumber] = useState('');
  const [amount, setAmount] = useState('');
  const [referenceCode, setReferenceCode] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [result, setResult] = useState<any>(null);

  const simulateCallback = async () => {
    if (!checkoutRequestId || !merchantRequestId) {
      toast.error('CheckoutRequestID and MerchantRequestID are required');
      return;
    }

    setIsLoading(true);

    try {
      const response = await fetch('/api/mpesa/sandbox', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          CheckoutRequestID: checkoutRequestId,
          MerchantRequestID: merchantRequestId,
          PhoneNumber: phoneNumber || undefined,
          Amount: amount ? Number(amount) : undefined,
          ReferenceCode: referenceCode || undefined,
        }),
      });

      const data = await response.json();
      setResult(data);

      if (data.success) {
        toast.success('Sandbox callback sent successfully');
      } else {
        toast.error(data.error || 'Failed to send sandbox callback');
      }
    } catch (error) {
      console.error('Error sending sandbox callback:', error);
      toast.error('An unexpected error occurred');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="container mx-auto py-10">
      <h1 className="text-3xl font-bold mb-6">M-Pesa Sandbox Testing</h1>
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle>Simulate STK Push Callback</CardTitle>
            <CardDescription>
              Use this form to simulate a successful M-Pesa STK Push callback.
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="checkout-request-id">Checkout Request ID *</Label>
                <Input
                  id="checkout-request-id"
                  value={checkoutRequestId}
                  onChange={(e) => setCheckoutRequestId(e.target.value)}
                  placeholder="e.g., ws_CO_DMZ_123456789_123456789"
                  required
                />
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="merchant-request-id">Merchant Request ID *</Label>
                <Input
                  id="merchant-request-id"
                  value={merchantRequestId}
                  onChange={(e) => setMerchantRequestId(e.target.value)}
                  placeholder="e.g., 123456-123456-1"
                  required
                />
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="phone-number">Phone Number (optional)</Label>
                <Input
                  id="phone-number"
                  value={phoneNumber}
                  onChange={(e) => setPhoneNumber(e.target.value)}
                  placeholder="e.g., 254712345678"
                />
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="amount">Amount (optional)</Label>
                <Input
                  id="amount"
                  type="number"
                  value={amount}
                  onChange={(e) => setAmount(e.target.value)}
                  placeholder="e.g., 1000"
                />
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="reference-code">Reference Code (optional)</Label>
                <Input
                  id="reference-code"
                  value={referenceCode}
                  onChange={(e) => setReferenceCode(e.target.value)}
                  placeholder="e.g., REF123456"
                />
              </div>
              
              <Button
                onClick={simulateCallback}
                disabled={isLoading || !checkoutRequestId || !merchantRequestId}
                className="w-full"
              >
                {isLoading ? 'Simulating...' : 'Simulate Callback'}
              </Button>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader>
            <CardTitle>Result</CardTitle>
            <CardDescription>
              The result of the simulated callback will appear here.
            </CardDescription>
          </CardHeader>
          <CardContent>
            {result ? (
              <pre className="bg-gray-100 dark:bg-gray-800 p-4 rounded overflow-auto max-h-96">
                {JSON.stringify(result, null, 2)}
              </pre>
            ) : (
              <p className="text-gray-500 dark:text-gray-400">No result yet. Simulate a callback to see the result.</p>
            )}
          </CardContent>
        </Card>
      </div>
      
      <div className="mt-8">
        <h2 className="text-2xl font-bold mb-4">How to Use</h2>
        <ol className="list-decimal list-inside space-y-2">
          <li>Initiate an STK Push using the payment flow in your application</li>
          <li>Copy the <strong>Checkout Request ID</strong> and <strong>Merchant Request ID</strong> from the console logs or network tab</li>
          <li>Paste them into the form above</li>
          <li>Click "Simulate Callback" to simulate a successful payment</li>
          <li>The payment should be automatically verified in your application</li>
        </ol>
      </div>
    </div>
  );
}
