import { z } from 'zod';

// Package types
export type PackageType = 'basic' | 'standard' | 'premium';

// Additional service type
export interface AdditionalService {
  id: string;
  name: string;
  price: number;
  description: string;
}

// Maintenance plan type
export interface MaintenancePlan {
  id: string;
  name: string;
  price: number;
  description: string;
}

// Client project type
export interface ClientProject {
  id: string;
  client_name: string;
  client_email: string;
  client_phone: string;
  reference_code: string;
  package_type: PackageType;
  total_amount: number;
  deposit_amount: number;
  status: 'pending' | 'verified' | 'completed' | 'cancelled';
  created_at: string;
  updated_at: string;
}

// Payment type
export interface Payment {
  id: string;
  client_project_id: string;
  mpesa_code: string;
  amount: number;
  payment_type: 'deposit' | 'final';
  verified: boolean;
  verification_date: string | null;
  created_at: string;
  updated_at: string;
}

// Questionnaire access type
export interface QuestionnaireAccess {
  id: string;
  client_project_id: string;
  access_token: string;
  expires_at: string;
  created_at: string;
}

// Package selection form schema
export const packageSelectionSchema = z.object({
  package_type: z.enum(['basic', 'standard', 'premium'], {
    required_error: 'Please select a package',
  }),
  additional_services: z.array(z.string()).optional(),
  maintenance_plan: z.string().nullable().optional(),
});

export type PackageSelectionFormData = z.infer<typeof packageSelectionSchema>;

// Enhanced email validation function
const validateEmail = (email: string) => {
  // Basic email format check
  const emailRegex = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;

  if (!email) {
    return 'Email address is required';
  }

  if (email.length < 5) {
    return 'Email address is too short';
  }

  if (email.length > 254) {
    return 'Email address is too long';
  }

  if (!emailRegex.test(email)) {
    return 'Please enter a valid email address (e.g., <EMAIL>)';
  }

  // Check for common typos in domain
  const domain = email.split('@')[1]?.toLowerCase();
  if (domain) {
    const commonDomainTypos = {
      'gmial.com': 'gmail.com',
      'gmai.com': 'gmail.com',
      'gmeil.com': 'gmail.com',
      'yahooo.com': 'yahoo.com',
      'yaho.com': 'yahoo.com',
      'hotmial.com': 'hotmail.com',
      'hotmeil.com': 'hotmail.com',
      'outlok.com': 'outlook.com',
      'outloo.com': 'outlook.com'
    };

    if (commonDomainTypos[domain]) {
      return `Did you mean ${email.replace(domain, commonDomainTypos[domain])}?`;
    }
  }

  // Check for consecutive dots
  if (email.includes('..')) {
    return 'Email address cannot contain consecutive dots';
  }

  // Check for valid characters before @
  const localPart = email.split('@')[0];
  if (localPart?.startsWith('.') || localPart?.endsWith('.')) {
    return 'Email address cannot start or end with a dot';
  }

  return true;
};

// Client information form schema
export const clientInformationSchema = z.object({
  client_name: z.string()
    .min(2, { message: 'Name must be at least 2 characters' })
    .max(100, { message: 'Name must be less than 100 characters' })
    .regex(/^[a-zA-Z\s'-]+$/, { message: 'Name can only contain letters, spaces, hyphens, and apostrophes' }),
  client_email: z.string()
    .refine(validateEmail, (email) => ({
      message: typeof validateEmail(email) === 'string' ? validateEmail(email) : 'Invalid email address'
    })),
  client_phone: z.string()
    .min(10, { message: 'Please enter a valid phone number' })
    .max(15, { message: 'Phone number is too long' })
    .regex(/^[0-9+\-\s()]+$/, { message: 'Phone number can only contain numbers, +, -, spaces, and parentheses' }),
});

export type ClientInformationFormData = z.infer<typeof clientInformationSchema>;

// Payment verification form schema
export const paymentVerificationSchema = z.object({
  mpesa_code: z.string().min(10, { message: 'Please enter a valid M-Pesa code' }).max(10, { message: 'M-Pesa code should be 10 characters' }),
});

export type PaymentVerificationFormData = z.infer<typeof paymentVerificationSchema>;

// Combined form data
export interface ProjectBookingFormData {
  package_type: PackageType;
  additional_services: string[];
  maintenance_plan?: string;
  client_name: string;
  client_email: string;
  client_phone: string;
}

// Package pricing
export const PACKAGE_PRICES = {
  basic: 75000,
  standard: 145000,
  premium: 225000,
};

// Additional services pricing
export const ADDITIONAL_SERVICES: AdditionalService[] = [
  { id: 'api-dev', name: 'Next.js API development', price: 25000, description: 'Custom API endpoints for your application' },
  { id: 'custom-components', name: 'Custom React components', price: 10000, description: 'Tailored React components for your specific needs' },
  { id: 'headless-cms', name: 'Headless CMS setup', price: 20000, description: 'Integration with a headless CMS for content management' },
  { id: 'performance', name: 'Performance optimization', price: 30000, description: 'Optimize your website for speed and performance' },
  { id: 'pwa', name: 'PWA implementation', price: 35000, description: 'Convert your website into a Progressive Web App' },
  { id: 'third-party-api', name: 'Third-party API integration', price: 20000, description: 'Integrate external APIs into your application' },
  { id: 'migration', name: 'Website migration to Next.js', price: 60000, description: 'Migrate your existing website to Next.js' },
];

// Maintenance plans pricing
export const MAINTENANCE_PLANS: MaintenancePlan[] = [
  { id: 'basic', name: 'Basic', price: 5000, description: 'Hosting monitoring, security updates, bug fixes, up to 2 hours of content updates' },
  { id: 'standard', name: 'Standard', price: 10000, description: 'All Basic features, performance monitoring, weekly backups, up to 4 hours of content updates' },
];

// Helper function to calculate total price
export function calculateTotalPrice(
  packageType: PackageType,
  additionalServices: string[] = [],
  maintenancePlan?: string | null
): number {
  // Base package price
  let total = PACKAGE_PRICES[packageType];

  // Add additional services
  additionalServices.forEach(serviceId => {
    const service = ADDITIONAL_SERVICES.find(s => s.id === serviceId);
    if (service) {
      total += service.price;
    }
  });

  // Add maintenance plan (if selected)
  if (maintenancePlan) {
    const plan = MAINTENANCE_PLANS.find(p => p.id === maintenancePlan);
    if (plan) {
      total += plan.price;
    }
  }

  return total;
}

// Helper function to calculate deposit amount (40% of total)
export function calculateDepositAmount(totalAmount: number): number {
  return Math.round(totalAmount * 0.4);
}

// Generate a unique reference code
export function generateReferenceCode(): string {
  const prefix = 'ZO';
  const timestamp = Date.now().toString().slice(-6);
  const random = Math.floor(Math.random() * 1000).toString().padStart(3, '0');
  return `${prefix}${timestamp}${random}`;
}
