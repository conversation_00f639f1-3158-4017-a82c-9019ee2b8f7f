<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>M-Pesa Payment Test</title>
    <meta name="supabase-url" content="" id="supabase-url-meta" />
    <meta name="supabase-key" content="" id="supabase-key-meta" />
    <style>
      body {
        font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto,
          Oxygen, Ubuntu, Cantarell, "Open Sans", "Helvetica Neue", sans-serif;
        line-height: 1.6;
        color: #333;
        max-width: 800px;
        margin: 0 auto;
        padding: 20px;
      }
      h1 {
        color: #2563eb;
        margin-bottom: 1rem;
      }
      h2 {
        color: #4b5563;
        margin-top: 2rem;
        margin-bottom: 1rem;
      }
      .card {
        background-color: #fff;
        border-radius: 8px;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        padding: 20px;
        margin-bottom: 20px;
      }
      .form-group {
        margin-bottom: 15px;
      }
      label {
        display: block;
        margin-bottom: 5px;
        font-weight: 500;
      }
      input,
      select {
        width: 100%;
        padding: 10px;
        border: 1px solid #d1d5db;
        border-radius: 4px;
        font-size: 16px;
      }
      button {
        background-color: #2563eb;
        color: white;
        border: none;
        padding: 10px 15px;
        border-radius: 4px;
        cursor: pointer;
        font-size: 16px;
        transition: background-color 0.2s;
      }
      button:hover {
        background-color: #1d4ed8;
      }
      button:disabled {
        background-color: #9ca3af;
        cursor: not-allowed;
      }
      .test-data {
        display: flex;
        gap: 10px;
        margin-bottom: 10px;
        flex-wrap: wrap;
      }
      .test-data button {
        background-color: #4b5563;
        font-size: 14px;
      }
      .result {
        margin-top: 20px;
        padding: 15px;
        border-radius: 4px;
      }
      .success {
        background-color: #d1fae5;
        color: #065f46;
      }
      .error {
        background-color: #fee2e2;
        color: #b91c1c;
      }
      pre {
        background-color: #f3f4f6;
        padding: 10px;
        border-radius: 4px;
        overflow-x: auto;
        font-size: 14px;
      }
      .tabs {
        display: flex;
        border-bottom: 1px solid #d1d5db;
        margin-bottom: 20px;
      }
      .tab {
        padding: 10px 15px;
        cursor: pointer;
        border-bottom: 2px solid transparent;
      }
      .tab.active {
        border-bottom: 2px solid #2563eb;
        color: #2563eb;
        font-weight: 500;
      }
      .tab-content {
        display: none;
      }
      .tab-content.active {
        display: block;
      }
    </style>
  </head>
  <body>
    <h1>M-Pesa Payment Test</h1>

    <div class="tabs">
      <div class="tab active" data-tab="test-payment">Test Payment</div>
      <div class="tab" data-tab="test-data">Test Data</div>
      <div class="tab" data-tab="debug">Debug Tools</div>
      <div class="tab" data-tab="generate-codes">Generate Codes</div>
    </div>

    <div id="test-payment" class="tab-content active">
      <div class="card">
        <h2>Payment Verification</h2>
        <form id="payment-form">
          <div class="form-group">
            <label for="reference-code">Reference Code</label>
            <input
              type="text"
              id="reference-code"
              placeholder="Enter reference code"
              required
            />
          </div>
          <div class="form-group">
            <label for="mpesa-code">M-Pesa Code</label>
            <input
              type="text"
              id="mpesa-code"
              placeholder="Enter M-Pesa confirmation code"
              required
              maxlength="10"
            />
          </div>
          <button type="submit" id="verify-button">Verify Payment</button>
        </form>
        <div id="result" class="result" style="display: none"></div>
      </div>
    </div>

    <div id="test-data" class="tab-content">
      <div class="card">
        <h2>Test M-Pesa Codes</h2>
        <div class="test-data">
          <button onclick="fillTestCode('ABC123DEFG')">Code 1</button>
          <button onclick="fillTestCode('XYZ456MNOP')">Code 2</button>
          <button onclick="fillTestCode('QWE789RTYU')">Code 3</button>
          <button onclick="fillTestCode('ZXC012VBNM')">Code 4</button>
          <button onclick="fillTestCode('MPESA12345')">Code 5</button>
          <button onclick="fillTestCode('M1P2E3S4A5')">Code 6</button>
        </div>
        <pre>
const testMpesaCodes = [
  'ABC123DEFG',  // Original test code
  'XYZ456MNOP',  // Original test code
  'QWE789RTYU',  // Original test code
  'ZXC012VBNM',  // Original test code
  'MPESA12345',  // Additional test code with MPESA prefix
  'M1P2E3S4A5',  // Additional test code with M-Pesa-like pattern
  '1234567890',  // Numeric only code
  'ABCDEFGHIJ'   // Alphabetic only code
];</pre
        >
      </div>
    </div>

    <div id="debug" class="tab-content">
      <div class="card">
        <h2>Debug Tools</h2>
        <p>Use these tools to debug M-Pesa payment verification issues:</p>
        <button onclick="loadDebugScript()">Load Debug Script</button>
        <p>
          After loading the debug script, you can use these functions in the
          browser console:
        </p>
        <pre>
// Check Supabase connection
checkSupabaseConnection();

// Log verification details
logVerificationDetails();

// Monitor network requests
monitorNetworkRequests();

// Try all test codes
tryAllTestCodes();

// Test server action directly
testServerAction(referenceCode, mpesaCode);</pre
        >
      </div>
    </div>

    <div id="generate-codes" class="tab-content">
      <div class="card">
        <h2>Generate New M-Pesa Codes</h2>
        <p>
          Need more test codes? Use our code generator to create random M-Pesa
          confirmation codes.
        </p>
        <a href="/mpesa-codes.html" target="_blank">
          <button>Open Code Generator</button>
        </a>
        <p>
          The code generator will create random 10-character codes that you can
          use for testing your payment verification system.
        </p>
      </div>
    </div>

    <script>
      // Set Supabase URL and Key from environment variables
      fetch("/api/config")
        .then((response) => response.json())
        .then((data) => {
          document.getElementById("supabase-url-meta").content =
            data.supabaseUrl || "";
          document.getElementById("supabase-key-meta").content =
            data.supabaseKey || "";
        })
        .catch((error) => console.error("Error fetching config:", error));

      // Tab functionality
      document.querySelectorAll(".tab").forEach((tab) => {
        tab.addEventListener("click", () => {
          // Remove active class from all tabs and tab contents
          document
            .querySelectorAll(".tab")
            .forEach((t) => t.classList.remove("active"));
          document
            .querySelectorAll(".tab-content")
            .forEach((c) => c.classList.remove("active"));

          // Add active class to clicked tab and corresponding content
          tab.classList.add("active");
          const tabId = tab.getAttribute("data-tab");
          document.getElementById(tabId).classList.add("active");
        });
      });

      // Fill test code
      function fillTestCode(code) {
        document.getElementById("mpesa-code").value = code;
      }

      // Load debug script
      function loadDebugScript() {
        const script = document.createElement("script");
        script.src = "/debug-mpesa.js";
        document.head.appendChild(script);
        alert(
          "Debug script loaded! Open the browser console (F12) to use the debug functions."
        );
      }

      // Handle form submission
      document
        .getElementById("payment-form")
        .addEventListener("submit", async (e) => {
          e.preventDefault();

          const referenceCode = document.getElementById("reference-code").value;
          const mpesaCode = document.getElementById("mpesa-code").value;
          const verifyButton = document.getElementById("verify-button");
          const resultDiv = document.getElementById("result");

          // Validate inputs
          if (!referenceCode || !mpesaCode) {
            resultDiv.className = "result error";
            resultDiv.innerHTML =
              "<p>Please enter both reference code and M-Pesa code.</p>";
            resultDiv.style.display = "block";
            return;
          }

          // Disable button and show loading
          verifyButton.disabled = true;
          verifyButton.textContent = "Verifying...";
          resultDiv.style.display = "none";

          try {
            // Call the API route
            const formData = new FormData();
            formData.append("reference_code", referenceCode);
            formData.append("mpesa_code", mpesaCode);

            const response = await fetch("/api/mpesa/verify", {
              method: "POST",
              body: formData,
            });

            const result = await response.json();

            // Display result
            if (response.ok && result.success) {
              resultDiv.className = "result success";
              resultDiv.innerHTML = `
            <p><strong>Payment verified successfully!</strong></p>
            <p>Questionnaire Access Token: ${result.data.questionnaire_access_token}</p>
            <p><a href="/questionnaire/${result.data.questionnaire_access_token}" target="_blank">Go to Questionnaire</a></p>
          `;
            } else {
              resultDiv.className = "result error";
              resultDiv.innerHTML = `<p><strong>Error:</strong> ${
                result.error || "Failed to verify payment."
              }</p>`;
            }
          } catch (error) {
            console.error("Error verifying payment:", error);
            resultDiv.className = "result error";
            resultDiv.innerHTML = `<p><strong>Error:</strong> An unexpected error occurred. Please try again later.</p>`;
          } finally {
            // Re-enable button and show result
            verifyButton.disabled = false;
            verifyButton.textContent = "Verify Payment";
            resultDiv.style.display = "block";
          }
        });
    </script>
  </body>
</html>
