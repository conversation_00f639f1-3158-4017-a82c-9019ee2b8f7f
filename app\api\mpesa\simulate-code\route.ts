import { NextRequest, NextResponse } from 'next/server';

// This endpoint generates a simulated M-Pesa code for testing
export async function GET(request: NextRequest) {
  try {
    // Generate a random M-Pesa code
    const mpesaCode = generateMpesaCode();
    
    return NextResponse.json({
      success: true,
      data: {
        mpesa_code: mpesaCode,
      },
    });
  } catch (error) {
    console.error('Error generating simulated M-Pesa code:', error);
    return NextResponse.json(
      {
        success: false,
        error: `Failed to generate simulated M-Pesa code: ${error instanceof Error ? error.message : 'Unknown error'}`,
      },
      { status: 500 }
    );
  }
}

// Generate a random M-Pesa code (10 characters)
function generateMpesaCode(): string {
  const characters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
  let result = '';
  for (let i = 0; i < 10; i++) {
    result += characters.charAt(Math.floor(Math.random() * characters.length));
  }
  return result;
}
