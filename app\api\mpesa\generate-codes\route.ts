import { NextResponse } from 'next/server';

// Function to generate a random M-Pesa-like confirmation code
function generateMpesaCode(): string {
  // M-Pesa codes are typically 10 characters long
  // They can be alphanumeric (letters and numbers)
  const characters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
  let result = '';
  
  // Generate a random 10-character code
  for (let i = 0; i < 10; i++) {
    result += characters.charAt(Math.floor(Math.random() * characters.length));
  }
  
  return result;
}

// Function to generate a set of M-Pesa codes with different patterns
function generateMpesaCodeSet(count: number = 10): string[] {
  const codes: string[] = [];
  
  // Generate standard random codes
  for (let i = 0; i < count; i++) {
    codes.push(generateMpesaCode());
  }
  
  // Add some codes with specific patterns that resemble real M-Pesa codes
  
  // Pattern: MPESA followed by numbers
  codes.push('MPESA' + Math.floor(10000 + Math.random() * 90000).toString());
  
  // Pattern: Letters followed by numbers
  const letters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ';
  let letterPrefix = '';
  for (let i = 0; i < 3; i++) {
    letterPrefix += letters.charAt(Math.floor(Math.random() * letters.length));
  }
  codes.push(letterPrefix + Math.floor(1000000 + Math.random() * 9000000).toString());
  
  // Pattern: Numbers followed by letters
  const numberPrefix = Math.floor(1000 + Math.random() * 9000).toString();
  let letterSuffix = '';
  for (let i = 0; i < 6; i++) {
    letterSuffix += letters.charAt(Math.floor(Math.random() * letters.length));
  }
  codes.push(numberPrefix + letterSuffix);
  
  // Pattern: Alternating letters and numbers
  let alternatingCode = '';
  for (let i = 0; i < 5; i++) {
    alternatingCode += letters.charAt(Math.floor(Math.random() * letters.length));
    alternatingCode += Math.floor(Math.random() * 10).toString();
  }
  codes.push(alternatingCode.substring(0, 10));
  
  return codes;
}

export async function GET(request: Request) {
  // Get the count parameter from the URL, default to 10
  const url = new URL(request.url);
  const countParam = url.searchParams.get('count');
  const count = countParam ? parseInt(countParam, 10) : 10;
  
  // Generate the M-Pesa codes
  const mpesaCodes = generateMpesaCodeSet(count);
  
  // Return the codes as JSON
  return NextResponse.json({
    success: true,
    data: {
      codes: mpesaCodes,
      message: 'These codes can be used for testing M-Pesa payment verification',
      instructions: 'Copy any of these codes and use them in the payment verification form'
    }
  });
}
