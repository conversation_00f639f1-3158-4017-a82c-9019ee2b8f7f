'use client';

import { useState, useEffect } from 'react';
import { toast } from 'sonner';
import { Briefcase, RefreshCcw, CheckCircle, XCircle, Loader2, Search } from 'lucide-react';
import { format } from 'date-fns';
import { getProjectRequests, generateAccessToken } from '@/app/actions/project-request-actions';
import { ADDITIONAL_SERVICES, MAINTENANCE_PLANS } from '@/lib/mpesa/types';
import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Badge } from '@/components/ui/badge';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from '@/components/ui/tabs';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';

interface ProjectRequest {
  id: string;
  client_name: string;
  client_email: string;
  client_phone: string;
  package_type: string;
  additional_services: string[];
  maintenance_plan: string | null;
  total_amount: number;
  reference_code: string;
  status: string;
  created_at: string;
  updated_at: string;
}

export default function ProjectRequestsPage() {
  const [projectRequests, setProjectRequests] = useState<ProjectRequest[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [selectedRequest, setSelectedRequest] = useState<ProjectRequest | null>(null);
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [isGeneratingToken, setIsGeneratingToken] = useState(false);
  const [accessToken, setAccessToken] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState('all');
  const [searchQuery, setSearchQuery] = useState('');

  // Fetch project requests
  const fetchProjectRequests = async () => {
    setIsLoading(true);
    try {
      const result = await getProjectRequests();
      if (result.success && result.data) {
        setProjectRequests(result.data);
      } else {
        toast.error(result.error || 'Failed to fetch project requests');
      }
    } catch (error) {
      console.error('Error fetching project requests:', error);
      toast.error('An unexpected error occurred');
    } finally {
      setIsLoading(false);
    }
  };

  // Generate access token
  const handleGenerateToken = async () => {
    if (!selectedRequest) return;

    setIsGeneratingToken(true);
    try {
      const result = await generateAccessToken({
        project_request_id: selectedRequest.id,
      });

      if (result.success && result.data) {
        setAccessToken(result.data.access_token);
        toast.success('Access token generated successfully');

        // Refresh the list to update the status
        fetchProjectRequests();
      } else {
        toast.error(result.error || 'Failed to generate access token');
      }
    } catch (error) {
      console.error('Error generating access token:', error);
      toast.error('An unexpected error occurred');
    } finally {
      setIsGeneratingToken(false);
    }
  };

  // Format currency
  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-KE', {
      style: 'currency',
      currency: 'KES',
    }).format(amount);
  };

  // Filter project requests based on tab and search query
  const filteredRequests = projectRequests.filter((request) => {
    const matchesTab = activeTab === 'all' || request.status === activeTab;
    const matchesSearch =
      request.client_name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      request.client_email.toLowerCase().includes(searchQuery.toLowerCase()) ||
      request.client_phone.includes(searchQuery) ||
      request.reference_code.toLowerCase().includes(searchQuery.toLowerCase());

    return matchesTab && matchesSearch;
  });

  // Load project requests on component mount
  useEffect(() => {
    fetchProjectRequests();
  }, []);

  // View request details
  const handleViewRequest = (request: ProjectRequest) => {
    setSelectedRequest(request);
    setIsDialogOpen(true);
    setAccessToken(null); // Reset access token when opening dialog
  };

  // Close dialog
  const handleCloseDialog = () => {
    setIsDialogOpen(false);
    setSelectedRequest(null);
    setAccessToken(null);
  };

  // Copy access token to clipboard
  const handleCopyToken = () => {
    if (accessToken) {
      navigator.clipboard.writeText(accessToken);
      toast.success('Access token copied to clipboard');
    }
  };

  // Copy questionnaire URL to clipboard
  const handleCopyQuestionnaireUrl = () => {
    if (accessToken) {
      const url = `${window.location.origin}/questionnaire/${accessToken}`;
      navigator.clipboard.writeText(url);
      toast.success('Questionnaire URL copied to clipboard');
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4 bg-white dark:bg-gray-800 p-4 rounded-lg shadow-sm border border-gray-100 dark:border-gray-700 mb-6">
        <div className="flex items-center gap-3">
          <div className="bg-primary/10 p-2 rounded-full hidden sm:flex">
            <Briefcase className="h-5 w-5 text-primary" />
          </div>
          <div>
            <h1 className="text-xl sm:text-2xl font-black tracking-tight">Project Requests</h1>
            <p className="text-muted-foreground text-sm sm:text-base font-semibold">
              Manage project requests and generate questionnaire access tokens
            </p>
          </div>
        </div>
        <Button
          onClick={fetchProjectRequests}
          variant="outline"
          size="sm"
          className="border-primary/30 hover:border-primary hover:bg-primary/5 dark:border-primary/20 dark:hover:bg-primary/10 transition-colors self-end sm:self-auto"
        >
          <RefreshCcw className="h-4 w-4 mr-2 text-primary" />
          Refresh
        </Button>
      </div>

      <div className="space-y-4">
        <div className="bg-white dark:bg-gray-800 p-4 rounded-lg shadow-sm border border-gray-100 dark:border-gray-700 flex flex-col sm:flex-row justify-between gap-4">
          <div className="flex items-center">
            <Tabs
              defaultValue="all"
              className="w-full sm:w-auto"
              onValueChange={setActiveTab}
            >
              <TabsList className="bg-gray-100 dark:bg-gray-700 p-1">
                <TabsTrigger
                  value="all"
                  className="data-[state=active]:bg-white dark:data-[state=active]:bg-gray-800 data-[state=active]:text-primary data-[state=active]:shadow-sm"
                >
                  All
                </TabsTrigger>
                <TabsTrigger
                  value="pending"
                  className="data-[state=active]:bg-white dark:data-[state=active]:bg-gray-800 data-[state=active]:text-primary data-[state=active]:shadow-sm"
                >
                  <div className="flex items-center gap-1.5">
                    <span className="h-2 w-2 rounded-full bg-amber-500"></span>
                    Pending
                  </div>
                </TabsTrigger>
                <TabsTrigger
                  value="approved"
                  className="data-[state=active]:bg-white dark:data-[state=active]:bg-gray-800 data-[state=active]:text-primary data-[state=active]:shadow-sm"
                >
                  <div className="flex items-center gap-1.5">
                    <span className="h-2 w-2 rounded-full bg-green-500"></span>
                    Approved
                  </div>
                </TabsTrigger>
              </TabsList>
            </Tabs>
          </div>

          <div className="w-full sm:w-64 relative">
            <Input
              placeholder="Search requests..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-9 border-gray-200 dark:border-gray-700 focus:border-primary/50 dark:focus:border-primary/50"
            />
            <Search className="h-4 w-4 text-gray-400 absolute left-3 top-1/2 transform -translate-y-1/2" />
          </div>
        </div>

        <Card className="shadow-sm hover:shadow-md transition-shadow duration-300 overflow-hidden">
          <CardContent className="p-0">
            {isLoading ? (
              <div className="flex justify-center items-center py-12">
                <div className="relative">
                  <div className="absolute inset-0 bg-primary/10 rounded-full scale-[1.6] blur-md animate-pulse" />
                  <Loader2 className="h-8 w-8 animate-spin text-primary relative" />
                </div>
              </div>
            ) : filteredRequests.length === 0 ? (
              <div className="flex flex-col items-center justify-center py-12 text-center">
                <div className="bg-gray-100 dark:bg-gray-800 p-4 rounded-full mb-4">
                  <Briefcase className="h-12 w-12 text-muted-foreground" />
                </div>
                <h3 className="text-lg font-medium">No project requests found</h3>
                <p className="text-sm text-muted-foreground mt-1 max-w-md">
                  {searchQuery ? 'Try a different search term' : 'New requests will appear here when clients submit project requests'}
                </p>
              </div>
            ) : (
              <>
                {/* Desktop Table View */}
                <div className="hidden md:block overflow-x-auto">
                <Table>
                  <TableHeader>
                    <TableRow className="bg-gray-50 dark:bg-gray-800/50">
                      <TableHead className="font-semibold">Date</TableHead>
                      <TableHead className="font-semibold">Client</TableHead>
                      <TableHead className="font-semibold">Package</TableHead>
                      <TableHead className="font-semibold">Amount</TableHead>
                      <TableHead className="font-semibold">Status</TableHead>
                      <TableHead className="text-right font-semibold">Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {filteredRequests.map((request) => (
                      <TableRow
                        key={request.id}
                        className="hover:bg-gray-50 dark:hover:bg-gray-800/50 transition-colors"
                      >
                        <TableCell className="whitespace-nowrap font-medium">
                          {format(new Date(request.created_at), 'MMM d, yyyy')}
                        </TableCell>
                        <TableCell>
                          <div className="font-semibold">{request.client_name}</div>
                          <div className="text-sm text-muted-foreground font-medium">{request.client_email}</div>
                        </TableCell>
                        <TableCell className="whitespace-nowrap">
                          <span className={`inline-flex items-center px-3 py-1 rounded-full text-xs font-semibold ${
                            request.package_type === 'premium'
                              ? 'bg-amber-50 text-amber-700 border border-amber-200 dark:bg-amber-900/20 dark:text-amber-400 dark:border-amber-800/50'
                              : request.package_type === 'standard'
                              ? 'bg-purple-50 text-purple-700 border border-purple-200 dark:bg-purple-900/20 dark:text-purple-400 dark:border-purple-800/50'
                              : 'bg-blue-50 text-blue-700 border border-blue-200 dark:bg-blue-900/20 dark:text-blue-400 dark:border-blue-800/50'
                          }`}>
                            {request.package_type.charAt(0).toUpperCase() + request.package_type.slice(1)}
                          </span>
                        </TableCell>
                        <TableCell className="font-semibold">{formatCurrency(request.total_amount)}</TableCell>
                        <TableCell>
                          <Badge
                            variant={
                              request.status === 'approved'
                                ? 'success'
                                : request.status === 'pending'
                                ? 'outline'
                                : 'destructive'
                            }
                            className={`font-semibold ${
                              request.status === 'approved'
                                ? 'bg-green-100 text-green-700 border-green-200 dark:bg-green-900/20 dark:text-green-400 dark:border-green-800/50'
                                : request.status === 'pending'
                                ? 'bg-amber-50 text-amber-700 border-amber-200 dark:bg-amber-900/20 dark:text-amber-400 dark:border-amber-800/50'
                                : ''
                            }`}
                          >
                            {request.status.charAt(0).toUpperCase() + request.status.slice(1)}
                          </Badge>
                        </TableCell>
                        <TableCell className="text-right">
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => handleViewRequest(request)}
                            className="border-primary/30 hover:border-primary hover:bg-primary/5 dark:border-primary/20 dark:hover:bg-primary/10 font-medium"
                          >
                            <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1 text-primary" viewBox="0 0 20 20" fill="currentColor">
                              <path d="M10 12a2 2 0 100-4 2 2 0 000 4z" />
                              <path fillRule="evenodd" d="M.458 10C1.732 5.943 5.522 3 10 3s8.268 2.943 9.542 7c-1.274 4.057-5.064 7-9.542 7S1.732 14.057.458 10zM14 10a4 4 0 11-8 0 4 4 0 018 0z" clipRule="evenodd" />
                            </svg>
                            View
                          </Button>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>

              {/* Mobile Card View */}
              <div className="md:hidden space-y-4">
                {filteredRequests.map((request) => (
                  <Card key={request.id} className="border border-gray-200 dark:border-gray-700 shadow-sm hover:shadow-md transition-all duration-200">
                    <CardContent className="p-4">
                      <div className="flex justify-between items-start mb-3">
                        <div className="flex-1">
                          <h3 className="font-bold text-base">{request.client_name}</h3>
                          <p className="text-sm text-muted-foreground font-medium">{request.client_email}</p>
                          <p className="text-xs text-muted-foreground font-medium mt-1">
                            {format(new Date(request.created_at), 'MMM d, yyyy')}
                          </p>
                        </div>
                        <div className="flex flex-col items-end gap-2">
                          <Badge
                            variant={
                              request.status === 'approved'
                                ? 'success'
                                : request.status === 'pending'
                                ? 'outline'
                                : 'destructive'
                            }
                            className={`font-semibold ${
                              request.status === 'approved'
                                ? 'bg-green-100 text-green-700 border-green-200 dark:bg-green-900/20 dark:text-green-400 dark:border-green-800/50'
                                : request.status === 'pending'
                                ? 'bg-amber-50 text-amber-700 border-amber-200 dark:bg-amber-900/20 dark:text-amber-400 dark:border-amber-800/50'
                                : ''
                            }`}
                          >
                            {request.status.charAt(0).toUpperCase() + request.status.slice(1)}
                          </Badge>
                        </div>
                      </div>

                      <div className="flex justify-between items-center">
                        <div className="flex items-center gap-3">
                          <span className={`inline-flex items-center px-2.5 py-1 rounded-full text-xs font-semibold ${
                            request.package_type === 'premium'
                              ? 'bg-amber-50 text-amber-700 border border-amber-200 dark:bg-amber-900/20 dark:text-amber-400 dark:border-amber-800/50'
                              : request.package_type === 'standard'
                              ? 'bg-purple-50 text-purple-700 border border-purple-200 dark:bg-purple-900/20 dark:text-purple-400 dark:border-purple-800/50'
                              : 'bg-blue-50 text-blue-700 border border-blue-200 dark:bg-blue-900/20 dark:text-blue-400 dark:border-blue-800/50'
                          }`}>
                            {request.package_type.charAt(0).toUpperCase() + request.package_type.slice(1)}
                          </span>
                          <span className="font-bold text-sm">{formatCurrency(request.total_amount)}</span>
                        </div>

                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleViewRequest(request)}
                          className="border-primary/30 hover:border-primary hover:bg-primary/5 dark:border-primary/20 dark:hover:bg-primary/10 font-medium"
                        >
                          <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1 text-primary" viewBox="0 0 20 20" fill="currentColor">
                            <path d="M10 12a2 2 0 100-4 2 2 0 000 4z" />
                            <path fillRule="evenodd" d="M.458 10C1.732 5.943 5.522 3 10 3s8.268 2.943 9.542 7c-1.274 4.057-5.064 7-9.542 7S1.732 14.057.458 10zM14 10a4 4 0 11-8 0 4 4 0 018 0z" clipRule="evenodd" />
                          </svg>
                          View
                        </Button>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
              </>
            )}
          </CardContent>
        </Card>
      </div>

      {/* Request Details Dialog */}
      <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
        <DialogContent className="max-w-4xl max-h-[95vh] overflow-y-auto p-0 mx-2 sm:mx-4 lg:mx-auto">
          <DialogHeader className="pb-4 sm:pb-5 sticky top-0 bg-background z-10 px-4 sm:px-6 pt-4 sm:pt-6 bg-gradient-to-r from-primary/5 to-transparent rounded-t-lg">
            <div className="flex items-center gap-3">
              <div className="bg-primary/10 p-2 rounded-full">
                <Briefcase className="h-5 w-5 text-primary" />
              </div>
              <div className="flex-1 min-w-0">
                <DialogTitle className="text-lg sm:text-xl font-bold">
                  Project Request Details
                </DialogTitle>
                <DialogDescription className="text-sm mt-1 font-medium">
                  Review the project request and generate an access token for the questionnaire
                </DialogDescription>
              </div>
            </div>
          </DialogHeader>

          {selectedRequest && (
            <div className="space-y-4 sm:space-y-6 px-3 sm:px-4 lg:px-6 py-4">
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-3 sm:gap-4 lg:gap-5">
                {/* Client Information Card */}
                <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-blue-100 dark:border-blue-900/30 overflow-hidden hover:shadow-md transition-shadow duration-300">
                  <div className="bg-gradient-to-r from-blue-50 to-blue-50/50 dark:from-blue-900/30 dark:to-blue-900/10 p-3 border-b border-blue-100 dark:border-blue-900/20">
                    <div className="flex items-center gap-2">
                      <div className="bg-blue-100 dark:bg-blue-800/50 p-1.5 rounded-full">
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 text-blue-600 dark:text-blue-400" viewBox="0 0 20 20" fill="currentColor">
                          <path fillRule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z" clipRule="evenodd" />
                        </svg>
                      </div>
                      <h3 className="font-medium text-blue-700 dark:text-blue-400">Client Information</h3>
                    </div>
                  </div>
                  <div className="p-3 space-y-3">
                    <div className="bg-blue-50/50 dark:bg-blue-900/10 p-2 rounded-md flex justify-between items-center">
                      <span className="text-sm text-blue-600/70 dark:text-blue-400/70">Name</span>
                      <span className="text-sm font-medium">{selectedRequest.client_name}</span>
                    </div>
                    <div className="bg-blue-50/50 dark:bg-blue-900/10 p-2 rounded-md flex justify-between items-center">
                      <span className="text-sm text-blue-600/70 dark:text-blue-400/70">Email</span>
                      <span className="text-sm font-medium">{selectedRequest.client_email}</span>
                    </div>
                    <div className="bg-blue-50/50 dark:bg-blue-900/10 p-2 rounded-md flex justify-between items-center">
                      <span className="text-sm text-blue-600/70 dark:text-blue-400/70">Phone</span>
                      <span className="text-sm font-medium">{selectedRequest.client_phone}</span>
                    </div>
                    <div className="bg-blue-50/50 dark:bg-blue-900/10 p-2 rounded-md flex justify-between items-center">
                      <span className="text-sm text-blue-600/70 dark:text-blue-400/70">Reference</span>
                      <span className="text-sm font-medium font-mono">{selectedRequest.reference_code}</span>
                    </div>
                  </div>
                </div>

                {/* Financial Information Card */}
                <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-green-100 dark:border-green-900/30 overflow-hidden hover:shadow-md transition-shadow duration-300">
                  <div className="bg-gradient-to-r from-green-50 to-green-50/50 dark:from-green-900/30 dark:to-green-900/10 p-3 border-b border-green-100 dark:border-green-900/20">
                    <div className="flex items-center gap-2">
                      <div className="bg-green-100 dark:bg-green-800/50 p-1.5 rounded-full">
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 text-green-600 dark:text-green-400" viewBox="0 0 20 20" fill="currentColor">
                          <path d="M8.433 7.418c.155-.103.346-.196.567-.267v1.698a2.305 2.305 0 01-.567-.267C8.07 8.34 8 8.114 8 8c0-.114.07-.34.433-.582zM11 12.849v-1.698c.22.071.412.164.567.267.364.243.433.468.433.582 0 .114-.07.34-.433.582a2.305 2.305 0 01-.567.267z" />
                          <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-13a1 1 0 10-2 0v.092a4.535 4.535 0 00-1.676.662C6.602 6.234 6 7.009 6 8c0 .99.602 1.765 1.324 *********** 1.054.545 1.676.662v1.941c-.391-.127-.68-.317-.843-.504a1 1 0 10-1.51 1.31c.562.649 1.413 1.076 2.353 1.253V15a1 1 0 102 0v-.092a4.535 4.535 0 001.676-.662C13.398 13.766 14 12.991 14 12c0-.99-.602-1.765-1.324-2.246A4.535 4.535 0 0011 9.092V7.151c.391.127.68.317.843.504a1 1 0 101.511-1.31c-.563-.649-1.413-1.076-2.354-1.253V5z" clipRule="evenodd" />
                        </svg>
                      </div>
                      <h3 className="font-medium text-green-700 dark:text-green-400">Financial Information</h3>
                    </div>
                  </div>
                  <div className="p-3 space-y-3">
                    <div className="bg-green-50/50 dark:bg-green-900/10 p-2 rounded-md flex justify-between items-center">
                      <span className="text-sm text-green-600/70 dark:text-green-400/70">Total Amount</span>
                      <span className="text-sm font-medium">{formatCurrency(selectedRequest.total_amount)}</span>
                    </div>
                    <div className="bg-green-50/50 dark:bg-green-900/10 p-2 rounded-md flex justify-between items-center">
                      <span className="text-sm text-green-600/70 dark:text-green-400/70">Deposit (40%)</span>
                      <span className="text-sm font-medium">{formatCurrency(selectedRequest.total_amount * 0.4)}</span>
                    </div>
                    <div className="bg-green-50/50 dark:bg-green-900/10 p-2 rounded-md flex justify-between items-center">
                      <span className="text-sm text-green-600/70 dark:text-green-400/70">Status</span>
                      <Badge
                        variant={
                          selectedRequest.status === 'approved'
                            ? 'success'
                            : selectedRequest.status === 'pending'
                            ? 'outline'
                            : 'destructive'
                        }
                        className={`text-sm px-2 py-1 ${
                          selectedRequest.status === 'approved'
                            ? 'bg-green-100 text-green-700 border-green-200 dark:bg-green-900/20 dark:text-green-400 dark:border-green-800/50'
                            : selectedRequest.status === 'pending'
                            ? 'bg-amber-50 text-amber-700 border-amber-200 dark:bg-amber-900/20 dark:text-amber-400 dark:border-amber-800/50'
                            : ''
                        }`}
                      >
                        {selectedRequest.status.charAt(0).toUpperCase() + selectedRequest.status.slice(1)}
                      </Badge>
                    </div>
                  </div>
                </div>

                {/* Package Information Card */}
                <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-purple-100 dark:border-purple-900/30 overflow-hidden hover:shadow-md transition-shadow duration-300">
                  <div className="bg-gradient-to-r from-purple-50 to-purple-50/50 dark:from-purple-900/30 dark:to-purple-900/10 p-3 border-b border-purple-100 dark:border-purple-900/20">
                    <div className="flex items-center gap-2">
                      <div className="bg-purple-100 dark:bg-purple-800/50 p-1.5 rounded-full">
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 text-purple-600 dark:text-purple-400" viewBox="0 0 20 20" fill="currentColor">
                          <path fillRule="evenodd" d="M5 4a3 3 0 00-3 3v6a3 3 0 003 3h10a3 3 0 003-3V7a3 3 0 00-3-3H5zm-1 9v-1h5v2H5a1 1 0 01-1-1zm7 1h4a1 1 0 001-1v-1h-5v2zm0-4h5V8h-5v2zM9 8H4v2h5V8z" clipRule="evenodd" />
                        </svg>
                      </div>
                      <h3 className="font-medium text-purple-700 dark:text-purple-400">Package Information</h3>
                    </div>
                  </div>
                  <div className="p-3 space-y-3">
                    <div className="bg-purple-50/50 dark:bg-purple-900/10 p-2 rounded-md flex justify-between items-center">
                      <span className="text-sm text-purple-600/70 dark:text-purple-400/70">Package Type</span>
                      <div className={`inline-flex items-center px-2 py-1 rounded-full text-sm font-medium ${
                        selectedRequest.package_type === 'premium'
                          ? 'bg-amber-50 text-amber-700 border border-amber-200 dark:bg-amber-900/20 dark:text-amber-400 dark:border-amber-800/50'
                          : selectedRequest.package_type === 'standard'
                          ? 'bg-purple-50 text-purple-700 border border-purple-200 dark:bg-purple-900/20 dark:text-purple-400 dark:border-purple-800/50'
                          : 'bg-blue-50 text-blue-700 border border-blue-200 dark:bg-blue-900/20 dark:text-blue-400 dark:border-blue-800/50'
                      }`}>
                        {selectedRequest.package_type.charAt(0).toUpperCase() + selectedRequest.package_type.slice(1)}
                      </div>
                    </div>

                    <div className="bg-purple-50/50 dark:bg-purple-900/10 p-2 rounded-md">
                      <div className="mb-1.5">
                        <span className="text-sm text-purple-600/70 dark:text-purple-400/70">Additional Services</span>
                      </div>
                      <div>
                        {selectedRequest.additional_services && selectedRequest.additional_services.length > 0 ? (
                          <div className="flex flex-wrap gap-1.5">
                            {selectedRequest.additional_services.map((serviceId, index) => {
                              const service = ADDITIONAL_SERVICES.find(s => s.id === serviceId);
                              return (
                                <span
                                  key={index}
                                  className="inline-flex items-center px-2 py-1 rounded-md text-xs font-medium bg-blue-50 text-blue-700 border border-blue-100 dark:bg-blue-900/30 dark:text-blue-300 dark:border-blue-800/50"
                                >
                                  {service ? service.name : serviceId}
                                </span>
                              );
                            })}
                          </div>
                        ) : (
                          <span className="text-sm text-muted-foreground">None</span>
                        )}
                      </div>
                    </div>

                    <div className="bg-purple-50/50 dark:bg-purple-900/10 p-2 rounded-md">
                      <div className="mb-1.5">
                        <span className="text-sm text-purple-600/70 dark:text-purple-400/70">Maintenance Plan</span>
                      </div>
                      {selectedRequest.maintenance_plan ? (
                        (() => {
                          const plan = MAINTENANCE_PLANS.find(p => p.id === selectedRequest.maintenance_plan);
                          return (
                            <div className="inline-flex items-center px-2 py-1 rounded-md text-xs font-medium bg-green-50 text-green-700 border border-green-100 dark:bg-green-900/30 dark:text-green-300 dark:border-green-800/50">
                              {plan ? `${plan.name} (${formatCurrency(plan.price)}/month)` : selectedRequest.maintenance_plan}
                            </div>
                          );
                        })()
                      ) : (
                        <span className="text-sm text-muted-foreground">None</span>
                      )}
                    </div>
                  </div>
                </div>

                {/* Access Token Information */}
                <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-amber-100 dark:border-amber-900/30 overflow-hidden hover:shadow-md transition-shadow duration-300">
                  <div className="bg-gradient-to-r from-amber-50 to-amber-50/50 dark:from-amber-900/30 dark:to-amber-900/10 p-3 border-b border-amber-100 dark:border-amber-900/20">
                    <div className="flex items-center gap-2">
                      <div className="bg-amber-100 dark:bg-amber-800/50 p-1.5 rounded-full">
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 text-amber-600 dark:text-amber-400" viewBox="0 0 20 20" fill="currentColor">
                          <path fillRule="evenodd" d="M5 9V7a5 5 0 0110 0v2a2 2 0 012 2v5a2 2 0 01-2 2H5a2 2 0 01-2-2v-5a2 2 0 012-2zm8-2v2H7V7a3 3 0 016 0z" clipRule="evenodd" />
                        </svg>
                      </div>
                      <h3 className="font-medium text-amber-700 dark:text-amber-400">Access Token</h3>
                    </div>
                  </div>
                  <div className="p-4 sm:p-5 flex flex-col justify-center min-h-[120px]">
                    <p className="text-sm text-gray-600 dark:text-gray-300 mb-6 text-center">
                      {selectedRequest.status === 'pending'
                        ? 'Generate an access token to allow the client to complete the questionnaire.'
                        : 'This project request has already been approved.'}
                    </p>

                    <div className="flex justify-center w-full">
                      {selectedRequest.status === 'pending' ? (
                        <Button
                          onClick={handleGenerateToken}
                          disabled={isGeneratingToken}
                          className="bg-primary hover:bg-primary/90 text-sm font-medium px-6 py-3 h-auto shadow-sm hover:shadow transition-all w-full sm:w-auto min-w-[200px] whitespace-nowrap"
                        >
                          {isGeneratingToken ? (
                            <>
                              <Loader2 className="mr-2 h-4 w-4 animate-spin flex-shrink-0" />
                              <span>Generating...</span>
                            </>
                          ) : (
                            <>
                              <CheckCircle className="mr-2 h-4 w-4 flex-shrink-0" />
                              <span>Generate Access Token</span>
                            </>
                          )}
                        </Button>
                      ) : (
                        <Button
                          disabled
                          className="bg-gray-100 text-gray-500 dark:bg-gray-700 dark:text-gray-400 text-sm font-medium px-6 py-3 h-auto w-full sm:w-auto min-w-[200px] whitespace-nowrap"
                        >
                          <CheckCircle className="mr-2 h-4 w-4 flex-shrink-0" />
                          <span>Already Approved</span>
                        </Button>
                      )}
                    </div>
                  </div>
                </div>
              </div>



              {accessToken && (
                <div className="col-span-1 md:col-span-2 bg-white dark:bg-gray-800 rounded-lg shadow-md p-0 border border-green-200 dark:border-green-800 overflow-hidden">
                  <div className="bg-gradient-to-r from-green-50 to-green-50/50 dark:from-green-900/30 dark:to-green-900/10 p-4 border-b border-green-100 dark:border-green-800">
                    <div className="flex items-center gap-3">
                      <div className="relative">
                        <div className="absolute inset-0 bg-green-100 dark:bg-green-900/30 rounded-full scale-[1.6] blur-md animate-pulse" />
                        <div className="relative bg-white dark:bg-gray-800 rounded-full p-2 shadow-sm border border-green-200 dark:border-green-800">
                          <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-green-600 dark:text-green-400" viewBox="0 0 20 20" fill="currentColor">
                            <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                          </svg>
                        </div>
                      </div>
                      <div>
                        <h3 className="font-medium text-green-800 dark:text-green-300 text-base">
                          Access Token Generated Successfully
                        </h3>
                        <p className="text-sm text-green-600/80 dark:text-green-400/80 mt-0.5">
                          Share this with the client to complete the questionnaire
                        </p>
                      </div>
                    </div>
                  </div>

                  <div className="p-4">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div className="bg-white dark:bg-gray-800/50 p-3 rounded-lg border border-green-100 dark:border-green-900/30 shadow-sm">
                        <Label htmlFor="access-token" className="text-sm font-medium text-green-800 dark:text-green-300 flex items-center gap-1.5 mb-2">
                          <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" viewBox="0 0 20 20" fill="currentColor">
                            <path fillRule="evenodd" d="M5 9V7a5 5 0 0110 0v2a2 2 0 012 2v5a2 2 0 01-2 2H5a2 2 0 01-2-2v-5a2 2 0 012-2zm8-2v2H7V7a3 3 0 016 0z" clipRule="evenodd" />
                          </svg>
                          Access Token
                        </Label>
                        <div className="flex">
                          <Input
                            id="access-token"
                            value={accessToken}
                            readOnly
                            className="font-mono text-sm border-green-200 dark:border-green-800 bg-green-50/50 dark:bg-green-900/10"
                          />
                          <Button
                            variant="outline"
                            size="default"
                            className="ml-2 border-green-200 hover:bg-green-50 text-green-700 dark:border-green-800 dark:hover:bg-green-900/20 dark:text-green-400"
                            onClick={handleCopyToken}
                          >
                            <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1" viewBox="0 0 20 20" fill="currentColor">
                              <path d="M8 2a1 1 0 000 2h2a1 1 0 100-2H8z" />
                              <path d="M3 5a2 2 0 012-2 3 3 0 003 3h2a3 3 0 003-3 2 2 0 012 2v6h-4.586l1.293-1.293a1 1 0 00-1.414-1.414l-3 3a1 1 0 000 1.414l3 3a1 1 0 001.414-1.414L10.414 13H15v3a2 2 0 01-2 2H5a2 2 0 01-2-2V5zM15 11h2a1 1 0 110 2h-2v-2z" />
                            </svg>
                            Copy
                          </Button>
                        </div>
                      </div>

                      <div className="bg-white dark:bg-gray-800/50 p-3 rounded-lg border border-green-100 dark:border-green-900/30 shadow-sm">
                        <Label htmlFor="questionnaire-url" className="text-sm font-medium text-green-800 dark:text-green-300 flex items-center gap-1.5 mb-2">
                          <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" viewBox="0 0 20 20" fill="currentColor">
                            <path fillRule="evenodd" d="M12.586 4.586a2 2 0 112.828 2.828l-3 3a2 2 0 01-2.828 0 1 1 0 00-1.414 1.414 4 4 0 005.656 0l3-3a4 4 0 00-5.656-5.656l-1.5 1.5a1 1 0 101.414 1.414l1.5-1.5zm-5 5a2 2 0 012.828 0 1 1 0 101.414-1.414 4 4 0 00-5.656 0l-3 3a4 4 0 105.656 5.656l1.5-1.5a1 1 0 10-1.414-1.414l-1.5 1.5a2 2 0 11-2.828-2.828l3-3z" clipRule="evenodd" />
                          </svg>
                          Questionnaire URL
                        </Label>
                        <div className="flex">
                          <Input
                            id="questionnaire-url"
                            value={`${window.location.origin}/questionnaire/${accessToken}`}
                            readOnly
                            className="font-mono text-sm border-green-200 dark:border-green-800 bg-green-50/50 dark:bg-green-900/10"
                          />
                          <Button
                            variant="outline"
                            size="default"
                            className="ml-2 border-green-200 hover:bg-green-50 text-green-700 dark:border-green-800 dark:hover:bg-green-900/20 dark:text-green-400"
                            onClick={handleCopyQuestionnaireUrl}
                          >
                            <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1" viewBox="0 0 20 20" fill="currentColor">
                              <path d="M8 2a1 1 0 000 2h2a1 1 0 100-2H8z" />
                              <path d="M3 5a2 2 0 012-2 3 3 0 003 3h2a3 3 0 003-3 2 2 0 012 2v6h-4.586l1.293-1.293a1 1 0 00-1.414-1.414l-3 3a1 1 0 000 1.414l3 3a1 1 0 001.414-1.414L10.414 13H15v3a2 2 0 01-2 2H5a2 2 0 01-2-2V5zM15 11h2a1 1 0 110 2h-2v-2z" />
                            </svg>
                            Copy
                          </Button>
                        </div>
                      </div>
                    </div>

                    <div className="mt-4 bg-green-50 dark:bg-green-900/20 p-3 rounded-lg border border-green-100 dark:border-green-800/50 flex items-start">
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-green-600 dark:text-green-400 mr-2 flex-shrink-0 mt-0.5" viewBox="0 0 20 20" fill="currentColor">
                        <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2h-1v-3a1 1 0 00-1-1z" clipRule="evenodd" />
                      </svg>
                      <div>
                        <p className="text-sm text-green-700 dark:text-green-300 font-medium">
                          Next Steps
                        </p>
                        <p className="text-sm text-green-600 dark:text-green-400 mt-1">
                          Share this URL with the client to allow them to complete the detailed questionnaire. The token will expire in 7 days.
                        </p>
                      </div>
                    </div>
                  </div>
                </div>
              )}
            </div>
          )}

          <DialogFooter className="px-3 sm:px-4 lg:px-6 py-3 sm:py-4 bg-gray-50 dark:bg-gray-900/20 border-t border-gray-200 dark:border-gray-700 sticky bottom-0">
            <Button
              variant="outline"
              onClick={handleCloseDialog}
              className="w-full sm:w-auto border-gray-300 hover:bg-gray-100 dark:border-gray-600 dark:hover:bg-gray-800 transition-colors font-medium"
            >
              <XCircle className="h-4 w-4 mr-2 text-gray-500" />
              Close
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}
