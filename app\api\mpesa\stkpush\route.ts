import { NextRequest, NextResponse } from 'next/server';
import { initiateSTKPush } from '@/lib/mpesa/daraja-api';
import { getClientProjectByReferenceCode } from '@/lib/mpesa/supabase-mpesa-fixed';

export async function POST(request: NextRequest) {
  try {
    console.log('STK Push API called');

    // Log environment variables (without sensitive values)
    console.log('Environment check:');
    console.log('MPESA_ENVIRONMENT:', process.env.MPESA_ENVIRONMENT);
    console.log('MPESA_SHORTCODE exists:', !!process.env.MPESA_SHORTCODE);
    console.log('MPESA_CONSUMER_KEY exists:', !!process.env.MPESA_CONSUMER_KEY);
    console.log('MPESA_CONSUMER_SECRET exists:', !!process.env.MPESA_CONSUMER_SECRET);
    console.log('MPESA_PASSKEY exists:', !!process.env.MPESA_PASSKEY);
    console.log('NEXT_PUBLIC_APP_URL:', process.env.NEXT_PUBLIC_APP_URL);

    // Parse request body
    const body = await request.json();
    const { phoneNumber, referenceCode } = body;

    console.log('Request body:', { phoneNumber, referenceCode });

    // Validate inputs
    if (!phoneNumber) {
      console.log('Error: Phone number is required');
      return NextResponse.json(
        {
          success: false,
          error: 'Phone number is required',
        },
        { status: 400 }
      );
    }

    if (!referenceCode) {
      console.log('Error: Reference code is required');
      return NextResponse.json(
        {
          success: false,
          error: 'Reference code is required',
        },
        { status: 400 }
      );
    }

    // Get client project by reference code
    const clientProject = await getClientProjectByReferenceCode(referenceCode);
    if (!clientProject) {
      return NextResponse.json(
        {
          success: false,
          error: `Invalid reference code: ${referenceCode}. Please check and try again.`,
        },
        { status: 400 }
      );
    }

    console.log('API route: Found client project:', JSON.stringify(clientProject));

    // Initiate STK Push
    try {
      console.log('Initiating STK Push with parameters:');
      console.log('Phone Number:', phoneNumber);
      console.log('Amount:', clientProject.deposit_amount);
      console.log('Reference Code:', referenceCode);
      console.log('Description:', `Payment for ${clientProject.package_type} package`);

      // Format phone number for debugging
      const formattedPhoneNumber = phoneNumber.startsWith('0')
        ? `254${phoneNumber.substring(1)}`
        : phoneNumber;
      console.log('Formatted Phone Number:', formattedPhoneNumber);

      const stkPushResponse = await initiateSTKPush(
        phoneNumber,
        clientProject.deposit_amount,
        referenceCode, // Use reference code as account reference
        `Payment for ${clientProject.package_type} package`
      );

      console.log('STK Push initiated successfully:', JSON.stringify(stkPushResponse));

      return NextResponse.json({
        success: true,
        data: {
          MerchantRequestID: stkPushResponse.MerchantRequestID,
          CheckoutRequestID: stkPushResponse.CheckoutRequestID,
          ResponseDescription: stkPushResponse.ResponseDescription,
          CustomerMessage: stkPushResponse.CustomerMessage,
        },
      });
    } catch (stkPushError) {
      console.error('Error initiating STK Push:', stkPushError);

      // Log more detailed error information
      if (stkPushError instanceof Error) {
        console.error('Error message:', stkPushError.message);
        console.error('Error stack:', stkPushError.stack);
      }

      return NextResponse.json(
        {
          success: false,
          error: `Failed to initiate STK Push: ${stkPushError instanceof Error ? stkPushError.message : 'Unknown error'}`,
        },
        { status: 500 }
      );
    }
  } catch (error) {
    console.error('Error processing STK Push request:', error);
    return NextResponse.json(
      {
        success: false,
        error: `Failed to process STK Push request: ${error instanceof Error ? error.message : 'Unknown error'}`,
      },
      { status: 500 }
    );
  }
}
