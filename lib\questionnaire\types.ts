import { z } from 'zod';

// Package types
export type PackageType = 'basic' | 'standard' | 'premium';

// Questionnaire response type
export interface QuestionnaireResponse {
  id: string;
  client_project_id: string;

  // General Information (Section 1)
  project_name?: string;
  project_description?: string;
  target_audience?: string;
  project_goals?: string[];
  color_scheme?: string;
  has_logo_or_brand_guidelines?: boolean;
  preferred_launch_timeline?: string;

  // Package-Specific Questions (Section 2)
  // Basic Package
  requested_pages?: string[];
  has_content_ready?: boolean;
  contact_form_fields?: string[];
  has_domain_name?: boolean;
  preferred_hosting?: string;

  // Standard Package (additional fields)
  blog_content_type?: string;
  blog_update_frequency?: string;
  cms_preference?: string;
  auth_features?: string[];
  design_references?: string;

  // Premium Package (additional fields)
  ecommerce_features?: string[];
  payment_methods?: string[];
  third_party_apis?: string[];
  database_requirements?: string;
  admin_panel_features?: string[];
  needs_user_roles?: boolean;

  // This is used in the UI but not stored in the database
  packageType?: PackageType;

  // Metadata
  completion_status: 'in_progress' | 'completed' | 'abandoned';
  last_section_completed: number;
  created_at: string;
  updated_at: string;
}

// Project goals options
export const PROJECT_GOALS = [
  { id: 'online_presence', label: 'Establish online presence' },
  { id: 'lead_generation', label: 'Generate leads/inquiries' },
  { id: 'sell_products', label: 'Sell products or services' },
  { id: 'provide_information', label: 'Provide information/resources' },
  { id: 'build_community', label: 'Build a community' },
  { id: 'showcase_portfolio', label: 'Showcase work/portfolio' },
  { id: 'brand_awareness', label: 'Increase brand awareness' },
  { id: 'customer_support', label: 'Provide customer support' }
];

// Color scheme options
export const COLOR_SCHEMES = [
  { id: 'light', label: 'Light and minimal' },
  { id: 'dark', label: 'Dark and bold' },
  { id: 'colorful', label: 'Colorful and vibrant' },
  { id: 'neutral', label: 'Neutral and professional' },
  { id: 'custom', label: 'I have specific brand colors' }
];

// Timeline options
export const TIMELINE_OPTIONS = [
  { id: 'asap', label: 'As soon as possible' },
  { id: '1_month', label: 'Within 1 month' },
  { id: '2_3_months', label: 'Within 2-3 months' },
  { id: '3_6_months', label: 'Within 3-6 months' },
  { id: 'flexible', label: 'Flexible/No specific deadline' }
];

// Contact form field options
export const CONTACT_FORM_FIELDS = [
  { id: 'name', label: 'Name' },
  { id: 'email', label: 'Email' },
  { id: 'phone', label: 'Phone' },
  { id: 'company', label: 'Company/Organization' },
  { id: 'subject', label: 'Subject' },
  { id: 'message', label: 'Message' },
  { id: 'budget', label: 'Budget' },
  { id: 'deadline', label: 'Deadline' },
  { id: 'how_found', label: 'How did you find us?' }
];

// Hosting options
export const HOSTING_OPTIONS = [
  { id: 'vercel', label: 'Vercel' },
  { id: 'netlify', label: 'Netlify' },
  { id: 'no_preference', label: 'No preference' }
];

// CMS options
export const CMS_OPTIONS = [
  { id: 'contentful', label: 'Contentful' },
  { id: 'sanity', label: 'Sanity' },
  { id: 'no_preference', label: 'No preference' }
];

// Blog update frequency options
export const BLOG_FREQUENCY_OPTIONS = [
  { id: 'weekly', label: 'Weekly' },
  { id: 'biweekly', label: 'Bi-weekly' },
  { id: 'monthly', label: 'Monthly' },
  { id: 'quarterly', label: 'Quarterly' },
  { id: 'irregular', label: 'Irregular/As needed' }
];

// Authentication features options
export const AUTH_FEATURES = [
  { id: 'email_password', label: 'Email/Password login' },
  { id: 'social_login', label: 'Social media login' },
  { id: 'magic_link', label: 'Magic link (passwordless)' },
  { id: 'two_factor', label: 'Two-factor authentication' },
  { id: 'role_based', label: 'Role-based access control' }
];

// E-commerce features options
export const ECOMMERCE_FEATURES = [
  { id: 'product_catalog', label: 'Product catalog' },
  { id: 'shopping_cart', label: 'Shopping cart' },
  { id: 'checkout', label: 'Checkout process' },
  { id: 'payment_processing', label: 'Payment processing' },
  { id: 'order_management', label: 'Order management' },
  { id: 'inventory', label: 'Inventory management' },
  { id: 'discounts', label: 'Discounts and promotions' },
  { id: 'shipping', label: 'Shipping options' }
];

// Payment methods options
export const PAYMENT_METHODS = [
  { id: 'mpesa', label: 'M-Pesa' },
  { id: 'credit_card', label: 'Credit/Debit Card' },
  { id: 'paypal', label: 'PayPal' },
  { id: 'bank_transfer', label: 'Bank Transfer' },
  { id: 'other', label: 'Other (please specify)' }
];

// Admin panel features options
export const ADMIN_PANEL_FEATURES = [
  { id: 'content_management', label: 'Content management' },
  { id: 'user_management', label: 'User management' },
  { id: 'analytics', label: 'Analytics dashboard' },
  { id: 'order_management', label: 'Order management' },
  { id: 'inventory', label: 'Inventory management' },
  { id: 'settings', label: 'Site settings' }
];

// General Information form schema (Section 1)
export const generalInformationSchema = z.object({
  project_name: z.string().min(2, { message: 'Project name is required' }),
  project_description: z.string().min(10, { message: 'Please provide a brief description' }),
  target_audience: z.string().min(5, { message: 'Please describe your target audience' }),
  project_goals: z.array(z.string()).min(1, { message: 'Please select at least one goal' }),
  color_scheme: z.string(),
  has_logo_or_brand_guidelines: z.boolean(),
  preferred_launch_timeline: z.string()
});

export type GeneralInformationFormData = z.infer<typeof generalInformationSchema>;

// Basic Package form schema
export const basicPackageSchema = z.object({
  requested_pages: z.array(z.string()).min(1, { message: 'Please list at least one page' }),
  has_content_ready: z.boolean(),
  contact_form_fields: z.array(z.string()).min(1, { message: 'Please select at least one field' }),
  has_domain_name: z.boolean(),
  preferred_hosting: z.string()
});

export type BasicPackageFormData = z.infer<typeof basicPackageSchema>;

// Standard Package form schema (includes Basic Package fields)
export const standardPackageSchema = basicPackageSchema.extend({
  blog_content_type: z.string().min(5, { message: 'Please describe your blog content' }),
  blog_update_frequency: z.string(),
  cms_preference: z.string(),
  auth_features: z.array(z.string()).optional(),
  design_references: z.string().optional()
});

export type StandardPackageFormData = z.infer<typeof standardPackageSchema>;

// Premium Package form schema (includes Standard Package fields)
export const premiumPackageSchema = standardPackageSchema.extend({
  ecommerce_features: z.array(z.string()).optional(),
  payment_methods: z.array(z.string()).optional(),
  third_party_apis: z.array(z.string()).optional(),
  database_requirements: z.string().optional(),
  admin_panel_features: z.array(z.string()).optional(),
  needs_user_roles: z.boolean().optional()
});

export type PremiumPackageFormData = z.infer<typeof premiumPackageSchema>;
