import { createClient } from '@supabase/supabase-js';
import { QuestionnaireResponse, PackageType, COLOR_SCHEMES } from './types';

// Create a Supabase client
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || '';
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || '';
const supabase = createClient(supabaseUrl, supabaseAnonKey);

// Helper function to get the full label for a color scheme ID
function getColorSchemeLabel(schemeId: string): string {
  const scheme = COLOR_SCHEMES.find(s => s.id === schemeId);
  return scheme ? scheme.label : schemeId;
}

// Validate questionnaire access token
export async function validateQuestionnaireToken(token: string): Promise<{
  isValid: boolean;
  clientProjectId?: string;
  packageType?: PackageType;
  error?: string;
}> {
  try {
    // Check if token exists and is not expired
    const { data: accessData, error: accessError } = await supabase
      .from('questionnaire_access')
      .select('*')
      .eq('access_token', token)
      .single();

    if (accessError || !accessData) {
      return {
        isValid: false,
        error: 'Invalid access token'
      };
    }

    // Check if token is expired
    const expiresAt = new Date(accessData.expires_at);
    if (expiresAt < new Date()) {
      return {
        isValid: false,
        error: 'Access token has expired'
      };
    }

    // Get client project details
    const { data: projectData, error: projectError } = await supabase
      .from('client_projects')
      .select('*')
      .eq('id', accessData.client_project_id)
      .single();

    if (projectError || !projectData) {
      return {
        isValid: false,
        error: 'Project not found'
      };
    }

    return {
      isValid: true,
      clientProjectId: accessData.client_project_id,
      packageType: projectData.package_type as PackageType
    };
  } catch (error: any) {
    console.error('Error validating questionnaire token:', error);
    return {
      isValid: false,
      error: error.message || 'An unexpected error occurred'
    };
  }
}

// Get existing questionnaire response
export async function getQuestionnaireResponse(clientProjectId: string): Promise<QuestionnaireResponse | null> {
  try {
    const { data, error } = await supabase
      .from('questionnaire_responses')
      .select('*')
      .eq('client_project_id', clientProjectId)
      .single();

    if (error) {
      if (error.code === 'PGRST116') { // No rows returned
        return null;
      }
      console.error(`Error fetching questionnaire response for client project ${clientProjectId}:`, error);
      throw new Error(`Failed to fetch questionnaire response: ${error.message}`);
    }

    return data;
  } catch (error: any) {
    console.error(`Unexpected error fetching questionnaire response for client project ${clientProjectId}:`, error);
    throw new Error(`Failed to fetch questionnaire response: ${error.message}`);
  }
}

// Create a new questionnaire response
export async function createQuestionnaireResponse(clientProjectId: string): Promise<QuestionnaireResponse> {
  try {
    const { data, error } = await supabase
      .from('questionnaire_responses')
      .insert([{
        client_project_id: clientProjectId,
        completion_status: 'in_progress',
        last_section_completed: 0
      }])
      .select()
      .single();

    if (error) {
      console.error('Error creating questionnaire response:', error);
      throw new Error(`Failed to create questionnaire response: ${error.message}`);
    }

    return data;
  } catch (error: any) {
    console.error('Unexpected error creating questionnaire response:', error);
    throw new Error(`Failed to create questionnaire response: ${error.message}`);
  }
}

// Update general information (Section 1)
export async function updateGeneralInformation(
  responseId: string,
  formData: {
    project_name: string;
    project_description: string;
    target_audience: string;
    project_goals: string[];
    color_scheme: string;
    has_logo_or_brand_guidelines: boolean;
    preferred_launch_timeline: string;
  }
): Promise<QuestionnaireResponse> {
  try {
    console.log(`Updating general information for response ${responseId}`);
    console.log('Form data:', JSON.stringify(formData, null, 2));

    // Check if Supabase URL and key are available
    if (!supabaseUrl || !supabaseAnonKey) {
      console.error('Missing Supabase credentials in updateGeneralInformation');
      throw new Error('Database configuration error');
    }

    // Check if responseId is valid
    if (!responseId) {
      console.error('Invalid responseId provided to updateGeneralInformation');
      throw new Error('Invalid response ID');
    }

    // First, get the existing record to get the client_project_id
    const { data: existingData, error: fetchError } = await supabase
      .from('questionnaire_responses')
      .select('client_project_id')
      .eq('id', responseId)
      .single();

    if (fetchError) {
      console.error(`Error fetching existing questionnaire response ${responseId}:`, fetchError);
      throw new Error(`Failed to fetch existing questionnaire response: ${fetchError.message}`);
    }

    if (!existingData || !existingData.client_project_id) {
      console.error(`No client_project_id found for questionnaire response ${responseId}`);
      throw new Error('No client_project_id found');
    }

    // Ensure field names match column names in the database
    const updateData = {
      client_project_id: existingData.client_project_id, // Include client_project_id
      project_name: formData.project_name,
      project_description: formData.project_description,
      target_audience: formData.target_audience,
      project_goals: formData.project_goals,
      // Save the full color scheme label instead of just the ID
      color_scheme: getColorSchemeLabel(formData.color_scheme),
      has_logo_or_brand_guidelines: formData.has_logo_or_brand_guidelines,
      preferred_launch_timeline: formData.preferred_launch_timeline,
      last_section_completed: 1,
      updated_at: new Date().toISOString()
    };

    console.log('Update data:', JSON.stringify(updateData, null, 2));

    const { data, error } = await supabase
      .from('questionnaire_responses')
      .upsert({
        id: responseId,
        ...updateData
      })
      .select()
      .single();

    if (error) {
      console.error(`Error updating general information for questionnaire response ${responseId}:`, error);
      throw new Error(`Failed to update general information: ${error.message}`);
    }

    if (!data) {
      console.error(`No data returned when updating general information for response ${responseId}`);
      throw new Error('No data returned from database');
    }

    console.log('Successfully updated general information:', data);
    return data;
  } catch (error: any) {
    console.error(`Unexpected error updating general information for questionnaire response ${responseId}:`, error);
    // Log more details about the error
    console.error('Error details:', {
      name: error.name,
      message: error.message,
      stack: error.stack,
      code: error.code,
      details: error.details
    });
    throw new Error(`Failed to update general information: ${error.message}`);
  }
}

// Update package-specific information (Section 2)
export async function updatePackageInformation(
  responseId: string,
  packageType: PackageType,
  formData: any
): Promise<QuestionnaireResponse> {
  try {
    console.log(`Updating package information for response ${responseId} with package type ${packageType}`);
    console.log('Form data:', JSON.stringify(formData, null, 2));

    // Check if Supabase URL and key are available
    if (!supabaseUrl || !supabaseAnonKey) {
      console.error('Missing Supabase credentials in updatePackageInformation');
      throw new Error('Database configuration error');
    }

    // Check if responseId is valid
    if (!responseId) {
      console.error('Invalid responseId provided to updatePackageInformation');
      throw new Error('Invalid response ID');
    }

    // First, get the existing record to get the client_project_id
    const { data: existingData, error: fetchError } = await supabase
      .from('questionnaire_responses')
      .select('client_project_id')
      .eq('id', responseId)
      .single();

    if (fetchError) {
      console.error(`Error fetching existing questionnaire response ${responseId}:`, fetchError);
      throw new Error(`Failed to fetch existing questionnaire response: ${fetchError.message}`);
    }

    if (!existingData || !existingData.client_project_id) {
      console.error(`No client_project_id found for questionnaire response ${responseId}`);
      throw new Error('No client_project_id found');
    }

    // Create a base update object with common fields
    const updateData: any = {
      client_project_id: existingData.client_project_id, // Include client_project_id
      // Don't set package_type as it doesn't exist in the database schema
      last_section_completed: 2,
      completion_status: 'completed',
      updated_at: new Date().toISOString()
    };

    // Add basic package fields
    if (formData.requested_pages) updateData.requested_pages = formData.requested_pages;
    if (formData.has_content_ready !== undefined) updateData.has_content_ready = formData.has_content_ready;
    if (formData.contact_form_fields) updateData.contact_form_fields = formData.contact_form_fields;
    if (formData.has_domain_name !== undefined) updateData.has_domain_name = formData.has_domain_name;
    if (formData.preferred_hosting) updateData.preferred_hosting = formData.preferred_hosting;

    // Add standard package fields if applicable
    if (packageType === 'standard' || packageType === 'premium') {
      if (formData.blog_content_type) updateData.blog_content_type = formData.blog_content_type;
      if (formData.blog_update_frequency) updateData.blog_update_frequency = formData.blog_update_frequency;
      if (formData.cms_preference) updateData.cms_preference = formData.cms_preference;
      if (formData.auth_features) updateData.auth_features = formData.auth_features;
      if (formData.design_references) updateData.design_references = formData.design_references;
    }

    // Add premium package fields if applicable
    if (packageType === 'premium') {
      if (formData.ecommerce_features) updateData.ecommerce_features = formData.ecommerce_features;
      if (formData.payment_methods) updateData.payment_methods = formData.payment_methods;
      if (formData.third_party_apis) updateData.third_party_apis = formData.third_party_apis;
      if (formData.database_requirements) updateData.database_requirements = formData.database_requirements;
      if (formData.admin_panel_features) updateData.admin_panel_features = formData.admin_panel_features;
      if (formData.needs_user_roles !== undefined) updateData.needs_user_roles = formData.needs_user_roles;
    }

    console.log('Update data:', JSON.stringify(updateData, null, 2));

    const { data, error } = await supabase
      .from('questionnaire_responses')
      .upsert({
        id: responseId,
        ...updateData
      })
      .select()
      .single();

    if (error) {
      console.error(`Error updating package information for questionnaire response ${responseId}:`, error);
      throw new Error(`Failed to update package information: ${error.message}`);
    }

    if (!data) {
      console.error(`No data returned when updating package information for response ${responseId}`);
      throw new Error('No data returned from database');
    }

    console.log('Successfully updated package information:', data);
    return data;
  } catch (error: any) {
    console.error(`Unexpected error updating package information for questionnaire response ${responseId}:`, error);
    // Log more details about the error
    console.error('Error details:', {
      name: error.name,
      message: error.message,
      stack: error.stack,
      code: error.code,
      details: error.details
    });
    throw new Error(`Failed to update package information: ${error.message}`);
  }
}

// Get client project details
export async function getClientProject(clientProjectId: string) {
  try {
    const { data, error } = await supabase
      .from('client_projects')
      .select('*')
      .eq('id', clientProjectId)
      .single();

    if (error) {
      console.error(`Error fetching client project ${clientProjectId}:`, error);
      throw new Error(`Failed to fetch client project: ${error.message}`);
    }

    return data;
  } catch (error: any) {
    console.error(`Unexpected error fetching client project ${clientProjectId}:`, error);
    throw new Error(`Failed to fetch client project: ${error.message}`);
  }
}
