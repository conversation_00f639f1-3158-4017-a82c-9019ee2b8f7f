'use client';

import { motion } from 'framer-motion';
import { CheckCircle2 } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import Link from 'next/link';
import { ADDITIONAL_SERVICES, MAINTENANCE_PLANS, calculateDepositAmount } from '@/lib/mpesa/types';

interface ProjectRequestConfirmationProps {
  clientName: string;
  clientEmail: string;
  clientPhone: string;
  referenceCode: string;
  packageType: 'basic' | 'standard' | 'premium';
  totalAmount: number;
  additionalServices?: string[];
  maintenancePlan?: string | null;
  isNetworkError?: boolean;
}

export default function ProjectRequestConfirmation({
  clientName,
  clientEmail,
  clientPhone,
  referenceCode,
  packageType,
  totalAmount,
  additionalServices = [],
  maintenancePlan = null,
  isNetworkError = false,
}: ProjectRequestConfirmationProps) {
  // Format package type for display
  const formatPackageType = (type: string) => {
    return type.charAt(0).toUpperCase() + type.slice(1);
  };

  // Format currency
  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-KE', {
      style: 'currency',
      currency: 'KES',
    }).format(amount);
  };

  // Calculate deposit amount
  const depositAmount = calculateDepositAmount(totalAmount);

  // Format additional services
  const getAdditionalServicesText = () => {
    if (!additionalServices || additionalServices.length === 0) {
      return 'None';
    }

    return additionalServices.map(serviceId => {
      const service = ADDITIONAL_SERVICES.find(s => s.id === serviceId);
      return service ? service.name : serviceId;
    }).join(', ');
  };

  // Format maintenance plan
  const getMaintenancePlanText = () => {
    if (!maintenancePlan) {
      return 'None';
    }

    const plan = MAINTENANCE_PLANS.find(p => p.id === maintenancePlan);
    return plan ? `${plan.name} (${formatCurrency(plan.price)}/month)` : maintenancePlan;
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
      className="max-w-3xl mx-auto"
    >
      <Card className="border border-green-200 dark:border-green-900 bg-white dark:bg-gray-800 shadow-md">
        <CardContent className="pt-6 px-6 pb-6">
          <div className="flex flex-col items-center text-center mb-8">
            <motion.div
              initial={{ scale: 0, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              transition={{ delay: 0.2, type: 'spring', stiffness: 200 }}
              className="relative mb-6"
            >
              <div className="absolute inset-0 bg-green-100 dark:bg-green-900/30 rounded-full scale-[1.6] blur-md animate-pulse" />
              <div className="relative bg-white dark:bg-gray-800 rounded-full p-4 shadow-lg border border-green-200 dark:border-green-800">
                <CheckCircle2 className="h-16 w-16 text-green-500" />
              </div>
            </motion.div>
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.4, duration: 0.5 }}
            >
              <h2 className="text-3xl font-bold text-gray-900 dark:text-gray-100 mb-3 bg-gradient-to-r from-green-500 to-blue-500 bg-clip-text text-transparent">
                Project Request Submitted!
              </h2>
              <p className="text-gray-600 dark:text-gray-300 max-w-lg text-lg">
                Thank you for your interest in working with me. I'll review your request and contact you soon via WhatsApp or email to discuss your project in detail.
              </p>
            </motion.div>
          </div>

          <div className="bg-gradient-to-b from-gray-50 to-white dark:from-gray-900 dark:to-gray-800 rounded-lg p-6 mb-6 shadow-md border border-gray-100 dark:border-gray-700">
            <div className="flex items-center gap-3 mb-4 border-b pb-3">
              <div className="bg-primary/10 p-2 rounded-full">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-primary" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M10 2a8 8 0 100 16 8 8 0 000-16zm0 14a6 6 0 100-12 6 6 0 000 12zm.75-6.75a.75.75 0 00-1.5 0v2.25h-2.25a.75.75 0 000 1.5h2.25v2.25a.75.75 0 001.5 0v-2.25h2.25a.75.75 0 000-1.5h-2.25V9.25z" clipRule="evenodd" />
                </svg>
              </div>
              <h3 className="font-bold text-xl text-gray-900 dark:text-gray-100">
                Your Request Details
              </h3>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {/* Client Information */}
              <motion.div
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ delay: 0.5, duration: 0.4 }}
                className="bg-white dark:bg-gray-800/50 rounded-lg border border-blue-100 dark:border-blue-900/30 p-4 shadow-sm"
              >
                <div className="flex items-center gap-2 mb-3 pb-2 border-b border-blue-50 dark:border-blue-900/20">
                  <div className="bg-blue-100 dark:bg-blue-900/30 p-1.5 rounded-full">
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 text-blue-600 dark:text-blue-400" viewBox="0 0 20 20" fill="currentColor">
                      <path fillRule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z" clipRule="evenodd" />
                    </svg>
                  </div>
                  <h4 className="font-semibold text-blue-700 dark:text-blue-400">Client Information</h4>
                </div>

                <div className="space-y-3">
                  <div className="bg-blue-50/50 dark:bg-blue-900/10 p-2 rounded-md">
                    <span className="text-xs font-medium text-blue-600/70 dark:text-blue-400/70 block mb-1">Name</span>
                    <span className="font-medium text-gray-900 dark:text-gray-100">{clientName}</span>
                  </div>

                  <div className="bg-blue-50/50 dark:bg-blue-900/10 p-2 rounded-md">
                    <span className="text-xs font-medium text-blue-600/70 dark:text-blue-400/70 block mb-1">Email</span>
                    <span className="font-medium text-gray-900 dark:text-gray-100">{clientEmail}</span>
                  </div>

                  <div className="bg-blue-50/50 dark:bg-blue-900/10 p-2 rounded-md">
                    <span className="text-xs font-medium text-blue-600/70 dark:text-blue-400/70 block mb-1">Phone</span>
                    <span className="font-medium text-gray-900 dark:text-gray-100">{clientPhone}</span>
                  </div>

                  <div className="bg-blue-50/50 dark:bg-blue-900/10 p-2 rounded-md">
                    <span className="text-xs font-medium text-blue-600/70 dark:text-blue-400/70 block mb-1">Reference Code</span>
                    <span className="font-medium text-gray-900 dark:text-gray-100 font-mono">{referenceCode}</span>
                  </div>
                </div>
              </motion.div>

              {/* Package Details */}
              <motion.div
                initial={{ opacity: 0, x: 20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ delay: 0.6, duration: 0.4 }}
                className="bg-white dark:bg-gray-800/50 rounded-lg border border-purple-100 dark:border-purple-900/30 p-4 shadow-sm"
              >
                <div className="flex items-center gap-2 mb-3 pb-2 border-b border-purple-50 dark:border-purple-900/20">
                  <div className="bg-purple-100 dark:bg-purple-900/30 p-1.5 rounded-full">
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 text-purple-600 dark:text-purple-400" viewBox="0 0 20 20" fill="currentColor">
                      <path fillRule="evenodd" d="M5 4a3 3 0 00-3 3v6a3 3 0 003 3h10a3 3 0 003-3V7a3 3 0 00-3-3H5zm-1 9v-1h5v2H5a1 1 0 01-1-1zm7 1h4a1 1 0 001-1v-1h-5v2zm0-4h5V8h-5v2zM9 8H4v2h5V8z" clipRule="evenodd" />
                    </svg>
                  </div>
                  <h4 className="font-semibold text-purple-700 dark:text-purple-400">Package Details</h4>
                </div>

                <div className="space-y-3">
                  <div className="bg-purple-50/50 dark:bg-purple-900/10 p-2 rounded-md">
                    <span className="text-xs font-medium text-purple-600/70 dark:text-purple-400/70 block mb-1">Package Type</span>
                    <div className="flex items-center gap-2">
                      <span className={`inline-flex items-center justify-center w-6 h-6 rounded-full
                        ${packageType === 'premium'
                          ? 'bg-amber-100 text-amber-700 dark:bg-amber-900/30 dark:text-amber-400'
                          : packageType === 'standard'
                          ? 'bg-purple-100 text-purple-700 dark:bg-purple-900/30 dark:text-purple-400'
                          : 'bg-blue-100 text-blue-700 dark:bg-blue-900/30 dark:text-blue-400'
                        }`}>
                        {formatPackageType(packageType).charAt(0)}
                      </span>
                      <span className="font-medium text-gray-900 dark:text-gray-100">
                        {formatPackageType(packageType)}
                      </span>
                    </div>
                  </div>

                  <div className="bg-purple-50/50 dark:bg-purple-900/10 p-2 rounded-md">
                    <span className="text-xs font-medium text-purple-600/70 dark:text-purple-400/70 block mb-1">Additional Services</span>
                    {additionalServices && additionalServices.length > 0 ? (
                      <div className="flex flex-wrap gap-1 mt-1">
                        {additionalServices.map(serviceId => {
                          const service = ADDITIONAL_SERVICES.find(s => s.id === serviceId);
                          return service ? (
                            <span key={serviceId} className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200">
                              {service.name}
                            </span>
                          ) : null;
                        })}
                      </div>
                    ) : (
                      <span className="font-medium text-gray-900 dark:text-gray-100">None</span>
                    )}
                  </div>

                  <div className="bg-purple-50/50 dark:bg-purple-900/10 p-2 rounded-md">
                    <span className="text-xs font-medium text-purple-600/70 dark:text-purple-400/70 block mb-1">Maintenance Plan</span>
                    <span className="font-medium text-gray-900 dark:text-gray-100">
                      {getMaintenancePlanText()}
                    </span>
                  </div>
                </div>
              </motion.div>
            </div>

            {/* Pricing Information */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.7, duration: 0.4 }}
              className="mt-6 pt-4 border-t"
            >
              <div className="flex items-center gap-2 mb-4">
                <div className="bg-green-100 dark:bg-green-900/30 p-1.5 rounded-full">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 text-green-600 dark:text-green-400" viewBox="0 0 20 20" fill="currentColor">
                    <path d="M8.433 7.418c.155-.103.346-.196.567-.267v1.698a2.305 2.305 0 01-.567-.267C8.07 8.34 8 8.114 8 8c0-.114.07-.34.433-.582zM11 12.849v-1.698c.22.071.412.164.567.267.364.243.433.468.433.582 0 .114-.07.34-.433.582a2.305 2.305 0 01-.567.267z" />
                    <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-13a1 1 0 10-2 0v.092a4.535 4.535 0 00-1.676.662C6.602 6.234 6 7.009 6 8c0 .99.602 1.765 1.324 *********** 1.054.545 1.676.662v1.941c-.391-.127-.68-.317-.843-.504a1 1 0 10-1.51 1.31c.562.649 1.413 1.076 2.353 1.253V15a1 1 0 102 0v-.092a4.535 4.535 0 001.676-.662C13.398 13.766 14 12.991 14 12c0-.99-.602-1.765-1.324-2.246A4.535 4.535 0 0011 9.092V7.151c.391.127.68.317.843.504a1 1 0 101.511-1.31c-.563-.649-1.413-1.076-2.354-1.253V5z" clipRule="evenodd" />
                  </svg>
                </div>
                <h4 className="font-semibold text-green-700 dark:text-green-400">Pricing Information</h4>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="bg-white dark:bg-gray-800 p-4 rounded-lg shadow-sm border border-green-100 dark:border-green-900/30 relative overflow-hidden">
                  <div className="absolute inset-0 bg-gradient-to-r from-green-50 to-transparent dark:from-green-900/20 dark:to-transparent"></div>
                  <div className="relative">
                    <span className="text-sm text-green-600 dark:text-green-400 font-medium block mb-1">Total Cost</span>
                    <span className="font-bold text-2xl text-gray-900 dark:text-gray-100">
                      {formatCurrency(totalAmount)}
                    </span>
                    <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">Full project cost</p>
                  </div>
                </div>

                <div className="bg-white dark:bg-gray-800 p-4 rounded-lg shadow-sm border border-green-100 dark:border-green-900/30 relative overflow-hidden">
                  <div className="absolute inset-0 bg-gradient-to-r from-green-50 to-transparent dark:from-green-900/20 dark:to-transparent"></div>
                  <div className="relative">
                    <span className="text-sm text-green-600 dark:text-green-400 font-medium block mb-1">Deposit Amount (40%)</span>
                    <span className="font-bold text-2xl text-gray-900 dark:text-gray-100">
                      {formatCurrency(depositAmount)}
                    </span>
                    <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">Required to start the project</p>
                  </div>
                </div>
              </div>
            </motion.div>
          </div>

          {isNetworkError && (
            <div className="bg-amber-50 dark:bg-amber-900/30 rounded-lg p-5 mb-6 shadow-sm border border-amber-200 dark:border-amber-800">
              <div className="flex items-start">
                <div className="flex-shrink-0 mt-0.5">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-amber-600 dark:text-amber-400" viewBox="0 0 20 20" fill="currentColor">
                    <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                  </svg>
                </div>
                <div className="ml-3">
                  <h3 className="font-semibold text-lg mb-2 text-amber-800 dark:text-amber-300">
                    Important Notice
                  </h3>
                  <p className="text-amber-700 dark:text-amber-300 text-sm mb-2">
                    Due to a network issue, your project request couldn't be saved to our database.
                    Please contact me directly with your information using the reference code above.
                  </p>
                  <p className="text-amber-700 dark:text-amber-300 text-sm">
                    You can reach me at <span className="font-medium"><EMAIL></span> or via WhatsApp at <span className="font-medium">0796 564 593</span>.
                  </p>
                </div>
              </div>
            </div>
          )}

          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.8, duration: 0.5 }}
            className="bg-gradient-to-br from-blue-50 to-blue-50/50 dark:from-blue-900/30 dark:to-blue-900/10 rounded-lg p-6 mb-6 shadow-md border border-blue-100 dark:border-blue-800"
          >
            <div className="flex items-center gap-3 mb-5 border-b border-blue-100 dark:border-blue-800/50 pb-3">
              <div className="bg-blue-100 dark:bg-blue-800/50 p-2 rounded-full">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-blue-600 dark:text-blue-400" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clipRule="evenodd" />
                </svg>
              </div>
              <h3 className="font-bold text-xl text-blue-800 dark:text-blue-300">
                What happens next?
              </h3>
            </div>

            <div className="relative pl-8 before:content-[''] before:absolute before:left-3 before:top-0 before:bottom-0 before:w-0.5 before:bg-blue-200 dark:before:bg-blue-800/70">
              <div className="relative mb-8">
                <div className="absolute left-[-28px] flex items-center justify-center h-10 w-10 rounded-full bg-blue-100 dark:bg-blue-800/70 text-blue-600 dark:text-blue-300 border-4 border-white dark:border-gray-800 shadow-sm">
                  <span className="text-sm font-bold">1</span>
                </div>
                <div className="bg-white dark:bg-gray-800/50 rounded-lg p-4 shadow-sm border border-blue-100 dark:border-blue-900/30 ml-2">
                  <p className="font-semibold text-blue-700 dark:text-blue-300 text-base">Review</p>
                  <p className="text-gray-600 dark:text-gray-300 mt-1">I'll review your project request details and prepare for our discussion.</p>
                  <div className="mt-2 text-xs text-blue-600/70 dark:text-blue-400/70 flex items-center">
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1" viewBox="0 0 20 20" fill="currentColor">
                      <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clipRule="evenodd" />
                    </svg>
                    Within 24 hours
                  </div>
                </div>
              </div>

              <div className="relative mb-8">
                <div className="absolute left-[-28px] flex items-center justify-center h-10 w-10 rounded-full bg-blue-100 dark:bg-blue-800/70 text-blue-600 dark:text-blue-300 border-4 border-white dark:border-gray-800 shadow-sm">
                  <span className="text-sm font-bold">2</span>
                </div>
                <div className="bg-white dark:bg-gray-800/50 rounded-lg p-4 shadow-sm border border-blue-100 dark:border-blue-900/30 ml-2">
                  <p className="font-semibold text-blue-700 dark:text-blue-300 text-base">Discussion</p>
                  <p className="text-gray-600 dark:text-gray-300 mt-1">I'll contact you via WhatsApp or email to discuss your project requirements in detail.</p>
                  <div className="mt-2 text-xs text-blue-600/70 dark:text-blue-400/70 flex items-center">
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1" viewBox="0 0 20 20" fill="currentColor">
                      <path d="M2 5a2 2 0 012-2h7a2 2 0 012 2v4a2 2 0 01-2 2H9l-3 3v-3H4a2 2 0 01-2-2V5z" />
                      <path d="M15 7v2a4 4 0 01-4 4H9.828l-1.766 1.767c.28.149.599.233.938.233h2l3 3v-3h2a2 2 0 002-2V9a2 2 0 00-2-2h-1z" />
                    </svg>
                    Personalized consultation
                  </div>
                </div>
              </div>

              <div className="relative mb-8">
                <div className="absolute left-[-28px] flex items-center justify-center h-10 w-10 rounded-full bg-blue-100 dark:bg-blue-800/70 text-blue-600 dark:text-blue-300 border-4 border-white dark:border-gray-800 shadow-sm">
                  <span className="text-sm font-bold">3</span>
                </div>
                <div className="bg-white dark:bg-gray-800/50 rounded-lg p-4 shadow-sm border border-blue-100 dark:border-blue-900/30 ml-2">
                  <p className="font-semibold text-blue-700 dark:text-blue-300 text-base">Access Code</p>
                  <p className="text-gray-600 dark:text-gray-300 mt-1">After our discussion, I'll provide you with an access code to complete the detailed questionnaire.</p>
                  <div className="mt-2 text-xs text-blue-600/70 dark:text-blue-400/70 flex items-center">
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1" viewBox="0 0 20 20" fill="currentColor">
                      <path fillRule="evenodd" d="M5 9V7a5 5 0 0110 0v2a2 2 0 012 2v5a2 2 0 01-2 2H5a2 2 0 01-2-2v-5a2 2 0 012-2zm8-2v2H7V7a3 3 0 016 0z" clipRule="evenodd" />
                    </svg>
                    Secure access to questionnaire
                  </div>
                </div>
              </div>

              <div className="relative">
                <div className="absolute left-[-28px] flex items-center justify-center h-10 w-10 rounded-full bg-blue-100 dark:bg-blue-800/70 text-blue-600 dark:text-blue-300 border-4 border-white dark:border-gray-800 shadow-sm">
                  <span className="text-sm font-bold">4</span>
                </div>
                <div className="bg-white dark:bg-gray-800/50 rounded-lg p-4 shadow-sm border border-blue-100 dark:border-blue-900/30 ml-2">
                  <p className="font-semibold text-blue-700 dark:text-blue-300 text-base">Development</p>
                  <p className="text-gray-600 dark:text-gray-300 mt-1">Once the questionnaire is completed, I'll begin working on your project with regular updates.</p>
                  <div className="mt-2 text-xs text-blue-600/70 dark:text-blue-400/70 flex items-center">
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1" viewBox="0 0 20 20" fill="currentColor">
                      <path fillRule="evenodd" d="M11.3 1.046A1 1 0 0112 2v5h4a1 1 0 01.82 1.573l-7 10A1 1 0 018 18v-5H4a1 1 0 01-.82-1.573l7-10a1 1 0 011.12-.38z" clipRule="evenodd" />
                    </svg>
                    Project kickoff
                  </div>
                </div>
              </div>
            </div>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.9, duration: 0.5 }}
            className="flex flex-col sm:flex-row justify-center gap-4 mt-8"
          >
            <Link href="/" passHref>
              <Button
                variant="outline"
                className="w-full sm:w-auto px-6 py-6 text-base font-medium border-2 border-gray-300 hover:bg-gray-100 dark:border-gray-600 dark:hover:bg-gray-800 transition-all duration-200 shadow-sm hover:shadow"
              >
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" viewBox="0 0 20 20" fill="currentColor">
                  <path d="M10.707 2.293a1 1 0 00-1.414 0l-7 7a1 1 0 001.414 1.414L4 10.414V17a1 1 0 001 1h2a1 1 0 001-1v-2a1 1 0 011-1h2a1 1 0 011 1v2a1 1 0 001 1h2a1 1 0 001-1v-6.586l.293.293a1 1 0 001.414-1.414l-7-7z" />
                </svg>
                Return to Homepage
              </Button>
            </Link>
            {/* Desktop Contact Button with Hover Popup */}
            <div className="relative hidden sm:block group">
              <Button
                className="w-auto px-6 py-6 text-base font-medium bg-primary hover:bg-primary/90 transition-all duration-200 shadow-md hover:shadow-lg"
              >
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" viewBox="0 0 20 20" fill="currentColor">
                  <path d="M2.003 5.884L10 9.882l7.997-3.998A2 2 0 0016 4H4a2 2 0 00-1.997 1.884z" />
                  <path d="M18 8.118l-8 4-8-4V14a2 2 0 002 2h12a2 2 0 002-2V8.118z" />
                </svg>
                Contact Me
              </Button>

              {/* Contact Information Popup (Desktop) */}
              <div className="absolute bottom-full mb-2 right-0 w-64 bg-white dark:bg-gray-800 rounded-lg shadow-lg p-4 border border-gray-200 dark:border-gray-700 opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200 z-10">
                <div className="absolute bottom-[-8px] right-8 w-4 h-4 bg-white dark:bg-gray-800 border-r border-b border-gray-200 dark:border-gray-700 transform rotate-45"></div>
                <div className="space-y-3">
                  <div className="flex items-center gap-2">
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-primary" viewBox="0 0 20 20" fill="currentColor">
                      <path d="M2.003 5.884L10 9.882l7.997-3.998A2 2 0 0016 4H4a2 2 0 00-1.997 1.884z" />
                      <path d="M18 8.118l-8 4-8-4V14a2 2 0 002 2h12a2 2 0 002-2V8.118z" />
                    </svg>
                    <a href="mailto:<EMAIL>" className="text-sm font-medium hover:text-primary transition-colors">
                      <EMAIL>
                    </a>
                  </div>
                  <div className="flex items-center gap-2">
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-primary" viewBox="0 0 20 20" fill="currentColor">
                      <path d="M2 3a1 1 0 011-1h2.153a1 1 0 01.986.836l.74 4.435a1 1 0 01-.54 1.06l-1.548.773a11.037 11.037 0 006.105 6.105l.774-1.548a1 1 0 011.059-.54l4.435.74a1 1 0 01.836.986V17a1 1 0 01-1 1h-2C7.82 18 2 12.18 2 5V3z" />
                    </svg>
                    <a href="tel:+254796564593" className="text-sm font-medium hover:text-primary transition-colors">
                      0796 564 593
                    </a>
                  </div>
                </div>
              </div>
            </div>

            {/* Mobile Contact Buttons */}
            <div className="flex flex-col sm:hidden gap-3 w-full">
              <a href="mailto:<EMAIL>">
                <Button
                  className="w-full px-6 py-6 text-base font-medium bg-primary hover:bg-primary/90 transition-all duration-200 shadow-md hover:shadow-lg"
                >
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" viewBox="0 0 20 20" fill="currentColor">
                    <path d="M2.003 5.884L10 9.882l7.997-3.998A2 2 0 0016 4H4a2 2 0 00-1.997 1.884z" />
                    <path d="M18 8.118l-8 4-8-4V14a2 2 0 002 2h12a2 2 0 002-2V8.118z" />
                  </svg>
                  Email Me
                </Button>
              </a>

              <a href="tel:+254796564593">
                <Button
                  variant="outline"
                  className="w-full px-6 py-6 text-base font-medium border-2 border-primary text-primary hover:bg-primary/5 transition-all duration-200 shadow-sm hover:shadow"
                >
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" viewBox="0 0 20 20" fill="currentColor">
                    <path d="M2 3a1 1 0 011-1h2.153a1 1 0 01.986.836l.74 4.435a1 1 0 01-.54 1.06l-1.548.773a11.037 11.037 0 006.105 6.105l.774-1.548a1 1 0 011.059-.54l4.435.74a1 1 0 01.836.986V17a1 1 0 01-1 1h-2C7.82 18 2 12.18 2 5V3z" />
                  </svg>
                  Call Me: 0796 564 593
                </Button>
              </a>
            </div>
          </motion.div>
        </CardContent>
      </Card>
    </motion.div>
  );
}
