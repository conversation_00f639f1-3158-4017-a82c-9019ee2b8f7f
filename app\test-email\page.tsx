'use client';

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { toast } from 'sonner';
import { testEmailBasicNotification, testEmailBasicTwoNotification } from '@/app/actions/test-email-actions';

export default function TestEmailPage() {
  const [isTestingBasic, setIsTestingBasic] = useState(false);
  const [isTestingBasicTwo, setIsTestingBasicTwo] = useState(false);

  const handleTestEmailBasic = async () => {
    setIsTestingBasic(true);
    try {
      console.log('Starting email test for EmailBasic...');
      const result = await testEmailBasicNotification();
      console.log('Test result:', result);

      if (result.success) {
        toast.success('Email notification sent successfully! Check your email.');
        console.log('✅ Email test successful');
      } else {
        console.error('❌ Email test failed:', result.error);
        toast.error(`Failed to send email: ${result.error}`);
      }
    } catch (error) {
      console.error('❌ Error testing email:', error);
      toast.error(`An unexpected error occurred: ${error instanceof Error ? error.message : 'Unknown error'}`);
    } finally {
      setIsTestingBasic(false);
    }
  };

  const handleTestEmailBasicTwo = async () => {
    setIsTestingBasicTwo(true);
    try {
      const result = await testEmailBasicTwoNotification();
      if (result.success) {
        toast.success('Email notification sent successfully! Check your email.');
      } else {
        toast.error(`Failed to send email: ${result.error}`);
      }
    } catch (error) {
      console.error('Error testing email:', error);
      toast.error('An unexpected error occurred');
    } finally {
      setIsTestingBasicTwo(false);
    }
  };

  return (
    <div className="container mx-auto px-4 py-12">
      <div className="max-w-2xl mx-auto">
        <h1 className="text-3xl font-bold text-center mb-8">Test Email Notifications</h1>

        <div className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Test Project Request Notifications</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center justify-between p-4 border rounded-lg">
                <div>
                  <h3 className="font-semibold">EmailBasic Project</h3>
                  <p className="text-sm text-muted-foreground">
                    Client: EmailBasic (<EMAIL>)
                  </p>
                  <p className="text-sm text-muted-foreground">
                    Reference: ZO073166429
                  </p>
                </div>
                <Button
                  onClick={handleTestEmailBasic}
                  disabled={isTestingBasic}
                >
                  {isTestingBasic ? 'Sending...' : 'Send Test Email'}
                </Button>
              </div>

              <div className="flex items-center justify-between p-4 border rounded-lg">
                <div>
                  <h3 className="font-semibold">EmailBasicTwo Project</h3>
                  <p className="text-sm text-muted-foreground">
                    Client: EmailBasicTwo (<EMAIL>)
                  </p>
                  <p className="text-sm text-muted-foreground">
                    Reference: ZO270601267
                  </p>
                </div>
                <Button
                  onClick={handleTestEmailBasicTwo}
                  disabled={isTestingBasicTwo}
                >
                  {isTestingBasicTwo ? 'Sending...' : 'Send Test Email'}
                </Button>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Test Contact Form (Working)</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <p className="text-sm text-muted-foreground">
                  Test the working contact form to verify email configuration:
                </p>
                <Button
                  onClick={() => window.open('/contact', '_blank')}
                  variant="outline"
                >
                  Open Contact Form
                </Button>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Instructions</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-2 text-sm">
                <p>1. First test the contact form to verify email is working</p>
                <p>2. Then click the "Send Test Email" button for either project</p>
                <p>3. Check your email (<EMAIL>) for the notification</p>
                <p>4. If successful, the email notifications are working correctly</p>
                <p>5. If failed, check the browser console for error details</p>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}
