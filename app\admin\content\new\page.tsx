"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import { toast } from "sonner";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { ContentSection, ContentKey } from "@/lib/cms/types";

export default function NewContentPage() {
  const router = useRouter();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [section, setSection] = useState<ContentSection | "">("");
  const [key, setKey] = useState<ContentKey | "">("");
  const [content, setContent] = useState("");

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!section || !key || !content) {
      toast.error("Please fill in all fields");
      return;
    }

    setIsSubmitting(true);

    try {
      const res = await fetch("/api/cms/content", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          section,
          key,
          content,
        }),
      });

      if (!res.ok) {
        const error = await res.json();
        throw new Error(error.error || "Failed to create content");
      }

      toast.success("Content created successfully");
      router.push("/admin/content");
    } catch (error: any) {
      console.error("Error creating content:", error);
      toast.error(error.message || "Failed to create content");
    } finally {
      setIsSubmitting(false);
    }
  };

  // Define available keys based on selected section
  const getKeysForSection = (section: ContentSection): ContentKey[] => {
    switch (section) {
      case "hero":
        return ["title", "subtitle", "description"];
      case "about":
        return ["bio", "education", "experience"];
      case "projects":
        return [
          "project_intro",
          "project_1_title", "project_1_description", "project_1_tags", "project_1_github", "project_1_demo",
          "project_2_title", "project_2_description", "project_2_tags", "project_2_github", "project_2_demo",
          "project_3_title", "project_3_description", "project_3_tags", "project_3_github", "project_3_demo",
          "project_4_title", "project_4_description", "project_4_tags", "project_4_github", "project_4_demo",
        ];
      case "skills":
        return ["skills_intro", "frontend_skills", "backend_skills", "other_skills"];
      case "contact":
        return ["contact_intro", "email", "linkedin", "github"];
      case "general":
        return ["site_title", "site_description", "meta_keywords"];
      default:
        return [];
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-3xl font-bold">Add New Content</h1>
        <Button variant="outline" onClick={() => router.back()}>
          Cancel
        </Button>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Content Details</CardTitle>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleSubmit} className="space-y-6">
            <div className="space-y-2">
              <Label htmlFor="section">Section</Label>
              <Select
                value={section}
                onValueChange={(value) => {
                  setSection(value as ContentSection);
                  setKey(""); // Reset key when section changes
                }}
              >
                <SelectTrigger id="section">
                  <SelectValue placeholder="Select section" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="hero">Hero</SelectItem>
                  <SelectItem value="about">About</SelectItem>
                  <SelectItem value="projects">Projects</SelectItem>
                  <SelectItem value="skills">Skills</SelectItem>
                  <SelectItem value="contact">Contact</SelectItem>
                  <SelectItem value="general">General</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="key">Key</Label>
              <Select
                value={key}
                onValueChange={(value) => setKey(value as ContentKey)}
                disabled={!section}
              >
                <SelectTrigger id="key">
                  <SelectValue placeholder="Select key" />
                </SelectTrigger>
                <SelectContent>
                  {section &&
                    getKeysForSection(section as ContentSection).map((k) => (
                      <SelectItem key={k} value={k}>
                        {k.replace(/_/g, " ")}
                      </SelectItem>
                    ))}
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="content">Content</Label>
              <Textarea
                id="content"
                value={content}
                onChange={(e) => setContent(e.target.value)}
                rows={10}
                placeholder="Enter content here..."
              />
            </div>

            <Button type="submit" disabled={isSubmitting}>
              {isSubmitting ? "Creating..." : "Create Content"}
            </Button>
          </form>
        </CardContent>
      </Card>
    </div>
  );
}
