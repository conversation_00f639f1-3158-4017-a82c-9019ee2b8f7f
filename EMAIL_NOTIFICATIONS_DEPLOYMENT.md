# Email Notifications Deployment Guide

This guide will help you deploy the new email notification Edge Functions to Supabase.

## Overview

I've implemented email notifications for:

1. **Project Request Submissions** - You'll get notified when a client submits a project request
2. **Questionnaire Completions** - You'll get notified when a client completes their questionnaire

## Files Created/Modified

### New Files:

- `lib/email-notifications.ts` - Helper functions for sending notifications
- `supabase/functions/send-project-request-notification/index.ts` - Edge Function for project request notifications
- `supabase/functions/send-questionnaire-completion-notification/index.ts` - Edge Function for questionnaire completion notifications

### Modified Files:

- `app/actions/project-request-actions.ts` - Added email notification when project requests are created
- `app/actions/questionnaire-actions.ts` - Added email notification when questionnaires are completed
- `package.json` - Added deployment scripts

## Deployment Steps

### ✅ COMPLETED - Edge Functions Deployed

The Edge Functions have been successfully deployed using the Supabase MCP server:

1. **✅ Project Request Notification Function**

   - **Name**: `send-project-request-notification`
   - **Status**: ACTIVE
   - **ID**: 906ad26e-c88b-4896-abd9-d06d543eabf0
   - **Deployed**: Successfully

2. **✅ Questionnaire Completion Notification Function**

   - **Name**: `send-questionnaire-completion-notification`
   - **Status**: ACTIVE
   - **ID**: e9daa831-8e49-48ab-b42a-100edf2ad62d
   - **Deployed**: Successfully

3. **✅ Environment Variables Verified**
   - `RESEND_API_KEY`: Configured and working
   - `RECIPIENT_EMAIL`: <NAME_EMAIL>

### Manual Deployment (Alternative Method)

If you need to redeploy or modify the functions manually:

1. **Go to your Supabase Dashboard**

   - Visit: https://supabase.com/dashboard/project/caogszaytzuiqwwkbhhi
   - Navigate to "Edge Functions" in the left sidebar

2. **Edit Existing Functions**
   - Click on the function name to edit
   - Modify the code as needed
   - Click "Deploy" to update

### Option 2: Using Supabase CLI (If available)

If you have the Supabase CLI working, you can use these commands:

```bash
# Deploy project request notification function
npm run supabase:deploy:project-notification

# Deploy questionnaire completion notification function
npm run supabase:deploy:questionnaire-notification

# Deploy all functions at once
npm run supabase:deploy:all
```

## Testing the Implementation

### Test Project Request Notifications:

1. Go to your portfolio website
2. Navigate to the "Start Project" page
3. Fill out and submit a project request
4. Check your email (<EMAIL>) for the notification

### Test Questionnaire Completion Notifications:

1. Generate an access token for a client in your CMS
2. Use the access token to access the questionnaire
3. Complete all sections of the questionnaire
4. Check your email for the completion notification

## Email Templates

### Project Request Notification Email:

- **Subject**: 🚀 New Project Request: [Package Type] - [Client Name]
- **Content**: Client information, project details, submission time, and next steps
- **Styling**: Professional HTML email with color-coded sections

### Questionnaire Completion Notification Email:

- **Subject**: ✅ Questionnaire Completed: [Project Name] - [Client Name]
- **Content**: Client information, project overview, completion details, and next steps
- **Styling**: Professional HTML email with organized sections

## Error Handling

The email notifications are designed to be non-blocking:

- If email sending fails, the main functionality (project request submission or questionnaire completion) will still work
- Errors are logged to the console for debugging
- Users will still see success messages even if email notifications fail

## Troubleshooting

### If emails are not being sent:

1. **Check Supabase Edge Functions logs**:

   - Go to Supabase Dashboard > Edge Functions
   - Click on the function name
   - Check the "Logs" tab for any errors

2. **Verify Resend API Key**:

   - Ensure `RESEND_API_KEY` is correctly set in Supabase environment variables
   - Test the API key with a simple Resend API call

3. **Check function deployment**:

   - Ensure both Edge Functions are deployed successfully
   - Verify the function names match exactly: `send-project-request-notification` and `send-questionnaire-completion-notification`

4. **Check console logs**:
   - Open browser developer tools when testing
   - Look for any error messages in the console

## Next Steps

After deployment, you should:

1. Test both notification types
2. Customize email templates if needed (modify the Edge Function files)
3. Consider adding more notification types (e.g., when access tokens are generated)
4. Set up email filtering rules in your email client to organize these notifications

## Support

If you encounter any issues:

1. Check the Supabase Edge Functions logs
2. Verify all environment variables are set correctly
3. Test the Resend API key independently
4. Check that the Edge Functions are deployed and accessible
