"use client";

import { useState, useRef } from "react";
import { useRouter } from "next/navigation";
import Image from "next/image";
import { toast } from "sonner";
import { Upload } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { ImageType } from "@/lib/cms/types";
import { useIsMobile } from "@/hooks/use-mobile";

export default function UploadImagePage() {
  const router = useRouter();
  const fileInputRef = useRef<HTMLInputElement>(null);
  const isMobile = useIsMobile();

  const [name, setName] = useState("");
  const [description, setDescription] = useState("");
  const [altText, setAltText] = useState("");
  const [type, setType] = useState<ImageType | "">("");
  const [file, setFile] = useState<File | null>(null);
  const [preview, setPreview] = useState<string | null>(null);
  const [isSubmitting, setIsSubmitting] = useState(false);

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      const selectedFile = e.target.files[0];

      // Check file type
      if (!selectedFile.type.startsWith("image/")) {
        toast.error("Please select an image file");
        return;
      }

      // Check file size (max 5MB)
      if (selectedFile.size > 5 * 1024 * 1024) {
        toast.error("File size should be less than 5MB");
        return;
      }

      setFile(selectedFile);

      // Create preview
      const reader = new FileReader();
      reader.onload = (event) => {
        setPreview(event.target?.result as string);
      };
      reader.readAsDataURL(selectedFile);

      // Auto-fill name if empty
      if (!name) {
        setName(selectedFile.name.split(".")[0].replace(/[_-]/g, " "));
      }
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!name || !altText || !type || !file) {
      toast.error("Please fill in all required fields and select an image");
      return;
    }

    setIsSubmitting(true);

    try {
      // Create a new FormData instance
      const formData = new FormData();
      formData.append("name", name);
      formData.append("alt_text", altText);
      formData.append("type", type);

      // Ensure the file is properly appended
      formData.append("file", file, file.name);

      if (description) {
        formData.append("description", description);
      }

      console.log("Submitting form with file:", file.name, file.type, file.size);

      // Add timeout to prevent quick timeouts
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 30000);

      try {
        const res = await fetch("/api/cms/images", {
          method: "POST",
          body: formData,
          signal: controller.signal
        });

        clearTimeout(timeoutId);

        if (!res.ok) {
          const errorData = await res.json().catch(() => ({ error: `Server error: ${res.status}` }));
          throw new Error(errorData.error || "Failed to upload image");
        }

        toast.success("Image uploaded successfully");
        router.push("/admin/images");
      } catch (fetchError: any) {
        if (fetchError.name === 'AbortError') {
          throw new Error("Upload timed out. Please try again with a smaller image or check your connection.");
        }
        throw fetchError;
      }
    } catch (error: any) {
      console.error("Error uploading image:", error);
      toast.error(error.message || "Failed to upload image");
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4">
        <h1 className="text-2xl md:text-3xl font-bold">Upload Image</h1>
        <Button variant="outline" onClick={() => router.back()} className="w-full sm:w-auto">
          Cancel
        </Button>
      </div>

      <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
        <Card>
          <CardHeader className="p-4 md:p-6">
            <CardTitle className="text-lg md:text-xl">Image Details</CardTitle>
          </CardHeader>
          <CardContent className="p-4 md:p-6 pt-0">
            <form onSubmit={handleSubmit} className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="name">Name *</Label>
                <Input
                  id="name"
                  value={name}
                  onChange={(e) => setName(e.target.value)}
                  placeholder="Image name"
                  required
                  className="h-10"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="description">Description</Label>
                <Textarea
                  id="description"
                  value={description}
                  onChange={(e) => setDescription(e.target.value)}
                  placeholder="Optional description"
                  rows={3}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="altText">Alt Text *</Label>
                <Input
                  id="altText"
                  value={altText}
                  onChange={(e) => setAltText(e.target.value)}
                  placeholder="Descriptive alt text for accessibility"
                  required
                  className="h-10"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="type">Type *</Label>
                <Select
                  value={type}
                  onValueChange={(value) => setType(value as ImageType)}
                >
                  <SelectTrigger id="type" className="h-10">
                    <SelectValue placeholder="Select image type" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="profile">Profile</SelectItem>
                    <SelectItem value="project">Project</SelectItem>
                    <SelectItem value="background">Background</SelectItem>
                    <SelectItem value="logo">Logo</SelectItem>
                    <SelectItem value="other">Other</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label htmlFor="file">Image File *</Label>
                <div className={`flex ${isMobile ? 'flex-col' : 'items-center'} gap-2`}>
                  <Input
                    ref={fileInputRef}
                    id="file"
                    type="file"
                    accept="image/*"
                    onChange={handleFileChange}
                    className="hidden"
                    required
                  />
                  <Button
                    type="button"
                    variant="outline"
                    onClick={() => fileInputRef.current?.click()}
                    className="h-10 touch-manipulation w-full sm:w-auto"
                  >
                    <Upload className="mr-2 h-4 w-4" />
                    Select Image
                  </Button>
                  {file && (
                    <span className="text-sm text-gray-500 truncate">
                      {file.name} ({(file.size / 1024).toFixed(1)} KB)
                    </span>
                  )}
                </div>
              </div>

              <Button
                type="submit"
                className="mt-4 w-full h-10 touch-manipulation"
                disabled={isSubmitting}
              >
                {isSubmitting ? "Uploading..." : "Upload Image"}
              </Button>
            </form>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="p-4 md:p-6">
            <CardTitle className="text-lg md:text-xl">Preview</CardTitle>
          </CardHeader>
          <CardContent className="p-4 md:p-6 pt-0">
            {preview ? (
              <div className="overflow-hidden rounded-md border">
                <div className="relative aspect-square w-full">
                  <Image
                    src={preview}
                    alt="Preview"
                    fill
                    className="object-contain"
                  />
                </div>
              </div>
            ) : (
              <div className="flex aspect-square w-full items-center justify-center rounded-md border border-dashed">
                <p className="text-center text-gray-500 p-4">
                  {isMobile ? "Tap 'Select Image' to upload and preview" : "Select an image to preview"}
                </p>
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
