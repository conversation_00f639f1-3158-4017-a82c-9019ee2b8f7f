import { NextRequest, NextResponse } from 'next/server';
import { getClientProjectByReferenceCode } from '@/lib/mpesa/supabase-mpesa-fixed';
import { STKPushResponse } from '@/lib/mpesa/daraja-api';

// This is a simulated STK Push endpoint for testing purposes
export async function POST(request: NextRequest) {
  try {
    console.log('Simulated STK Push API called');
    
    // Parse request body
    const body = await request.json();
    const { phoneNumber, referenceCode } = body;
    
    console.log('Request body:', { phoneNumber, referenceCode });
    
    // Validate inputs
    if (!phoneNumber) {
      console.log('Error: Phone number is required');
      return NextResponse.json(
        {
          success: false,
          error: 'Phone number is required',
        },
        { status: 400 }
      );
    }
    
    if (!referenceCode) {
      console.log('Error: Reference code is required');
      return NextResponse.json(
        {
          success: false,
          error: 'Reference code is required',
        },
        { status: 400 }
      );
    }
    
    // Get client project by reference code
    const clientProject = await getClientProjectByReferenceCode(referenceCode);
    if (!clientProject) {
      return NextResponse.json(
        {
          success: false,
          error: `Invalid reference code: ${referenceCode}. Please check and try again.`,
        },
        { status: 400 }
      );
    }
    
    console.log('API route: Found client project:', JSON.stringify(clientProject));
    
    // Generate random IDs for the response
    const merchantRequestId = `M-${Date.now()}-${Math.floor(Math.random() * 10000)}`;
    const checkoutRequestId = `C-${Date.now()}-${Math.floor(Math.random() * 10000)}`;
    
    // Create a simulated STK Push response
    const stkPushResponse: STKPushResponse = {
      MerchantRequestID: merchantRequestId,
      CheckoutRequestID: checkoutRequestId,
      ResponseCode: '0',
      ResponseDescription: 'Success. Request accepted for processing',
      CustomerMessage: 'Success. Request accepted for processing',
    };
    
    console.log('Simulated STK Push response:', JSON.stringify(stkPushResponse));
    
    // Return the simulated response
    return NextResponse.json({
      success: true,
      data: stkPushResponse,
      simulated: true,
    });
  } catch (error) {
    console.error('Error processing simulated STK Push request:', error);
    return NextResponse.json(
      {
        success: false,
        error: `Failed to process simulated STK Push request: ${error instanceof Error ? error.message : 'Unknown error'}`,
      },
      { status: 500 }
    );
  }
}
