'use client';

import { useState } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { motion } from 'framer-motion';
import {
  PremiumPackageFormData,
  premiumPackageSchema,
  CONTACT_FORM_FIELDS,
  HOSTING_OPTIONS,
  CMS_OPTIONS,
  BLOG_FREQUENCY_OPTIONS,
  AUTH_FEATURES,
  ECOMMERCE_FEATURES,
  PAYMENT_METHODS,
  ADMIN_PANEL_FEATURES
} from '@/lib/questionnaire/types';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
  FormDescription
} from '@/components/ui/form';
import { Checkbox } from '@/components/ui/checkbox';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { toast } from 'sonner';
import { submitPackageInformation } from '@/app/actions/questionnaire-actions';
import { QuestionnaireResponse } from '@/lib/questionnaire/types';

interface PremiumPackageFormProps {
  responseId: string;
  initialData?: Partial<PremiumPackageFormData>;
  onComplete: (data: QuestionnaireResponse) => void;
  onBack: () => void;
}

export default function PremiumPackageForm({
  responseId,
  initialData,
  onComplete,
  onBack
}: PremiumPackageFormProps) {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [pages, setPages] = useState<string[]>(initialData?.requested_pages || ['Home', 'About', 'Services', 'Blog', 'Shop', 'Contact', 'Dashboard', '']);

  // Initialize form with react-hook-form and zod validation
  const form = useForm<PremiumPackageFormData>({
    resolver: zodResolver(premiumPackageSchema),
    defaultValues: {
      requested_pages: initialData?.requested_pages || ['Home', 'About', 'Services', 'Blog', 'Shop', 'Contact', 'Dashboard'],
      has_content_ready: initialData?.has_content_ready || false,
      contact_form_fields: initialData?.contact_form_fields || ['name', 'email', 'message'],
      has_domain_name: initialData?.has_domain_name || false,
      preferred_hosting: initialData?.preferred_hosting || 'vercel',
      blog_content_type: initialData?.blog_content_type || '',
      blog_update_frequency: initialData?.blog_update_frequency || 'monthly',
      cms_preference: initialData?.cms_preference || 'contentful',
      auth_features: initialData?.auth_features || [],
      design_references: initialData?.design_references || '',
      ecommerce_features: initialData?.ecommerce_features || [],
      payment_methods: initialData?.payment_methods || [],
      third_party_apis: initialData?.third_party_apis || [],
      database_requirements: initialData?.database_requirements || '',
      admin_panel_features: initialData?.admin_panel_features || [],
      needs_user_roles: initialData?.needs_user_roles || false
    }
  });

  // Handle adding a new page input
  const addPageInput = () => {
    if (pages.length < 12) {
      setPages([...pages, '']);
    } else {
      toast.info('The Premium Package includes up to 12 pages');
    }
  };

  // Handle removing a page input
  const removePageInput = (index: number) => {
    if (pages.length > 1) {
      const newPages = [...pages];
      newPages.splice(index, 1);
      setPages(newPages);

      // Update form value
      const currentPages = form.getValues().requested_pages || [];
      const newFormPages = [...currentPages];
      newFormPages.splice(index, 1);
      form.setValue('requested_pages', newFormPages.filter(Boolean));
    }
  };

  // Handle page input change
  const handlePageChange = (index: number, value: string) => {
    const newPages = [...pages];
    newPages[index] = value;
    setPages(newPages);

    // Update form value
    const currentPages = form.getValues().requested_pages || [];
    const newFormPages = [...currentPages];
    newFormPages[index] = value;
    form.setValue('requested_pages', newFormPages.filter(Boolean));
  };

  // Handle form submission
  const onSubmit = async (data: PremiumPackageFormData) => {
    setIsSubmitting(true);

    try {
      // Filter out empty page names
      const filteredPages = data.requested_pages.filter(Boolean);

      if (filteredPages.length === 0) {
        toast.error('Please specify at least one page for your website');
        setIsSubmitting(false);
        return;
      }

      // Always submit to database, but show additional message if checkbox is checked
      if (data.has_content_ready || data.has_domain_name ||
          data.ecommerce_features?.length || data.payment_methods?.length ||
          data.third_party_apis?.length || data.needs_user_roles) {
        // Show a more detailed message about contacting directly
        toast.info(
          "Your information has been saved. Please contact Zachary <NAME_EMAIL> to provide additional information about your premium project requirements.",
          { duration: 8000 }
        );
      }

      console.log('Submitting premium package information with data:', {
        responseId,
        packageType: 'premium',
        ...data,
        requested_pages: filteredPages
      });

      try {
        const result = await submitPackageInformation({
          responseId,
          packageType: 'premium',
          ...data,
          requested_pages: filteredPages
        });

        if (result.success && result.data) {
          toast.success('Package information saved successfully!');
          onComplete(result.data);
        } else {
          console.error('Server returned error:', result.error);

          // If there's a database error, still allow the user to proceed
          if (result.error && result.error.includes("fetch failed")) {
            toast.warning('There was an issue saving your information, but your questionnaire has been completed. Please contact <NAME_EMAIL> if you encounter further issues.');

            // Create a mock response to allow continuing
            onComplete({
              id: responseId,
              ...data,
              requested_pages: filteredPages,
              packageType: 'premium', // This is for UI only, not stored in DB
              last_section_completed: 2,
              completion_status: 'completed',
              created_at: new Date().toISOString(),
              updated_at: new Date().toISOString()
            } as QuestionnaireResponse);
          } else {
            toast.error(result.error || 'Failed to save information. Please try again.');
          }
        }
      } catch (submitError) {
        console.error('Error in submission:', submitError);

        // Allow user to continue despite the error
        toast.warning('There was an issue saving your information, but your questionnaire has been completed. Please contact <NAME_EMAIL> if you encounter further issues.');

        // Create a mock response to allow continuing
        onComplete({
          id: responseId,
          ...data,
          requested_pages: filteredPages,
          packageType: 'premium', // This is for UI only, not stored in DB
          last_section_completed: 2,
          completion_status: 'completed',
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        } as QuestionnaireResponse);
      }
    } catch (error) {
      console.error('Error submitting package information:', error);
      // More detailed error logging
      if (error instanceof Error) {
        console.error('Error name:', error.name);
        console.error('Error message:', error.message);
        console.error('Error stack:', error.stack);
      }
      toast.error('An unexpected error occurred. Please try again or contact Zachary <NAME_EMAIL>.');
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
      className="w-full"
    >
      <div className="mb-6">
        <h2 className="text-xl sm:text-2xl font-bold">Premium Package Details</h2>
        <p className="text-muted-foreground text-sm sm:text-base">
          Please provide details about your premium website requirements.
        </p>
      </div>

      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-8">
          {/* Website Pages Section */}
          <div className="space-y-4">
            <FormLabel>Website Pages</FormLabel>
            <FormDescription>
              The Premium Package includes up to 12 pages. Please specify the pages you need.
            </FormDescription>

            {pages.map((page, index) => (
              <div key={index} className="flex items-center gap-2">
                <Input
                  placeholder={`Page ${index + 1} (e.g., Home, About, Services)`}
                  value={page}
                  onChange={(e) => handlePageChange(index, e.target.value)}
                  className="flex-1"
                />
                <Button
                  type="button"
                  variant="outline"
                  size="icon"
                  onClick={() => removePageInput(index)}
                  disabled={pages.length <= 1}
                >
                  ✕
                </Button>
              </div>
            ))}

            {pages.length < 12 && (
              <Button
                type="button"
                variant="outline"
                size="sm"
                onClick={addPageInput}
                className="mt-2"
              >
                + Add Page
              </Button>
            )}
          </div>

          {/* Content Readiness */}
          <FormField
            control={form.control}
            name="has_content_ready"
            render={({ field }) => (
              <FormItem className="flex flex-row items-start space-x-3 space-y-0 rounded-md border p-4">
                <FormControl>
                  <Checkbox
                    checked={field.value}
                    onCheckedChange={(checked) => {
                      field.onChange(checked);
                      if (checked) {
                        toast.info(
                          "Please contact Zachary <NAME_EMAIL> to provide additional information about your content.",
                          { duration: 8000 }
                        );
                      }
                    }}
                  />
                </FormControl>
                <div className="space-y-1 leading-none">
                  <FormLabel className="font-normal cursor-pointer">
                    I have content ready for these pages
                  </FormLabel>
                  <FormDescription>
                    Check this if you already have text, images, and other content for your website.
                    You'll need to contact me directly to provide these assets.
                  </FormDescription>
                </div>
              </FormItem>
            )}
          />

          {/* Blog Content */}
          <div className="space-y-4">
            <FormField
              control={form.control}
              name="blog_content_type"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Blog Content Type</FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder="Describe the type of content you plan to publish on your blog"
                      {...field}
                      rows={3}
                    />
                  </FormControl>
                  <FormDescription>
                    This helps determine the best blog structure and features for your needs.
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="blog_update_frequency"
              render={({ field }) => (
                <FormItem className="space-y-3">
                  <FormLabel>Blog Update Frequency</FormLabel>
                  <FormControl>
                    <RadioGroup
                      onValueChange={field.onChange}
                      defaultValue={field.value}
                      className="flex flex-col space-y-1"
                    >
                      {BLOG_FREQUENCY_OPTIONS.map((option) => (
                        <FormItem
                          key={option.id}
                          className="flex items-center space-x-3 space-y-0"
                        >
                          <FormControl>
                            <RadioGroupItem value={option.id} />
                          </FormControl>
                          <FormLabel className="font-normal cursor-pointer">
                            {option.label}
                          </FormLabel>
                        </FormItem>
                      ))}
                    </RadioGroup>
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>

          {/* CMS Preference */}
          <FormField
            control={form.control}
            name="cms_preference"
            render={({ field }) => (
              <FormItem className="space-y-3">
                <FormLabel>CMS Preference</FormLabel>
                <FormControl>
                  <RadioGroup
                    onValueChange={field.onChange}
                    defaultValue={field.value}
                    className="flex flex-col space-y-1"
                  >
                    {CMS_OPTIONS.map((option) => (
                      <FormItem
                        key={option.id}
                        className="flex items-center space-x-3 space-y-0"
                      >
                        <FormControl>
                          <RadioGroupItem value={option.id} />
                        </FormControl>
                        <FormLabel className="font-normal cursor-pointer">
                          {option.label}
                        </FormLabel>
                      </FormItem>
                    ))}
                  </RadioGroup>
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          {/* E-commerce Features */}
          <FormField
            control={form.control}
            name="ecommerce_features"
            render={() => (
              <FormItem>
                <div className="mb-4">
                  <FormLabel>E-commerce Features</FormLabel>
                  <FormDescription>
                    Select the e-commerce features you need for your website.
                  </FormDescription>
                </div>
                <div className="grid grid-cols-1 sm:grid-cols-2 gap-2 sm:gap-3">
                  {ECOMMERCE_FEATURES.map((feature) => (
                    <FormField
                      key={feature.id}
                      control={form.control}
                      name="ecommerce_features"
                      render={({ field }) => {
                        return (
                          <FormItem
                            key={feature.id}
                            className="flex flex-row items-start space-x-3 space-y-0 rounded-md border p-4"
                          >
                            <FormControl>
                              <Checkbox
                                checked={field.value?.includes(feature.id)}
                                onCheckedChange={(checked) => {
                                  const currentFeatures = field.value || [];
                                  return checked
                                    ? field.onChange([...currentFeatures, feature.id])
                                    : field.onChange(
                                        currentFeatures.filter((value) => value !== feature.id)
                                      );
                                }}
                              />
                            </FormControl>
                            <FormLabel className="font-normal cursor-pointer">
                              {feature.label}
                            </FormLabel>
                          </FormItem>
                        );
                      }}
                    />
                  ))}
                </div>
                <FormMessage />
              </FormItem>
            )}
          />

          {/* Payment Methods */}
          <FormField
            control={form.control}
            name="payment_methods"
            render={() => (
              <FormItem>
                <div className="mb-4">
                  <FormLabel>Payment Methods</FormLabel>
                  <FormDescription>
                    Select the payment methods you want to integrate.
                  </FormDescription>
                </div>
                <div className="grid grid-cols-1 sm:grid-cols-2 gap-2 sm:gap-3">
                  {PAYMENT_METHODS.map((method) => (
                    <FormField
                      key={method.id}
                      control={form.control}
                      name="payment_methods"
                      render={({ field }) => {
                        return (
                          <FormItem
                            key={method.id}
                            className="flex flex-row items-start space-x-3 space-y-0 rounded-md border p-4"
                          >
                            <FormControl>
                              <Checkbox
                                checked={field.value?.includes(method.id)}
                                onCheckedChange={(checked) => {
                                  const currentMethods = field.value || [];
                                  return checked
                                    ? field.onChange([...currentMethods, method.id])
                                    : field.onChange(
                                        currentMethods.filter((value) => value !== method.id)
                                      );
                                }}
                              />
                            </FormControl>
                            <FormLabel className="font-normal cursor-pointer">
                              {method.label}
                            </FormLabel>
                          </FormItem>
                        );
                      }}
                    />
                  ))}
                </div>
                <FormMessage />
              </FormItem>
            )}
          />

          {/* Third-Party APIs */}
          <FormField
            control={form.control}
            name="third_party_apis"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Third-Party APIs</FormLabel>
                <FormControl>
                  <Textarea
                    placeholder="List any third-party APIs you want to integrate (e.g., Google Maps, Social Media, Analytics)"
                    value={field.value?.join('\n') || ''}
                    onChange={(e) => {
                      const value = e.target.value;
                      field.onChange(value.split('\n').filter(Boolean));
                    }}
                    rows={3}
                  />
                </FormControl>
                <FormDescription>
                  Enter each API on a new line.
                </FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />

          {/* Database Requirements */}
          <FormField
            control={form.control}
            name="database_requirements"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Database Requirements</FormLabel>
                <FormControl>
                  <Textarea
                    placeholder="Describe your database requirements (e.g., user data, product catalog, content management)"
                    {...field}
                    rows={3}
                  />
                </FormControl>
                <FormDescription>
                  This helps determine the best database solution for your project.
                </FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />

          {/* Admin Panel Features */}
          <FormField
            control={form.control}
            name="admin_panel_features"
            render={() => (
              <FormItem>
                <div className="mb-4">
                  <FormLabel>Admin Panel Features</FormLabel>
                  <FormDescription>
                    Select the features you need in your admin dashboard.
                  </FormDescription>
                </div>
                <div className="grid grid-cols-1 sm:grid-cols-2 gap-2 sm:gap-3">
                  {ADMIN_PANEL_FEATURES.map((feature) => (
                    <FormField
                      key={feature.id}
                      control={form.control}
                      name="admin_panel_features"
                      render={({ field }) => {
                        return (
                          <FormItem
                            key={feature.id}
                            className="flex flex-row items-start space-x-3 space-y-0 rounded-md border p-4"
                          >
                            <FormControl>
                              <Checkbox
                                checked={field.value?.includes(feature.id)}
                                onCheckedChange={(checked) => {
                                  const currentFeatures = field.value || [];
                                  return checked
                                    ? field.onChange([...currentFeatures, feature.id])
                                    : field.onChange(
                                        currentFeatures.filter((value) => value !== feature.id)
                                      );
                                }}
                              />
                            </FormControl>
                            <FormLabel className="font-normal cursor-pointer">
                              {feature.label}
                            </FormLabel>
                          </FormItem>
                        );
                      }}
                    />
                  ))}
                </div>
                <FormMessage />
              </FormItem>
            )}
          />

          {/* User Roles */}
          <FormField
            control={form.control}
            name="needs_user_roles"
            render={({ field }) => (
              <FormItem className="flex flex-row items-start space-x-3 space-y-0 rounded-md border p-4">
                <FormControl>
                  <Checkbox
                    checked={field.value}
                    onCheckedChange={(checked) => {
                      field.onChange(checked);
                      if (checked) {
                        toast.info(
                          "Please contact Zachary <NAME_EMAIL> to discuss your user role requirements in detail.",
                          { duration: 8000 }
                        );
                      }
                    }}
                  />
                </FormControl>
                <div className="space-y-1 leading-none">
                  <FormLabel className="font-normal cursor-pointer">
                    I need different user roles and permissions
                  </FormLabel>
                  <FormDescription>
                    Check this if your application requires different access levels (e.g., admin, editor, user).
                    You'll need to contact me directly to discuss the details.
                  </FormDescription>
                </div>
              </FormItem>
            )}
          />

          {/* Domain Name */}
          <FormField
            control={form.control}
            name="has_domain_name"
            render={({ field }) => (
              <FormItem className="flex flex-row items-start space-x-3 space-y-0 rounded-md border p-4">
                <FormControl>
                  <Checkbox
                    checked={field.value}
                    onCheckedChange={(checked) => {
                      field.onChange(checked);
                      if (checked) {
                        toast.info(
                          "Please contact Zachary <NAME_EMAIL> to provide information about your domain name.",
                          { duration: 8000 }
                        );
                      }
                    }}
                  />
                </FormControl>
                <div className="space-y-1 leading-none">
                  <FormLabel className="font-normal cursor-pointer">
                    I already have a domain name
                  </FormLabel>
                  <FormDescription>
                    Check this if you already own a domain name for your website.
                    You'll need to contact me directly with your domain details.
                  </FormDescription>
                </div>
              </FormItem>
            )}
          />

          {/* Hosting Preference */}
          <FormField
            control={form.control}
            name="preferred_hosting"
            render={({ field }) => (
              <FormItem className="space-y-3">
                <FormLabel>Preferred Hosting</FormLabel>
                <FormControl>
                  <RadioGroup
                    onValueChange={field.onChange}
                    defaultValue={field.value}
                    className="flex flex-col space-y-1"
                  >
                    {HOSTING_OPTIONS.map((option) => (
                      <FormItem
                        key={option.id}
                        className="flex items-center space-x-3 space-y-0"
                      >
                        <FormControl>
                          <RadioGroupItem value={option.id} />
                        </FormControl>
                        <FormLabel className="font-normal cursor-pointer">
                          {option.label}
                        </FormLabel>
                      </FormItem>
                    ))}
                  </RadioGroup>
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          {/* Design References */}
          <FormField
            control={form.control}
            name="design_references"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Design References</FormLabel>
                <FormControl>
                  <Textarea
                    placeholder="List websites you like the design of, or describe your design preferences"
                    {...field}
                    rows={3}
                  />
                </FormControl>
                <FormDescription>
                  This helps understand your aesthetic preferences.
                </FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />

          <div className="pt-6 flex justify-between">
            <Button
              type="button"
              variant="outline"
              onClick={onBack}
              disabled={isSubmitting}
            >
              Back
            </Button>

            <Button
              type="submit"
              disabled={isSubmitting}
            >
              {isSubmitting ? 'Submitting...' : 'Complete Questionnaire'}
            </Button>
          </div>
        </form>
      </Form>
    </motion.div>
  );
}
