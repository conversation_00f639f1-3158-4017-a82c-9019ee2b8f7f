"use client";

import { useState } from "react";
import Link from "next/link";
import { usePathname } from "next/navigation";
import { signOut } from "next-auth/react";
import {
  LayoutDashboard,
  FileText,
  Image as ImageIcon,
  Settings,
  LogOut,
  ChevronRight,
  Activity,
  Briefcase,
  ClipboardList
} from "lucide-react";
import { cn } from "@/lib/utils";
import { Button } from "@/components/ui/button";
import { CmsThemeToggle } from "./cms-theme-toggle";

const navItems = [
  {
    title: "Dashboard",
    href: "/admin",
    icon: LayoutDashboard,
  },
  {
    title: "Content",
    href: "/admin/content",
    icon: FileText,
  },
  {
    title: "Images",
    href: "/admin/images",
    icon: ImageIcon,
  },
  {
    title: "Project Requests",
    href: "/admin/project-requests",
    icon: Briefcase,
  },
  {
    title: "Questionnaires",
    href: "/admin/questionnaires",
    icon: ClipboardList,
  },
  {
    title: "Settings",
    href: "/admin/settings",
    icon: Settings,
  },
  {
    title: "Diagnostics",
    href: "/admin/diagnostics",
    icon: Activity,
  },
];

export function Sidebar() {
  const pathname = usePathname();
  const [collapsed, setCollapsed] = useState(false);

  return (
    <div
      className={cn(
        "flex h-screen flex-col border-r bg-gray-100 dark:bg-gray-800 transition-all duration-300",
        collapsed ? "w-16" : "w-64"
      )}
    >
      <div className="flex h-14 items-center border-b px-4">
        <div className={cn("flex items-center", collapsed && "justify-center")}>
          <span className={cn("font-bold text-lg", collapsed && "hidden")}>
            Portfolio CMS
          </span>
        </div>
        <div className="ml-auto flex items-center gap-1">
          <CmsThemeToggle
            variant={collapsed ? "compact" : "default"}
            className={cn(collapsed && "mr-1")}
          />
          <Button
            variant="ghost"
            size="icon"
            className={cn("", collapsed && "rotate-180")}
            onClick={() => setCollapsed(!collapsed)}
          >
            <ChevronRight className="h-4 w-4" />
          </Button>
        </div>
      </div>
      <nav className="flex-1 overflow-auto p-2">
        <ul className="space-y-2">
          {navItems.map((item) => (
            <li key={item.href}>
              <Link
                href={item.href}
                className={cn(
                  "flex items-center rounded-md px-3 py-2 text-sm font-medium transition-colors",
                  pathname === item.href
                    ? "bg-gray-200 text-gray-900 dark:bg-gray-700 dark:text-gray-50"
                    : "text-gray-500 hover:bg-gray-200 dark:text-gray-400 dark:hover:bg-gray-700"
                )}
              >
                <item.icon className="mr-2 h-4 w-4" />
                <span className={cn(collapsed && "hidden")}>{item.title}</span>
              </Link>
            </li>
          ))}
        </ul>
      </nav>
      <div className="border-t p-2">
        <Button
          variant="ghost"
          className={cn(
            "flex w-full items-center justify-start px-3 py-2 text-sm font-medium text-gray-500 hover:bg-gray-200 dark:text-gray-400 dark:hover:bg-gray-700",
            collapsed && "justify-center"
          )}
          onClick={() => signOut({ callbackUrl: "/admin/login" })}
        >
          <LogOut className="mr-2 h-4 w-4" />
          <span className={cn(collapsed && "hidden")}>Logout</span>
        </Button>
      </div>
    </div>
  );
}
