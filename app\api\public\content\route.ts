import { NextRequest, NextResponse } from 'next/server';
import {
  getAllContent,
  getContentBySection,
  getContentBySectionAndKey
} from '@/lib/cms/supabase-cms';
import { getDefaultContent } from '@/lib/cms/client';

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const section = searchParams.get('section');
    const key = searchParams.get('key');

    if (section && key) {
      try {
        const content = await getContentBySectionAndKey(section as any, key as any);

        if (!content) {
          // Return default content instead of 404
          return NextResponse.json({
            id: `default-${section}-${key}`,
            section: section,
            key: key,
            content: getDefaultContent(section as any, key as any),
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString()
          });
        }

        return NextResponse.json(content);
      } catch (err) {
        console.error(`Error fetching content for ${section}.${key}:`, err);
        // Return default content on error
        return NextResponse.json({
          id: `default-${section}-${key}`,
          section: section,
          key: key,
          content: getDefaultContent(section as any, key as any),
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        });
      }
    } else if (section) {
      try {
        const content = await getContentBySection(section as any);
        return NextResponse.json(content);
      } catch (err) {
        console.error(`Error fetching content for section ${section}:`, err);
        // Return empty array on error
        return NextResponse.json([]);
      }
    } else {
      try {
        const content = await getAllContent();
        return NextResponse.json(content);
      } catch (err) {
        console.error('Error fetching all content:', err);
        // Return empty array on error
        return NextResponse.json([]);
      }
    }
  } catch (error: any) {
    console.error('Error in public content GET route:', error);
    return NextResponse.json(
      { error: error.message || 'Failed to fetch content' },
      { status: 500 }
    );
  }
}
