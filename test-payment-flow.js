/**
 * Test Payment Flow Script
 *
 * This script helps test the payment system workflow by simulating a client
 * going through the payment process. It provides test data for each step
 * and instructions on how to test the flow.
 *
 * How to use:
 * 1. Open your browser console on the /start-project page
 * 2. Copy and paste the relevant section of code for the step you're testing
 * 3. Follow the instructions in the console
 */

// =============================================
// STEP 1: Package Selection Test Data
// =============================================

const testPackageData = {
  // Basic Package
  basic: {
    package_type: 'basic',
    additional_services: ['api-dev', 'custom-components'],
    maintenance_plan: 'basic'
  },

  // Standard Package
  standard: {
    package_type: 'standard',
    additional_services: ['headless-cms', 'performance'],
    maintenance_plan: 'standard'
  },

  // Premium Package
  premium: {
    package_type: 'premium',
    additional_services: ['pwa', 'third-party-api'],
    maintenance_plan: 'standard'
  }
};

// Function to fill package selection form
function fillPackageSelectionForm(packageType = 'basic') {
  console.log(`Filling package selection form with ${packageType} package data...`);

  // Select package type
  const packageData = testPackageData[packageType];

  // Select the package radio button
  const packageRadio = document.querySelector(`input[value="${packageData.package_type}"]`);
  if (packageRadio) {
    packageRadio.click();
    console.log(`Selected ${packageData.package_type} package`);
  } else {
    console.error(`Package radio button for ${packageData.package_type} not found`);
  }

  // Wait a bit for UI to update
  setTimeout(() => {
    // Select additional services
    packageData.additional_services.forEach(service => {
      // Try different selector patterns
      const serviceCheckbox =
        document.querySelector(`#service-${service}`) || // First try the ID format used in your component
        document.querySelector(`input[id="service-${service}"]`) || // Try with input and ID
        document.querySelector(`input[value="${service}"]`) || // Try with value attribute
        document.querySelector(`[data-value="${service}"]`); // Try with data attribute

      if (serviceCheckbox) {
        serviceCheckbox.click();
        console.log(`Selected additional service: ${service}`);
      } else {
        console.log(`Service checkbox for ${service} not found. Trying alternative method...`);

        // Alternative method: find by label text
        const serviceLabels = Array.from(document.querySelectorAll('label'));
        const serviceLabel = serviceLabels.find(label =>
          label.textContent && label.textContent.includes(service)
        );

        if (serviceLabel) {
          const forAttr = serviceLabel.getAttribute('for');
          if (forAttr) {
            const checkbox = document.querySelector(`#${forAttr}`);
            if (checkbox) {
              checkbox.click();
              console.log(`Selected additional service: ${service} (by label)`);
            } else {
              console.error(`Checkbox with ID ${forAttr} not found`);
            }
          } else {
            // If no 'for' attribute, try clicking the label directly
            serviceLabel.click();
            console.log(`Clicked label for service: ${service}`);
          }
        } else {
          console.error(`Service checkbox or label for ${service} not found`);
        }
      }
    });

    // Select maintenance plan
    if (packageData.maintenance_plan) {
      // Try different selector patterns for maintenance plan
      const planRadio =
        document.querySelector(`#plan-${packageData.maintenance_plan}`) || // Try ID format
        document.querySelector(`input[value="${packageData.maintenance_plan}"]`) || // Try value attribute
        document.querySelector(`[data-value="${packageData.maintenance_plan}"]`); // Try data attribute

      if (planRadio) {
        planRadio.click();
        console.log(`Selected maintenance plan: ${packageData.maintenance_plan}`);
      } else {
        console.log(`Maintenance plan radio for ${packageData.maintenance_plan} not found. Trying alternative method...`);

        // Alternative method: find by label text
        const planLabels = Array.from(document.querySelectorAll('label'));
        const planLabel = planLabels.find(label =>
          label.textContent && label.textContent.toLowerCase().includes(packageData.maintenance_plan.toLowerCase())
        );

        if (planLabel) {
          const forAttr = planLabel.getAttribute('for');
          if (forAttr) {
            const radio = document.querySelector(`#${forAttr}`);
            if (radio) {
              radio.click();
              console.log(`Selected maintenance plan: ${packageData.maintenance_plan} (by label)`);
            } else {
              console.error(`Radio with ID ${forAttr} not found`);
            }
          } else {
            // If no 'for' attribute, try clicking the label directly
            planLabel.click();
            console.log(`Clicked label for maintenance plan: ${packageData.maintenance_plan}`);
          }
        } else {
          console.error(`Maintenance plan radio or label for ${packageData.maintenance_plan} not found`);
        }
      }
    }

    console.log('Package selection form filled. Click "Continue" to proceed.');
  }, 500);
}

// =============================================
// STEP 2: Client Information Test Data
// =============================================

const testClientData = {
  // Test Client 1
  client1: {
    client_name: 'John Doe',
    client_email: '<EMAIL>',
    client_phone: '0796564593'
  },

  // Test Client 2
  client2: {
    client_name: 'Jane Smith',
    client_email: '<EMAIL>',
    client_phone: '0723456789'
  },

  // Test Client 3
  client3: {
    client_name: 'Test User',
    client_email: '<EMAIL>',
    client_phone: '0734567890'
  }
};

// Function to fill client information form
function fillClientInformationForm(clientKey = 'client1') {
  console.log(`Filling client information form with ${clientKey} data...`);

  const clientData = testClientData[clientKey];

  // Fill name field
  const nameInput = document.querySelector('input[name="client_name"]');
  if (nameInput) {
    nameInput.value = clientData.client_name;
    nameInput.dispatchEvent(new Event('input', { bubbles: true }));
    console.log(`Filled name: ${clientData.client_name}`);
  } else {
    console.error('Name input not found');
  }

  // Fill email field
  const emailInput = document.querySelector('input[name="client_email"]');
  if (emailInput) {
    emailInput.value = clientData.client_email;
    emailInput.dispatchEvent(new Event('input', { bubbles: true }));
    console.log(`Filled email: ${clientData.client_email}`);
  } else {
    console.error('Email input not found');
  }

  // Fill phone field
  const phoneInput = document.querySelector('input[name="client_phone"]');
  if (phoneInput) {
    phoneInput.value = clientData.client_phone;
    phoneInput.dispatchEvent(new Event('input', { bubbles: true }));
    console.log(`Filled phone: ${clientData.client_phone}`);
  } else {
    console.error('Phone input not found');
  }

  console.log('Client information form filled. Click "Continue" to proceed.');
}

// =============================================
// STEP 3: Payment Verification Test Data
// =============================================

const testMpesaCodes = [
  'ABC123DEFG',
  'XYZ456MNOP',
  'QWE789RTYU',
  'ZXC012VBNM'
];

// Function to fill payment verification form
function fillPaymentVerificationForm(codeIndex = 0) {
  console.log('Filling payment verification form...');

  const mpesaCode = testMpesaCodes[codeIndex] || testMpesaCodes[0];

  // Fill M-Pesa code field
  const codeInput = document.querySelector('input[name="mpesa_code"]');
  if (codeInput) {
    codeInput.value = mpesaCode;
    codeInput.dispatchEvent(new Event('input', { bubbles: true }));
    console.log(`Filled M-Pesa code: ${mpesaCode}`);
  } else {
    console.error('M-Pesa code input not found');
  }

  console.log('Payment verification form filled. Click "Verify Payment" to proceed.');
}

// =============================================
// STEP 4: Full Flow Test
// =============================================

// Function to test the full payment flow
function testFullPaymentFlow(packageType = 'basic', clientKey = 'client1', codeIndex = 0) {
  console.log('=== STARTING FULL PAYMENT FLOW TEST ===');
  console.log(`Package: ${packageType}, Client: ${clientKey}, M-Pesa Code Index: ${codeIndex}`);

  // Step 1: Fill package selection form
  fillPackageSelectionForm(packageType);

  console.log('\nAfter clicking "Continue" on the package selection form, run:');
  console.log('fillClientInformationForm()');

  // Instructions for next steps
  console.log('\n=== NEXT STEPS ===');
  console.log('1. After filling client information and clicking "Continue", you\'ll see the payment instructions');
  console.log('2. Click "I\'ve Made the Payment" to proceed to verification');
  console.log('3. Then run: fillPaymentVerificationForm()');
  console.log('4. Click "Verify Payment" to complete the flow');
  console.log('5. You should be redirected to the confirmation page with a link to the questionnaire');
}

// Helper function to inspect form elements
function inspectFormElements() {
  console.log('=== INSPECTING FORM ELEMENTS ===');

  // Inspect package radio buttons
  console.log('Package radio buttons:');
  document.querySelectorAll('input[type="radio"]').forEach(radio => {
    console.log(`Radio: id=${radio.id}, name=${radio.name}, value=${radio.value}`);
  });

  // Inspect checkboxes
  console.log('\nCheckboxes:');
  document.querySelectorAll('input[type="checkbox"]').forEach(checkbox => {
    console.log(`Checkbox: id=${checkbox.id}, name=${checkbox.name}, value=${checkbox.value}`);
  });

  // Inspect labels
  console.log('\nLabels:');
  document.querySelectorAll('label').forEach(label => {
    console.log(`Label: for=${label.getAttribute('for')}, text=${label.textContent?.trim().substring(0, 30)}...`);
  });

  console.log('\nUse this information to update the test script if needed.');
}

// =============================================
// USAGE INSTRUCTIONS
// =============================================

console.log('=== PAYMENT FLOW TEST SCRIPT LOADED ===');
console.log('To test the full payment flow, run:');
console.log('testFullPaymentFlow()');
console.log('\nOr test individual steps:');
console.log('1. fillPackageSelectionForm("basic" | "standard" | "premium")');
console.log('2. fillClientInformationForm("client1" | "client2" | "client3")');
console.log('3. fillPaymentVerificationForm(0-3)');
console.log('\nIf you encounter issues with form elements not being found, run:');
console.log('inspectFormElements()');
console.log('This will help you identify the actual IDs and values of form elements.');
