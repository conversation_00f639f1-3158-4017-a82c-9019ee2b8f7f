import { NextRequest, NextResponse } from 'next/server';
import {
  getAllImages,
  getImagesByType,
  createImage
} from '@/lib/cms/supabase-cms';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const type = searchParams.get('type');

    if (type) {
      const images = await getImagesByType(type as any);
      return NextResponse.json(images);
    } else {
      const images = await getAllImages();
      return NextResponse.json(images);
    }
  } catch (error: any) {
    console.error('Error in images GET route:', error);
    return NextResponse.json(
      { error: error.message || 'Failed to fetch images' },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);

    // Check if user is authenticated for write operations
    if (!session) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const formData = await request.formData();
    const name = formData.get('name') as string;
    const description = formData.get('description') as string;
    const alt_text = formData.get('alt_text') as string;
    const type = formData.get('type') as string;
    const file = formData.get('file') as File;

    // Validate payload
    if (!name || !alt_text || !type || !file) {
      return NextResponse.json(
        { error: 'Missing required fields: name, alt_text, type, file' },
        { status: 400 }
      );
    }

    console.log('Received file:', file.name, file.type, file.size);

    try {
      const image = await createImage({
        name,
        description: description || undefined,
        alt_text,
        type: type as any,
        file
      });

      return NextResponse.json(image, { status: 201 });
    } catch (uploadError: any) {
      console.error('Error uploading image to Supabase:', uploadError);
      return NextResponse.json(
        { error: `Upload failed: ${uploadError.message}` },
        { status: 500 }
      );
    }
  } catch (error: any) {
    console.error('Error in images POST route:', error);
    return NextResponse.json(
      { error: error.message || 'Failed to create image' },
      { status: 500 }
    );
  }
}
