"use client";

import { useState, useRef, useEffect } from "react";
import { useRouter } from "next/navigation";
import Image from "next/image";
import { toast } from "sonner";
import { useSession } from "next-auth/react";
import { Upload } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { PortfolioImage, ImageType } from "@/lib/cms/types";
import { use } from "react";

export default function EditImagePage({
  params,
}: {
  params: { id: string };
}) {
  const router = useRouter();
  const { data: session, status } = useSession();
  const resolvedParams = use(params);
  const id = resolvedParams.id;
  const fileInputRef = useRef<HTMLInputElement>(null);

  const [image, setImage] = useState<PortfolioImage | null>(null);
  const [name, setName] = useState("");
  const [description, setDescription] = useState("");
  const [altText, setAltText] = useState("");
  const [type, setType] = useState<ImageType | "">("");
  const [file, setFile] = useState<File | null>(null);
  const [preview, setPreview] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isSubmitting, setIsSubmitting] = useState(false);

  useEffect(() => {
    // Don't fetch if not authenticated
    if (status === "unauthenticated") {
      router.push("/admin/login");
      return;
    }

    // Wait for authentication to complete
    if (status === "loading") {
      return;
    }

    const fetchImage = async () => {
      try {
        console.log(`Fetching image with ID: ${id}`);

        // Make sure we have a valid session before making the request
        if (!session) {
          console.error("No session available");
          toast.error("Authentication required");
          router.push("/admin/login");
          return;
        }

        const res = await fetch(`/api/cms/images/${id}`, {
          // Add cache: 'no-store' to prevent caching issues
          cache: 'no-store',
          headers: {
            'Content-Type': 'application/json'
          }
        });

        if (!res.ok) {
          if (res.status === 401) {
            console.error("Unauthorized: Status 401");
            toast.error("You must be logged in to view this image");
            router.push("/admin/login");
            return;
          }
          if (res.status === 404) {
            console.error("Not found: Status 404");
            toast.error("Image not found");
            router.push("/admin/images");
            return;
          }

          // Try to get more detailed error information
          const errorData = await res.json().catch(() => null);
          const errorMessage = errorData?.error || `Server error: ${res.status}`;
          console.error(`API error: ${errorMessage}`);
          throw new Error(errorMessage);
        }

        const data = await res.json();
        console.log("Image data received:", data);

        if (!data || !data.id) {
          throw new Error("Invalid image data received");
        }

        setImage(data);
        setName(data.name);
        setDescription(data.description || "");
        setAltText(data.alt_text);
        setType(data.type);
        setPreview(data.public_url);
      } catch (error: any) {
        console.error("Error fetching image:", error);
        toast.error(error.message || "Failed to load image");
      } finally {
        setIsLoading(false);
      }
    };

    fetchImage();
  }, [id, router, status, session]);

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      const selectedFile = e.target.files[0];

      // Check file type
      if (!selectedFile.type.startsWith("image/")) {
        toast.error("Please select an image file");
        return;
      }

      // Check file size (max 5MB)
      if (selectedFile.size > 5 * 1024 * 1024) {
        toast.error("File size should be less than 5MB");
        return;
      }

      setFile(selectedFile);

      // Create preview
      const reader = new FileReader();
      reader.onload = (event) => {
        setPreview(event.target?.result as string);
      };
      reader.readAsDataURL(selectedFile);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!name || !altText || !type) {
      toast.error("Please fill in all required fields");
      return;
    }

    setIsSubmitting(true);

    try {
      const formData = new FormData();
      formData.append("name", name);
      formData.append("alt_text", altText);
      formData.append("type", type);

      if (description !== undefined) {
        formData.append("description", description);
      }

      if (file) {
        // Ensure the file is properly appended with filename
        formData.append("file", file, file.name);
        console.log("Including file in update:", file.name, file.type, file.size);
      }

      // Add timeout to prevent quick timeouts
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 30000);

      try {
        const res = await fetch(`/api/cms/images/${id}`, {
          method: "PUT",
          body: formData,
          signal: controller.signal
        });

        clearTimeout(timeoutId);

        if (!res.ok) {
          const errorData = await res.json().catch(() => ({ error: `Server error: ${res.status}` }));
          throw new Error(errorData.error || "Failed to update image");
        }

        toast.success("Image updated successfully");
        router.push("/admin/images");
      } catch (fetchError: any) {
        if (fetchError.name === 'AbortError') {
          throw new Error("Update timed out. Please try again with a smaller image or check your connection.");
        }
        throw fetchError;
      }
    } catch (error: any) {
      console.error("Error updating image:", error);
      toast.error(error.message || "Failed to update image");
    } finally {
      setIsSubmitting(false);
    }
  };

  if (isLoading) {
    return (
      <div className="flex justify-center py-8">
        <div className="h-8 w-8 animate-spin rounded-full border-b-2 border-t-2 border-blue-500"></div>
      </div>
    );
  }

  if (!image) {
    return (
      <div className="flex flex-col items-center justify-center py-8 text-center">
        <p className="mb-4 text-gray-500">Image not found</p>
        <Button variant="outline" onClick={() => router.push("/admin/images")}>
          Back to Images
        </Button>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-3xl font-bold">Edit Image</h1>
        <Button variant="outline" onClick={() => router.back()}>
          Cancel
        </Button>
      </div>

      <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
        <Card>
          <CardHeader>
            <CardTitle>Image Details</CardTitle>
          </CardHeader>
          <CardContent>
            <form onSubmit={handleSubmit} className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="name">Name *</Label>
                <Input
                  id="name"
                  value={name}
                  onChange={(e) => setName(e.target.value)}
                  placeholder="Image name"
                  required
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="description">Description</Label>
                <Textarea
                  id="description"
                  value={description}
                  onChange={(e) => setDescription(e.target.value)}
                  placeholder="Optional description"
                  rows={3}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="altText">Alt Text *</Label>
                <Input
                  id="altText"
                  value={altText}
                  onChange={(e) => setAltText(e.target.value)}
                  placeholder="Descriptive alt text for accessibility"
                  required
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="type">Type *</Label>
                <Select
                  value={type}
                  onValueChange={(value) => setType(value as ImageType)}
                >
                  <SelectTrigger id="type">
                    <SelectValue placeholder="Select image type" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="profile">Profile</SelectItem>
                    <SelectItem value="project">Project</SelectItem>
                    <SelectItem value="background">Background</SelectItem>
                    <SelectItem value="logo">Logo</SelectItem>
                    <SelectItem value="other">Other</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label htmlFor="file">Replace Image (Optional)</Label>
                <div className="flex items-center gap-2">
                  <Input
                    ref={fileInputRef}
                    id="file"
                    type="file"
                    accept="image/*"
                    onChange={handleFileChange}
                    className="hidden"
                  />
                  <Button
                    type="button"
                    variant="outline"
                    onClick={() => fileInputRef.current?.click()}
                  >
                    <Upload className="mr-2 h-4 w-4" />
                    Select New Image
                  </Button>
                  {file && (
                    <span className="text-sm text-gray-500">
                      {file.name} ({(file.size / 1024).toFixed(1)} KB)
                    </span>
                  )}
                </div>
              </div>

              <div className="flex space-x-2">
                <Button
                  type="submit"
                  className="mt-4"
                  disabled={isSubmitting}
                >
                  {isSubmitting ? "Saving..." : "Save Changes"}
                </Button>
                <Button
                  type="button"
                  variant="outline"
                  className="mt-4"
                  onClick={() => router.push("/admin/images")}
                >
                  Cancel
                </Button>
              </div>
            </form>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Preview</CardTitle>
          </CardHeader>
          <CardContent>
            {preview ? (
              <div className="overflow-hidden rounded-md border">
                <div className="relative aspect-square w-full">
                  <Image
                    src={preview}
                    alt="Preview"
                    fill
                    className="object-contain"
                  />
                </div>
              </div>
            ) : (
              <div className="flex aspect-square w-full items-center justify-center rounded-md border border-dashed">
                <p className="text-center text-gray-500">No image preview</p>
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
