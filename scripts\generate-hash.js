// A simple script to generate a hash for the password
// Since we're having issues with bcrypt installation, we'll use a simpler approach
// This is not as secure as bcrypt but will work for our demo purposes

const crypto = require('crypto');

// The password to hash
const password = 'wasekaoloch04';

// Generate a salt
const salt = crypto.randomBytes(16).toString('hex');

// Hash the password with the salt using SHA-256
const hash = crypto.pbkdf2Sync(password, salt, 1000, 64, 'sha256').toString('hex');

// The final hash is salt:hash
const finalHash = `${salt}:${hash}`;

console.log('\n--- COPY THIS HASH TO YOUR .ENV.LOCAL FILE ---\n');
console.log(finalHash);
console.log('\n--- END OF HASH ---\n');

console.log('Add this to your .env.local file as ADMIN_PASSWORD_HASH');
