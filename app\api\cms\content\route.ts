import { NextRequest, NextResponse } from 'next/server';
import { 
  getAllContent, 
  getContentBySection, 
  createContent 
} from '@/lib/cms/supabase-cms';
import { CreateContentPayload } from '@/lib/cms/types';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    
    // Check if user is authenticated for write operations
    if (!session) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }
    
    const { searchParams } = new URL(request.url);
    const section = searchParams.get('section');
    
    if (section) {
      const content = await getContentBySection(section as any);
      return NextResponse.json(content);
    } else {
      const content = await getAllContent();
      return NextResponse.json(content);
    }
  } catch (error: any) {
    console.error('Error in content GET route:', error);
    return NextResponse.json(
      { error: error.message || 'Failed to fetch content' },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    
    // Check if user is authenticated for write operations
    if (!session) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }
    
    const payload: CreateContentPayload = await request.json();
    
    // Validate payload
    if (!payload.section || !payload.key || !payload.content) {
      return NextResponse.json(
        { error: 'Missing required fields: section, key, content' },
        { status: 400 }
      );
    }
    
    const content = await createContent(payload);
    return NextResponse.json(content, { status: 201 });
  } catch (error: any) {
    console.error('Error in content POST route:', error);
    return NextResponse.json(
      { error: error.message || 'Failed to create content' },
      { status: 500 }
    );
  }
}
