#!/usr/bin/env node

/**
 * Performance testing script
 * Tests various aspects of the application performance
 */

const fs = require('fs');
const path = require('path');
const { performance } = require('perf_hooks');

class PerformanceTester {
  constructor() {
    this.results = [];
  }

  async runTest(name, testFn) {
    console.log(`🧪 Running test: ${name}`);
    const startTime = performance.now();
    
    try {
      const result = await testFn();
      const endTime = performance.now();
      const duration = endTime - startTime;
      
      this.results.push({
        name,
        duration,
        status: 'passed',
        result
      });
      
      console.log(`✅ ${name}: ${duration.toFixed(2)}ms`);
      return result;
    } catch (error) {
      const endTime = performance.now();
      const duration = endTime - startTime;
      
      this.results.push({
        name,
        duration,
        status: 'failed',
        error: error.message
      });
      
      console.log(`❌ ${name}: Failed after ${duration.toFixed(2)}ms - ${error.message}`);
      throw error;
    }
  }

  async testBuildSize() {
    return this.runTest('Build Size Analysis', async () => {
      const buildDir = path.join(process.cwd(), '.next');
      
      if (!fs.existsSync(buildDir)) {
        throw new Error('Build directory not found. Run "npm run build" first.');
      }

      const getDirectorySize = (dirPath) => {
        let totalSize = 0;
        const items = fs.readdirSync(dirPath);
        
        for (const item of items) {
          const itemPath = path.join(dirPath, item);
          const stats = fs.statSync(itemPath);
          
          if (stats.isDirectory()) {
            totalSize += getDirectorySize(itemPath);
          } else {
            totalSize += stats.size;
          }
        }
        
        return totalSize;
      };

      const totalSize = getDirectorySize(buildDir);
      const sizeMB = (totalSize / (1024 * 1024)).toFixed(2);
      
      // Performance thresholds
      const warnings = [];
      if (totalSize > 50 * 1024 * 1024) { // > 50MB
        warnings.push('Build size is very large (>50MB)');
      } else if (totalSize > 20 * 1024 * 1024) { // > 20MB
        warnings.push('Build size is large (>20MB)');
      }

      return {
        totalSize,
        sizeMB,
        warnings
      };
    });
  }

  async testStaticAssets() {
    return this.runTest('Static Assets Analysis', async () => {
      const publicDir = path.join(process.cwd(), 'public');
      
      if (!fs.existsSync(publicDir)) {
        return { message: 'No public directory found' };
      }

      const analyzeAssets = (dirPath, basePath = '') => {
        const assets = [];
        const items = fs.readdirSync(dirPath);
        
        for (const item of items) {
          const itemPath = path.join(dirPath, item);
          const relativePath = path.join(basePath, item);
          const stats = fs.statSync(itemPath);
          
          if (stats.isDirectory()) {
            assets.push(...analyzeAssets(itemPath, relativePath));
          } else {
            const ext = path.extname(item).toLowerCase();
            assets.push({
              path: relativePath,
              size: stats.size,
              type: ext
            });
          }
        }
        
        return assets;
      };

      const assets = analyzeAssets(publicDir);
      const totalSize = assets.reduce((sum, asset) => sum + asset.size, 0);
      
      // Find large assets
      const largeAssets = assets.filter(asset => asset.size > 1024 * 1024); // > 1MB
      const imageAssets = assets.filter(asset => 
        ['.jpg', '.jpeg', '.png', '.gif', '.svg', '.webp'].includes(asset.type)
      );
      
      const warnings = [];
      if (largeAssets.length > 0) {
        warnings.push(`${largeAssets.length} assets are larger than 1MB`);
      }
      
      const unoptimizedImages = imageAssets.filter(asset => 
        asset.size > 500 * 1024 && !['.webp', '.svg'].includes(asset.type)
      );
      
      if (unoptimizedImages.length > 0) {
        warnings.push(`${unoptimizedImages.length} images could be optimized`);
      }

      return {
        totalAssets: assets.length,
        totalSize,
        largeAssets: largeAssets.length,
        imageAssets: imageAssets.length,
        warnings
      };
    });
  }

  async testDependencies() {
    return this.runTest('Dependencies Analysis', async () => {
      const packageJsonPath = path.join(process.cwd(), 'package.json');
      
      if (!fs.existsSync(packageJsonPath)) {
        throw new Error('package.json not found');
      }

      const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'));
      const dependencies = packageJson.dependencies || {};
      const devDependencies = packageJson.devDependencies || {};
      
      const totalDeps = Object.keys(dependencies).length + Object.keys(devDependencies).length;
      
      // Check for potentially heavy dependencies
      const heavyDeps = [];
      const knownHeavyPackages = [
        'moment', 'lodash', 'rxjs', 'three', 'chart.js', 'monaco-editor'
      ];
      
      Object.keys(dependencies).forEach(dep => {
        if (knownHeavyPackages.some(heavy => dep.includes(heavy))) {
          heavyDeps.push(dep);
        }
      });

      const warnings = [];
      if (totalDeps > 100) {
        warnings.push('Large number of dependencies (>100)');
      }
      if (heavyDeps.length > 0) {
        warnings.push(`Potentially heavy dependencies: ${heavyDeps.join(', ')}`);
      }

      return {
        totalDependencies: totalDeps,
        productionDeps: Object.keys(dependencies).length,
        devDeps: Object.keys(devDependencies).length,
        heavyDeps,
        warnings
      };
    });
  }

  async testCodeComplexity() {
    return this.runTest('Code Complexity Analysis', async () => {
      const srcDirs = ['app', 'components', 'lib'].filter(dir => 
        fs.existsSync(path.join(process.cwd(), dir))
      );

      let totalFiles = 0;
      let totalLines = 0;
      let largeFiles = [];

      const analyzeDirectory = (dirPath) => {
        const items = fs.readdirSync(dirPath);
        
        for (const item of items) {
          const itemPath = path.join(dirPath, item);
          const stats = fs.statSync(itemPath);
          
          if (stats.isDirectory()) {
            analyzeDirectory(itemPath);
          } else if (['.ts', '.tsx', '.js', '.jsx'].includes(path.extname(item))) {
            totalFiles++;
            const content = fs.readFileSync(itemPath, 'utf8');
            const lines = content.split('\n').length;
            totalLines += lines;
            
            if (lines > 300) {
              largeFiles.push({
                path: path.relative(process.cwd(), itemPath),
                lines
              });
            }
          }
        }
      };

      srcDirs.forEach(dir => {
        analyzeDirectory(path.join(process.cwd(), dir));
      });

      const avgLinesPerFile = totalFiles > 0 ? Math.round(totalLines / totalFiles) : 0;
      
      const warnings = [];
      if (avgLinesPerFile > 200) {
        warnings.push('High average lines per file (>200)');
      }
      if (largeFiles.length > 0) {
        warnings.push(`${largeFiles.length} files have >300 lines`);
      }

      return {
        totalFiles,
        totalLines,
        avgLinesPerFile,
        largeFiles: largeFiles.length,
        warnings
      };
    });
  }

  generateReport() {
    console.log('\n📊 Performance Test Report');
    console.log('=' .repeat(50));
    
    const passedTests = this.results.filter(r => r.status === 'passed');
    const failedTests = this.results.filter(r => r.status === 'failed');
    
    console.log(`Total Tests: ${this.results.length}`);
    console.log(`Passed: ${passedTests.length}`);
    console.log(`Failed: ${failedTests.length}`);
    console.log(`Total Duration: ${this.results.reduce((sum, r) => sum + r.duration, 0).toFixed(2)}ms`);
    
    console.log('\n📈 Test Results:');
    console.log('-'.repeat(50));
    
    this.results.forEach(result => {
      const status = result.status === 'passed' ? '✅' : '❌';
      console.log(`${status} ${result.name}: ${result.duration.toFixed(2)}ms`);
      
      if (result.result && result.result.warnings && result.result.warnings.length > 0) {
        result.result.warnings.forEach(warning => {
          console.log(`   ⚠️  ${warning}`);
        });
      }
    });

    console.log('\n💡 Performance Recommendations:');
    console.log('-'.repeat(50));
    
    const allWarnings = this.results
      .filter(r => r.result && r.result.warnings)
      .flatMap(r => r.result.warnings);
    
    if (allWarnings.length === 0) {
      console.log('✅ No performance issues detected!');
    } else {
      allWarnings.forEach(warning => {
        console.log(`• ${warning}`);
      });
    }
  }

  async runAllTests() {
    console.log('🚀 Starting Performance Tests...\n');
    
    try {
      await this.testBuildSize();
      await this.testStaticAssets();
      await this.testDependencies();
      await this.testCodeComplexity();
    } catch (error) {
      // Individual test failures are already logged
    }
    
    this.generateReport();
  }
}

// Run tests if called directly
if (require.main === module) {
  const tester = new PerformanceTester();
  tester.runAllTests().catch(console.error);
}

module.exports = PerformanceTester;
