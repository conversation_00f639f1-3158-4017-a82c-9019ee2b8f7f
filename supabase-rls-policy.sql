-- Enable Row Level Security on the portfolio_images table
ALTER TABLE portfolio_images ENABLE ROW LEVEL SECURITY;

-- Create a policy that allows anyone to read from the portfolio_images table
CREATE POLICY "Allow public read access to portfolio_images" 
ON portfolio_images
FOR SELECT 
USING (true);

-- Note: This policy allows anyone to read the images, but only the admin client can create, update, or delete images
-- The admin operations are handled by the service role key we set up earlier
