'use client'

import { useEffect } from 'react'

interface PerformanceMetric {
  name: string
  value: number
  rating: 'good' | 'needs-improvement' | 'poor'
}

// Function to send metrics to analytics service
function sendToAnalytics(metric: PerformanceMetric) {
  // In production, you might want to send to Google Analytics, Vercel Analytics, etc.
  if (process.env.NODE_ENV === 'development') {
    console.log('Performance Metric:', {
      name: metric.name,
      value: metric.value,
      rating: metric.rating
    })
  }

  // Example: Send to Google Analytics (uncomment if using GA)
  // if (typeof gtag !== 'undefined') {
  //   gtag('event', metric.name, {
  //     event_category: 'Web Vitals',
  //     value: Math.round(metric.value),
  //     non_interaction: true,
  //   })
  // }

  // Example: Send to Vercel Analytics (uncomment if using Vercel Analytics)
  // if (typeof window !== 'undefined' && window.va) {
  //   window.va('track', 'Performance', {
  //     metric: metric.name,
  //     value: metric.value,
  //     rating: metric.rating
  //   })
  // }
}

// Helper function to determine rating based on thresholds
function getRating(name: string, value: number): 'good' | 'needs-improvement' | 'poor' {
  const thresholds = {
    FCP: { good: 1800, poor: 3000 },
    LCP: { good: 2500, poor: 4000 },
    FID: { good: 100, poor: 300 },
    CLS: { good: 0.1, poor: 0.25 },
    TTFB: { good: 800, poor: 1800 }
  }

  const threshold = thresholds[name as keyof typeof thresholds]
  if (!threshold) return 'good'

  if (value <= threshold.good) return 'good'
  if (value <= threshold.poor) return 'needs-improvement'
  return 'poor'
}

export default function WebVitals() {
  useEffect(() => {
    // Only run in browser environment
    if (typeof window === 'undefined') return

    // Track performance metrics using Performance API
    const trackPerformance = () => {
      if (!window.performance) return

      // First Contentful Paint (FCP)
      const paintEntries = performance.getEntriesByType('paint')
      const fcpEntry = paintEntries.find(entry => entry.name === 'first-contentful-paint')
      if (fcpEntry) {
        sendToAnalytics({
          name: 'FCP',
          value: fcpEntry.startTime,
          rating: getRating('FCP', fcpEntry.startTime)
        })
      }

      // Largest Contentful Paint (LCP) - using PerformanceObserver
      if ('PerformanceObserver' in window) {
        try {
          const lcpObserver = new PerformanceObserver((list) => {
            const entries = list.getEntries()
            const lastEntry = entries[entries.length - 1]
            if (lastEntry) {
              sendToAnalytics({
                name: 'LCP',
                value: lastEntry.startTime,
                rating: getRating('LCP', lastEntry.startTime)
              })
            }
          })
          lcpObserver.observe({ entryTypes: ['largest-contentful-paint'] })

          // Cumulative Layout Shift (CLS)
          let clsValue = 0
          const clsObserver = new PerformanceObserver((list) => {
            for (const entry of list.getEntries()) {
              if (!(entry as any).hadRecentInput) {
                clsValue += (entry as any).value
              }
            }
            sendToAnalytics({
              name: 'CLS',
              value: clsValue,
              rating: getRating('CLS', clsValue)
            })
          })
          clsObserver.observe({ entryTypes: ['layout-shift'] })

          // First Input Delay (FID)
          const fidObserver = new PerformanceObserver((list) => {
            for (const entry of list.getEntries()) {
              sendToAnalytics({
                name: 'FID',
                value: (entry as any).processingStart - entry.startTime,
                rating: getRating('FID', (entry as any).processingStart - entry.startTime)
              })
            }
          })
          fidObserver.observe({ entryTypes: ['first-input'] })
        } catch (error) {
          console.warn('Performance monitoring not fully supported:', error)
        }
      }

      // Time to First Byte (TTFB)
      const navigationEntry = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming
      if (navigationEntry) {
        const ttfb = navigationEntry.responseStart - navigationEntry.requestStart
        sendToAnalytics({
          name: 'TTFB',
          value: ttfb,
          rating: getRating('TTFB', ttfb)
        })
      }
    }

    // Track immediately and on page load
    if (document.readyState === 'complete') {
      trackPerformance()
    } else {
      window.addEventListener('load', trackPerformance)
    }

    return () => {
      window.removeEventListener('load', trackPerformance)
    }
  }, [])

  // This component doesn't render anything
  return null
}
