'use server';

import { z } from 'zod';
import { createClient } from '@supabase/supabase-js';
import {
  clientInformationSchema,
  packageSelectionSchema,
  PackageType,
  calculateTotalPrice,
} from '@/lib/mpesa/types';
import { generateReferenceCode } from '@/lib/mpesa/types';
import { sendProjectRequestNotification } from '@/lib/email-notifications';

// Create a Supabase client with service role for admin operations
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || '';
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY || '';
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || '';

// Create admin client with service role key
const supabaseAdmin = createClient(supabaseUrl, supabaseServiceKey);

// Create a fallback client with anon key
const supabaseFallback = createClient(supabaseUrl, supabaseAnonKey);

// Log Supabase connection details (without exposing sensitive keys)
console.log('Supabase URL configured:', !!supabaseUrl);
console.log('Supabase Service Key configured:', !!supabaseServiceKey);
console.log('Supabase Anon Key configured:', !!supabaseAnonKey);

// Create a project request
export async function createProjectRequest(formData: {
  client_name: string;
  client_email: string;
  client_phone: string;
  package_type: PackageType;
  additional_services?: string[];
  maintenance_plan?: string | null;
}) {
  console.log('Server action: createProjectRequest called with data:', JSON.stringify(formData));

  try {
    // Ensure additional_services is always an array
    const additional_services = Array.isArray(formData.additional_services) ? formData.additional_services : [];

    // Validate client information
    const validatedClientInfo = clientInformationSchema.parse({
      client_name: formData.client_name,
      client_email: formData.client_email,
      client_phone: formData.client_phone,
    });

    // Validate package selection
    const validatedPackageSelection = packageSelectionSchema.parse({
      package_type: formData.package_type,
      additional_services: additional_services,
      maintenance_plan: formData.maintenance_plan === undefined ? null : formData.maintenance_plan,
    });

    // Calculate total price
    const totalAmount = calculateTotalPrice(
      validatedPackageSelection.package_type,
      validatedPackageSelection.additional_services,
      validatedPackageSelection.maintenance_plan
    );

    // Generate a reference code
    const referenceCode = generateReferenceCode();

    // Log the data we're about to insert
    console.log('Attempting to insert project request with data:', {
      client_name: validatedClientInfo.client_name,
      client_email: validatedClientInfo.client_email,
      client_phone: validatedClientInfo.client_phone,
      package_type: validatedPackageSelection.package_type,
      additional_services: validatedPackageSelection.additional_services,
      maintenance_plan: validatedPackageSelection.maintenance_plan,
      total_amount: totalAmount,
      reference_code: referenceCode,
    });

    try {
      // Prepare the data to insert
      const projectData = {
        client_name: validatedClientInfo.client_name,
        client_email: validatedClientInfo.client_email,
        client_phone: validatedClientInfo.client_phone,
        package_type: validatedPackageSelection.package_type,
        additional_services: validatedPackageSelection.additional_services,
        maintenance_plan: validatedPackageSelection.maintenance_plan,
        total_amount: totalAmount,
        reference_code: referenceCode,
        status: 'pending',
      };

      // Try with admin client first
      try {
        console.log('Attempting to insert with admin client...');
        const { data: projectRequest, error } = await supabaseAdmin
          .from('project_requests')
          .insert([projectData])
          .select()
          .single();

        if (error) {
          console.error('Error creating project request with admin client:', error);
          throw error;
        }

        // Send email notification
        try {
          console.log('Sending project request notification email...');
          await sendProjectRequestNotification({
            client_name: projectRequest.client_name,
            client_email: projectRequest.client_email,
            client_phone: projectRequest.client_phone,
            package_type: projectRequest.package_type,
            additional_services: projectRequest.additional_services,
            maintenance_plan: projectRequest.maintenance_plan,
            total_amount: projectRequest.total_amount,
            reference_code: projectRequest.reference_code,
            created_at: projectRequest.created_at,
          });
          console.log('Project request notification email sent successfully');
        } catch (emailError) {
          console.error('Failed to send project request notification email:', emailError);
          // Don't fail the entire request if email fails
        }

        return {
          success: true,
          data: {
            id: projectRequest.id,
            reference_code: referenceCode,
            total_amount: totalAmount,
          },
        };
      } catch (adminError) {
        console.error('Admin client failed, trying fallback client:', adminError);

        // Try with fallback client
        const { data: projectRequest, error } = await supabaseFallback
          .from('project_requests')
          .insert([projectData])
          .select()
          .single();

        if (error) {
          console.error('Error creating project request with fallback client:', error);
          return {
            success: false,
            error: `Failed to create project request: ${error.message}`,
          };
        }

        console.log('Successfully inserted with fallback client');

        // Send email notification
        try {
          console.log('Sending project request notification email...');
          await sendProjectRequestNotification({
            client_name: projectRequest.client_name,
            client_email: projectRequest.client_email,
            client_phone: projectRequest.client_phone,
            package_type: projectRequest.package_type,
            additional_services: projectRequest.additional_services,
            maintenance_plan: projectRequest.maintenance_plan,
            total_amount: projectRequest.total_amount,
            reference_code: projectRequest.reference_code,
            created_at: projectRequest.created_at,
          });
          console.log('Project request notification email sent successfully');
        } catch (emailError) {
          console.error('Failed to send project request notification email:', emailError);
          // Don't fail the entire request if email fails
        }

        return {
          success: true,
          data: {
            id: projectRequest.id,
            reference_code: referenceCode,
            total_amount: totalAmount,
          },
        };
      }
    } catch (insertError) {
      console.error('Exception during project request insertion:', insertError);
      return {
        success: false,
        error: `Failed to create project request: ${insertError instanceof Error ? insertError.message : 'Unknown error'}`,
      };
    }
  } catch (error) {
    console.error('Error in createProjectRequest:', error);
    if (error instanceof z.ZodError) {
      return {
        success: false,
        error: error.errors.map(e => e.message).join(', '),
      };
    }
    return {
      success: false,
      error: error instanceof Error ? error.message : 'An unexpected error occurred',
    };
  }
}

// Generate questionnaire access token for a project request
export async function generateAccessToken(formData: {
  project_request_id: string;
}) {
  try {
    console.log('Server action: generateAccessToken called with data:', JSON.stringify(formData));

    // Generate a random access token
    const accessToken = Array.from(
      { length: 32 },
      () => Math.floor(Math.random() * 36).toString(36)
    ).join('');

    // Set expiration date (7 days from now)
    const expiresAt = new Date();
    expiresAt.setDate(expiresAt.getDate() + 7);

    // Get project request details
    const { data: projectRequest, error: projectError } = await supabaseAdmin
      .from('project_requests')
      .select('*')
      .eq('id', formData.project_request_id)
      .single();

    if (projectError || !projectRequest) {
      console.error('Error fetching project request:', projectError);
      return {
        success: false,
        error: `Project request not found: ${projectError?.message || 'Unknown error'}`,
      };
    }

    // Create client project record (needed for questionnaire)
    const { data: clientProject, error: clientError } = await supabaseAdmin
      .from('client_projects')
      .insert([
        {
          client_name: projectRequest.client_name,
          client_email: projectRequest.client_email,
          client_phone: projectRequest.client_phone,
          reference_code: projectRequest.reference_code,
          package_type: projectRequest.package_type,
          total_amount: projectRequest.total_amount,
          deposit_amount: Math.round(projectRequest.total_amount * 0.4), // Calculate deposit amount (40%)
          status: 'approved', // Mark as approved since we're manually generating access
        },
      ])
      .select()
      .single();

    if (clientError) {
      console.error('Error creating client project:', clientError);
      return {
        success: false,
        error: `Failed to create client project: ${clientError.message}`,
      };
    }

    // Add additional services if any
    if (projectRequest.additional_services && projectRequest.additional_services.length > 0) {
      try {
        // Import the services data
        const { ADDITIONAL_SERVICES } = await import('@/lib/mpesa/types');

        // Create records for each selected service
        const servicesToInsert = projectRequest.additional_services.map(serviceId => {
          const service = ADDITIONAL_SERVICES.find(s => s.id === serviceId);
          if (!service) {
            console.warn(`Service with ID ${serviceId} not found`);
            return null;
          }

          return {
            client_project_id: clientProject.id,
            service_name: service.name,
            service_price: service.price
          };
        }).filter(Boolean); // Remove null entries

        if (servicesToInsert.length > 0) {
          const { error: servicesError } = await supabaseAdmin
            .from('additional_services')
            .insert(servicesToInsert);

          if (servicesError) {
            console.error('Error adding additional services:', servicesError);
            // Continue anyway, this is not critical
          }
        }
      } catch (servicesError) {
        console.error('Error processing additional services:', servicesError);
        // Continue anyway, this is not critical
      }
    }

    // Add maintenance plan if selected
    if (projectRequest.maintenance_plan) {
      try {
        // Import the maintenance plans data
        const { MAINTENANCE_PLANS } = await import('@/lib/mpesa/types');

        // Find the selected plan
        const plan = MAINTENANCE_PLANS.find(p => p.id === projectRequest.maintenance_plan);

        if (plan) {
          const { error: planError } = await supabaseAdmin
            .from('maintenance_plans')
            .insert([{
              client_project_id: clientProject.id,
              plan_name: plan.name,
              plan_price: plan.price
            }]);

          if (planError) {
            console.error('Error adding maintenance plan:', planError);
            // Continue anyway, this is not critical
          }
        } else {
          console.warn(`Maintenance plan with ID ${projectRequest.maintenance_plan} not found`);
        }
      } catch (planError) {
        console.error('Error processing maintenance plan:', planError);
        // Continue anyway, this is not critical
      }
    }

    // Create questionnaire access record
    const { data: accessData, error: accessError } = await supabaseAdmin
      .from('questionnaire_access')
      .insert([
        {
          client_project_id: clientProject.id,
          access_token: accessToken,
          expires_at: expiresAt.toISOString(),
        },
      ])
      .select()
      .single();

    if (accessError) {
      console.error('Error generating questionnaire access:', accessError);
      return {
        success: false,
        error: `Failed to generate questionnaire access: ${accessError.message}`,
      };
    }

    // Update project request status
    await supabaseAdmin
      .from('project_requests')
      .update({ status: 'approved' })
      .eq('id', formData.project_request_id);

    return {
      success: true,
      data: {
        access_token: accessToken,
        client_project_id: clientProject.id,
        expires_at: expiresAt.toISOString(),
      },
    };
  } catch (error) {
    console.error('Error in generateAccessToken:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'An unexpected error occurred',
    };
  }
}

// Get all project requests
export async function getProjectRequests() {
  try {
    const { data, error } = await supabaseAdmin
      .from('project_requests')
      .select('*')
      .order('created_at', { ascending: false });

    if (error) {
      console.error('Error fetching project requests:', error);
      return {
        success: false,
        error: `Failed to fetch project requests: ${error.message}`,
      };
    }

    return {
      success: true,
      data,
    };
  } catch (error) {
    console.error('Error in getProjectRequests:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'An unexpected error occurred',
    };
  }
}
