# Visual Deployment Guide for Supabase Edge Function

This guide provides step-by-step instructions with visual cues to help you deploy the Edge Function through the Supabase web interface.

## Step 1: Access the Supabase Dashboard

1. Go to the Supabase Dashboard: https://supabase.com/dashboard/project/caogszaytzuiqwwkbhhi
2. Log in with your Supabase account credentials
3. You should see your project dashboard

## Step 2: Navigate to Edge Functions

1. In the left sidebar, look for "Edge Functions" (it should be under the "Database" section)
2. Click on "Edge Functions"
3. You should see a page with a "New Function" button in the top right

## Step 3: Create a New Edge Function

1. Click the "New Function" button
2. In the dialog that appears, enter "send-contact-email" as the function name
3. Click "Create Function"
4. You should be redirected to the function editor

## Step 4: Set Up the Shared CORS File

1. In the function editor, look for a file browser on the left side
2. Click on the "+" icon to create a new file
3. Enter "_shared/cors.ts" as the file path
4. In the editor that appears, paste the following code:

```typescript
export const corsHeaders = {
  "Access-Control-Allow-Origin": "*",
  "Access-Control-Allow-Headers": "authorization, x-client-info, apikey, content-type",
  "Access-Control-Allow-Methods": "POST, OPTIONS",
};
```

5. Click "Save" or press Ctrl+S to save the file

## Step 5: Edit the Main Function File

1. In the file browser, click on "index.ts" (it should be the default file)
2. Delete all the existing code
3. Paste the following code:

```typescript
// Follow this setup guide to integrate the Deno runtime into your application:
// https://deno.land/manual/examples/deploy_node_server

import { serve } from "https://deno.land/std@0.168.0/http/server.ts";
import { corsHeaders } from "../_shared/cors.ts";

interface EmailPayload {
  name: string;
  email: string;
  subject: string;
  message: string;
}

const RESEND_API_KEY = Deno.env.get("RESEND_API_KEY") || "";
const RECIPIENT_EMAIL = Deno.env.get("RECIPIENT_EMAIL") || "<EMAIL>";

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === "OPTIONS") {
    return new Response("ok", { headers: corsHeaders });
  }

  try {
    // Get the request payload
    const payload: EmailPayload = await req.json();
    
    // Validate the payload
    if (!payload.name || !payload.email || !payload.subject || !payload.message) {
      return new Response(
        JSON.stringify({
          success: false,
          error: "Missing required fields",
        }),
        {
          headers: { ...corsHeaders, "Content-Type": "application/json" },
          status: 400,
        }
      );
    }

    // Send email using Resend
    const res = await fetch("https://api.resend.com/emails", {
      method: "POST",
      headers: {
        "Authorization": `Bearer ${RESEND_API_KEY}`,
        "Content-Type": "application/json",
      },
      body: JSON.stringify({
        from: "Portfolio Contact Form <<EMAIL>>",
        to: RECIPIENT_EMAIL,
        subject: `Portfolio Contact: ${payload.subject}`,
        html: `
          <h2>New Contact Form Submission</h2>
          <p><strong>Name:</strong> ${payload.name}</p>
          <p><strong>Email:</strong> ${payload.email}</p>
          <p><strong>Subject:</strong> ${payload.subject}</p>
          <p><strong>Message:</strong></p>
          <p>${payload.message.replace(/\n/g, "<br>")}</p>
        `,
      }),
    });

    const data = await res.json();

    if (res.status >= 400) {
      console.error("Error sending email:", data);
      return new Response(
        JSON.stringify({
          success: false,
          error: "Failed to send email",
        }),
        {
          headers: { ...corsHeaders, "Content-Type": "application/json" },
          status: 500,
        }
      );
    }

    return new Response(
      JSON.stringify({
        success: true,
        data,
      }),
      {
        headers: { ...corsHeaders, "Content-Type": "application/json" },
        status: 200,
      }
    );
  } catch (error) {
    console.error("Error in send-contact-email function:", error);
    return new Response(
      JSON.stringify({
        success: false,
        error: "Internal server error",
      }),
      {
        headers: { ...corsHeaders, "Content-Type": "application/json" },
        status: 500,
      }
    );
  }
});
```

4. Click "Save" or press Ctrl+S to save the file

## Step 6: Set Environment Variables

1. Look for a tab or button labeled "Environment Variables" or "Settings"
2. Click on it to access the environment variables section
3. Add the following variables:
   - Key: `RESEND_API_KEY`, Value: `re_bHXZQij1_KrBUYTuM943sN4ViHBi5qaW3`
   - Key: `RECIPIENT_EMAIL`, Value: `<EMAIL>`
4. Click "Save" to save the environment variables

## Step 7: Deploy the Function

1. Look for a "Deploy" button (usually in the top right)
2. Click "Deploy"
3. Wait for the deployment to complete (you should see a success message)

## Step 8: Test the Function

1. Go to your portfolio website
2. Fill out and submit the contact form
3. You should receive an email <NAME_EMAIL>

## Visual Cues

- The Edge Functions section is usually represented by a lightning bolt icon
- The "New Function" button is typically a blue button in the top right
- The file browser is usually on the left side of the editor
- Environment variables are typically accessed through a tab or button near the top of the editor
- The "Deploy" button is usually a prominent button in the top right

## Troubleshooting

If you encounter any issues:

1. Check the Edge Function logs (usually accessible through a "Logs" tab)
2. Verify that the environment variables are set correctly
3. Make sure the Edge Function is deployed and active (status should be "Active" or "Running")
4. If you get CORS errors, make sure the CORS headers are set correctly in the _shared/cors.ts file
