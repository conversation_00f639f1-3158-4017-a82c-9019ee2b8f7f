'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { toast } from 'sonner';
import { motion } from 'framer-motion';
import { validateToken } from '@/app/actions/questionnaire-actions';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Loader2 } from 'lucide-react';
import Link from 'next/link';

export default function QuestionnaireAccessPage() {
  const [accessToken, setAccessToken] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const router = useRouter();

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!accessToken.trim()) {
      toast.error('Please enter an access token');
      return;
    }

    setIsLoading(true);

    try {
      const result = await validateToken(accessToken.trim());
      
      if (result.isValid) {
        toast.success('Access token validated successfully');
        router.push(`/questionnaire/${accessToken.trim()}`);
      } else {
        toast.error(result.error || 'Invalid access token');
        setIsLoading(false);
      }
    } catch (error) {
      console.error('Error validating token:', error);
      toast.error('An unexpected error occurred');
      setIsLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-background flex flex-col">
      <header className="border-b">
        <div className="container mx-auto px-4 py-4 flex items-center justify-between">
          <Link href="/" passHref>
            <Button variant="ghost" size="sm" className="gap-2">
              Back to Portfolio
            </Button>
          </Link>
          
          <div className="text-xl font-bold">Zachary Odero</div>
        </div>
      </header>

      <main className="flex-1 flex items-center justify-center p-4">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
          className="w-full max-w-md"
        >
          <Card className="border shadow-lg">
            <CardHeader className="space-y-1">
              <CardTitle className="text-2xl font-bold text-center">Project Questionnaire</CardTitle>
              <CardDescription className="text-center">
                Enter your access token to continue to the questionnaire
              </CardDescription>
            </CardHeader>
            <form onSubmit={handleSubmit}>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <Input
                    type="text"
                    placeholder="Enter your access token"
                    value={accessToken}
                    onChange={(e) => setAccessToken(e.target.value)}
                    className="w-full"
                    disabled={isLoading}
                  />
                  <p className="text-sm text-muted-foreground">
                    The access token was provided to you after our discussion about your project.
                  </p>
                </div>
              </CardContent>
              <CardFooter>
                <Button 
                  type="submit" 
                  className="w-full" 
                  disabled={isLoading}
                >
                  {isLoading ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Validating...
                    </>
                  ) : (
                    'Access Questionnaire'
                  )}
                </Button>
              </CardFooter>
            </form>
          </Card>

          <div className="mt-6 text-center">
            <p className="text-sm text-muted-foreground">
              Don't have an access token?{' '}
              <Link href="/start-project" className="text-primary hover:underline">
                Start a new project
              </Link>{' '}
              or{' '}
              <Link href="/contact" className="text-primary hover:underline">
                contact me
              </Link>{' '}
              for assistance.
            </p>
          </div>
        </motion.div>
      </main>

      <footer className="border-t py-6">
        <div className="container mx-auto px-4 text-center text-sm text-muted-foreground">
          &copy; {new Date().getFullYear()} Zachary Odero. All rights reserved.
        </div>
      </footer>
    </div>
  );
}
