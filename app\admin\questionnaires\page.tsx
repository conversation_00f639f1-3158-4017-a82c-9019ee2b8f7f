'use client';

import { useState, useEffect } from 'react';
import { toast } from 'sonner';
import { FileText, RefreshCcw, Eye, Loader2, Search, Filter, Calendar, CheckCircle, XCircle, Copy, Link, ExternalLink, Layers, Server, User } from 'lucide-react';
import { format, subDays } from 'date-fns';
import { motion, AnimatePresence } from 'framer-motion';
import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
  CardFooter,
} from '@/components/ui/card';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogFooter,
} from '@/components/ui/dialog';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from '@/components/ui/tabs';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import { createClient } from '@supabase/supabase-js';
import { QuestionnaireResponse } from '@/lib/questionnaire/types';

export default function QuestionnairesPage() {
  const [responses, setResponses] = useState<any[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [selectedResponse, setSelectedResponse] = useState<any | null>(null);
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [viewStatus, setViewStatus] = useState('all'); // 'all', 'seen', 'unseen'
  const [dateFilter, setDateFilter] = useState('all'); // 'all', 'today', 'week', 'month'
  const [packageFilter, setPackageFilter] = useState('all'); // 'all', 'basic', 'standard', 'premium'
  const [isGeneratingToken, setIsGeneratingToken] = useState(false);
  const [accessToken, setAccessToken] = useState<string | null>(null);
  const [tokenExpiresAt, setTokenExpiresAt] = useState<Date | null>(null);
  const [clientEmail, setClientEmail] = useState('');
  const [clientName, setClientName] = useState('');
  const [clientPhone, setClientPhone] = useState('');
  const [selectedPackage, setSelectedPackage] = useState<'basic' | 'standard' | 'premium'>('basic');
  const [tokenNotes, setTokenNotes] = useState('');
  const [isTokenDialogOpen, setIsTokenDialogOpen] = useState(false);
  const [viewedResponses, setViewedResponses] = useState<Set<string>>(new Set());

  // Create a Supabase client
  const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || '';
  const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || '';
  const supabase = createClient(supabaseUrl, supabaseAnonKey);

  // Fetch questionnaire responses
  const fetchResponses = async () => {
    setIsLoading(true);
    try {
      // Join questionnaire_responses with client_projects to get client info
      const { data, error } = await supabase
        .from('questionnaire_responses')
        .select(`
          *,
          client_projects:client_project_id (
            id,
            client_name,
            client_email,
            client_phone,
            package_type,
            reference_code
          )
        `)
        .order('updated_at', { ascending: false });

      if (error) {
        console.error('Error fetching questionnaire responses:', error);
        toast.error('Failed to load questionnaire responses');
        return;
      }

      setResponses(data || []);
    } catch (error) {
      console.error('Unexpected error fetching questionnaire responses:', error);
      toast.error('An unexpected error occurred');
    } finally {
      setIsLoading(false);
    }
  };

  // Initial fetch
  useEffect(() => {
    fetchResponses();

    // Load viewed responses from localStorage
    const storedViewedResponses = localStorage.getItem('viewedResponses');
    if (storedViewedResponses) {
      setViewedResponses(new Set(JSON.parse(storedViewedResponses)));
    }
  }, []);

  // Handle view response
  const handleViewResponse = (response: any) => {
    setSelectedResponse(response);
    setIsDialogOpen(true);

    // Mark as viewed
    const newViewedResponses = new Set(viewedResponses);
    newViewedResponses.add(response.id);
    setViewedResponses(newViewedResponses);

    // Save to localStorage
    localStorage.setItem('viewedResponses', JSON.stringify([...newViewedResponses]));
  };

  // Format array values for display
  const formatArrayValue = (value: string[] | undefined | null) => {
    if (!value || value.length === 0) return 'None';
    return value.join(', ');
  };

  // Filter responses based on search query and filters
  const filteredResponses = responses.filter((response) => {
    // Search filter
    const matchesSearch =
      searchQuery === '' ||
      response.client_projects?.client_name?.toLowerCase().includes(searchQuery.toLowerCase()) ||
      response.client_projects?.client_email?.toLowerCase().includes(searchQuery.toLowerCase()) ||
      response.project_name?.toLowerCase().includes(searchQuery.toLowerCase()) ||
      response.project_description?.toLowerCase().includes(searchQuery.toLowerCase());

    // View status filter
    const isViewed = viewedResponses.has(response.id);
    const matchesViewStatus =
      viewStatus === 'all' ||
      (viewStatus === 'seen' && isViewed) ||
      (viewStatus === 'unseen' && !isViewed);

    // Package filter
    const matchesPackage =
      packageFilter === 'all' ||
      response.client_projects?.package_type === packageFilter;

    // Date filter
    let matchesDate = true;
    const responseDate = new Date(response.updated_at);
    const today = new Date();
    today.setHours(0, 0, 0, 0);

    if (dateFilter === 'today') {
      const startOfDay = today;
      matchesDate = responseDate >= startOfDay;
    } else if (dateFilter === 'week') {
      const startOfWeek = subDays(today, 7);
      matchesDate = responseDate >= startOfWeek;
    } else if (dateFilter === 'month') {
      const startOfMonth = subDays(today, 30);
      matchesDate = responseDate >= startOfMonth;
    }

    return matchesSearch && matchesViewStatus && matchesPackage && matchesDate;
  });

  // Generate access token for a client
  const handleGenerateToken = async () => {
    if (!clientName || !clientEmail) {
      toast.error('Please enter client name and email');
      return;
    }

    setIsGeneratingToken(true);
    try {
      // Generate reference code with package prefix
      const packagePrefix = selectedPackage.charAt(0).toUpperCase();
      const referenceCode = `${packagePrefix}-${Date.now().toString(36).toUpperCase()}`;

      // Set default amounts based on package type using the actual KES prices
      let totalAmount = 0;
      let depositAmount = 0;

      switch(selectedPackage) {
        case 'basic':
          totalAmount = 75000;  // KES 75,000
          depositAmount = 30000; // 40% deposit
          break;
        case 'standard':
          totalAmount = 145000; // KES 145,000
          depositAmount = 58000; // 40% deposit
          break;
        case 'premium':
          totalAmount = 225000; // KES 225,000
          depositAmount = 90000; // 40% deposit
          break;
      }

      // Import the server action dynamically to avoid issues with 'use server' directive
      const { generateAccessToken } = await import('@/app/actions/token');

      // Call the server action to generate the token
      const result = await generateAccessToken({
        clientName,
        clientEmail,
        clientPhone,
        packageType: selectedPackage,
        referenceCode,
        totalAmount,
        depositAmount
      });

      if (!result.success) {
        toast.error(result.error || 'Failed to generate access token');
        return;
      }

      // Set the token and expiration date
      setAccessToken(result.accessToken);
      setTokenExpiresAt(new Date(result.expiresAt));
      toast.success('Access token generated successfully');

    } catch (error) {
      console.error('Unexpected error in token generation process:', error);
      toast.error('An unexpected error occurred during token generation');
    } finally {
      setIsGeneratingToken(false);
    }
  };

  // Copy access token to clipboard
  const handleCopyToken = () => {
    if (accessToken) {
      navigator.clipboard.writeText(accessToken);
      toast.success('Access token copied to clipboard');
    }
  };

  // Copy questionnaire URL to clipboard
  const handleCopyQuestionnaireUrl = () => {
    if (accessToken) {
      const url = `${window.location.origin}/questionnaire/${accessToken}`;
      navigator.clipboard.writeText(url);
      toast.success('Questionnaire URL copied to clipboard');
    }
  };

  // Open token generation dialog
  const openTokenDialog = () => {
    // Reset all form fields
    setClientName('');
    setClientEmail('');
    setClientPhone('');
    setSelectedPackage('basic');
    setTokenNotes('');
    setAccessToken(null);
    setTokenExpiresAt(null);
    setIsTokenDialogOpen(true);
  };

  // Close token generation dialog
  const closeTokenDialog = () => {
    // If we have a token, confirm before closing
    if (accessToken && !confirm('Are you sure you want to close? Make sure you have copied the token or URL.')) {
      return;
    }
    setIsTokenDialogOpen(false);
  };

  return (
    <div className="space-y-6">
      <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between gap-4">
        <div>
          <h1 className="text-2xl font-black tracking-tight">Questionnaire Responses</h1>
          <p className="text-muted-foreground font-semibold">
            View and manage client questionnaire responses
          </p>
        </div>
        <div className="flex gap-2">
          <Button onClick={openTokenDialog} variant="default" size="sm">
            <Link className="h-4 w-4 mr-2" />
            Generate Access Token
          </Button>
          <Button onClick={fetchResponses} variant="outline" size="sm">
            <RefreshCcw className="h-4 w-4 mr-2" />
            Refresh
          </Button>
        </div>
      </div>

      <div className="grid gap-6">
        {/* Filters */}
        <Card className="border-none shadow-sm">
          <CardContent className="p-4">
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
              {/* Search */}
              <div className="relative">
                <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="Search responses..."
                  className="pl-8"
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                />
              </div>

              {/* View Status Filter */}
              <Select value={viewStatus} onValueChange={setViewStatus}>
                <SelectTrigger className="w-full">
                  <SelectValue placeholder="Filter by status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Responses</SelectItem>
                  <SelectItem value="seen">Viewed</SelectItem>
                  <SelectItem value="unseen">Unviewed</SelectItem>
                </SelectContent>
              </Select>

              {/* Package Filter */}
              <Select value={packageFilter} onValueChange={setPackageFilter}>
                <SelectTrigger className="w-full">
                  <SelectValue placeholder="Filter by package" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Packages</SelectItem>
                  <SelectItem value="basic">Basic</SelectItem>
                  <SelectItem value="standard">Standard</SelectItem>
                  <SelectItem value="premium">Premium</SelectItem>
                </SelectContent>
              </Select>

              {/* Date Filter */}
              <Select value={dateFilter} onValueChange={setDateFilter}>
                <SelectTrigger className="w-full">
                  <SelectValue placeholder="Filter by date" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Time</SelectItem>
                  <SelectItem value="today">Today</SelectItem>
                  <SelectItem value="week">Last 7 Days</SelectItem>
                  <SelectItem value="month">Last 30 Days</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </CardContent>
        </Card>

        {/* Responses */}
        <Card>
          <CardHeader className="pb-3">
            <div className="flex justify-between items-center">
              <div>
                <CardTitle>Responses</CardTitle>
                <CardDescription>
                  {filteredResponses.length} questionnaire {filteredResponses.length === 1 ? 'response' : 'responses'} found
                </CardDescription>
              </div>
              <Badge variant="outline" className="ml-2">
                {responses.length} Total
              </Badge>
            </div>
          </CardHeader>
          <CardContent>
            {isLoading ? (
              <div className="flex justify-center py-8">
                <Loader2 className="h-8 w-8 animate-spin text-primary" />
              </div>
            ) : filteredResponses.length === 0 ? (
              <motion.div
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                className="text-center py-12 text-muted-foreground"
              >
                <FileText className="h-16 w-16 mx-auto mb-4 opacity-20" />
                <p className="text-lg font-medium">No responses found</p>
                <p className="text-sm mt-1">Try adjusting your filters or search criteria</p>
              </motion.div>
            ) : (
              <>
                {/* Desktop Table View */}
                <div className="hidden md:block rounded-md border overflow-hidden">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead className="font-semibold">Date</TableHead>
                      <TableHead className="font-semibold">Client</TableHead>
                      <TableHead className="font-semibold">Package</TableHead>
                      <TableHead className="font-semibold">Status</TableHead>
                      <TableHead className="text-right font-semibold">Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    <AnimatePresence>
                      {filteredResponses.map((response) => {
                        const isViewed = viewedResponses.has(response.id);
                        return (
                          <motion.tr
                            key={response.id}
                            initial={{ opacity: 0, y: 10 }}
                            animate={{ opacity: 1, y: 0 }}
                            exit={{ opacity: 0 }}
                            transition={{ duration: 0.2 }}
                            className={`${isViewed ? '' : 'bg-blue-50 dark:bg-blue-900/10'} relative`}
                          >
                            <TableCell className="whitespace-nowrap font-semibold">
                              {format(new Date(response.updated_at), 'MMM d, yyyy')}
                              <div className="text-xs text-muted-foreground font-medium">
                                {format(new Date(response.updated_at), 'h:mm a')}
                              </div>
                            </TableCell>
                            <TableCell>
                              <div className="font-bold">{response.client_projects?.client_name || 'Unknown'}</div>
                              <div className="text-sm text-muted-foreground font-medium">{response.client_projects?.client_email || 'No email'}</div>
                            </TableCell>
                            <TableCell className="whitespace-nowrap">
                              <Badge
                                variant="outline"
                                className={`capitalize font-semibold ${
                                  response.client_projects?.package_type === 'premium'
                                    ? 'bg-amber-50 text-amber-700 border-amber-200 dark:bg-amber-900/20 dark:text-amber-400 dark:border-amber-800/50'
                                    : response.client_projects?.package_type === 'standard'
                                    ? 'bg-purple-50 text-purple-700 border-purple-200 dark:bg-purple-900/20 dark:text-purple-400 dark:border-purple-800/50'
                                    : 'bg-blue-50 text-blue-700 border-blue-200 dark:bg-blue-900/20 dark:text-blue-400 dark:border-blue-800/50'
                                }`}
                              >
                                {response.client_projects?.package_type || 'Unknown'}
                              </Badge>
                            </TableCell>
                            <TableCell>
                              <Badge
                                variant={
                                  response.completion_status === 'completed'
                                    ? 'success'
                                    : response.completion_status === 'in_progress'
                                    ? 'outline'
                                    : 'destructive'
                                }
                                className={`font-semibold ${
                                  response.completion_status === 'completed'
                                    ? 'bg-green-100 text-green-700 border-green-200 dark:bg-green-900/20 dark:text-green-400 dark:border-green-800/50'
                                    : response.completion_status === 'in_progress'
                                    ? 'bg-amber-50 text-amber-700 border-amber-200 dark:bg-amber-900/20 dark:text-amber-400 dark:border-amber-800/50'
                                    : ''
                                }`}
                              >
                                {response.completion_status.replace('_', ' ').charAt(0).toUpperCase() + response.completion_status.replace('_', ' ').slice(1)}
                              </Badge>
                              {!isViewed && (
                                <Badge className="ml-2 bg-blue-500 hover:bg-blue-600 font-semibold">New</Badge>
                              )}
                            </TableCell>
                            <TableCell className="text-right">
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={() => handleViewResponse(response)}
                                className="hover:bg-blue-50 dark:hover:bg-blue-900/20 font-medium"
                              >
                                <Eye className="h-4 w-4 mr-2" />
                                View
                              </Button>
                            </TableCell>
                          </motion.tr>
                        );
                      })}
                    </AnimatePresence>
                  </TableBody>
                </Table>
              </div>

              {/* Mobile Card View */}
              <div className="md:hidden space-y-4">
                <AnimatePresence>
                  {filteredResponses.map((response) => {
                    const isViewed = viewedResponses.has(response.id);
                    return (
                      <motion.div
                        key={response.id}
                        initial={{ opacity: 0, y: 10 }}
                        animate={{ opacity: 1, y: 0 }}
                        exit={{ opacity: 0 }}
                        transition={{ duration: 0.2 }}
                      >
                        <Card className={`border shadow-sm hover:shadow-md transition-all duration-200 ${
                          !isViewed ? 'border-blue-200 bg-blue-50/50 dark:border-blue-800 dark:bg-blue-900/10' : 'border-gray-200 dark:border-gray-700'
                        }`}>
                          <CardContent className="p-4">
                            <div className="flex justify-between items-start mb-3">
                              <div className="flex-1">
                                <h3 className="font-bold text-base">{response.client_projects?.client_name || 'Unknown'}</h3>
                                <p className="text-sm text-muted-foreground font-medium">{response.client_projects?.client_email || 'No email'}</p>
                                <p className="text-xs text-muted-foreground font-medium mt-1">
                                  {format(new Date(response.updated_at), 'MMM d, yyyy')} • {format(new Date(response.updated_at), 'h:mm a')}
                                </p>
                              </div>
                              <div className="flex flex-col items-end gap-2">
                                <Badge
                                  variant={
                                    response.completion_status === 'completed'
                                      ? 'success'
                                      : response.completion_status === 'in_progress'
                                      ? 'outline'
                                      : 'destructive'
                                  }
                                  className={`font-semibold ${
                                    response.completion_status === 'completed'
                                      ? 'bg-green-100 text-green-700 border-green-200 dark:bg-green-900/20 dark:text-green-400 dark:border-green-800/50'
                                      : response.completion_status === 'in_progress'
                                      ? 'bg-amber-50 text-amber-700 border-amber-200 dark:bg-amber-900/20 dark:text-amber-400 dark:border-amber-800/50'
                                      : ''
                                  }`}
                                >
                                  {response.completion_status.replace('_', ' ').charAt(0).toUpperCase() + response.completion_status.replace('_', ' ').slice(1)}
                                </Badge>
                                {!isViewed && (
                                  <Badge className="bg-blue-500 hover:bg-blue-600 font-semibold">New</Badge>
                                )}
                              </div>
                            </div>

                            <div className="flex justify-between items-center">
                              <Badge
                                variant="outline"
                                className={`capitalize font-semibold ${
                                  response.client_projects?.package_type === 'premium'
                                    ? 'bg-amber-50 text-amber-700 border-amber-200 dark:bg-amber-900/20 dark:text-amber-400 dark:border-amber-800/50'
                                    : response.client_projects?.package_type === 'standard'
                                    ? 'bg-purple-50 text-purple-700 border-purple-200 dark:bg-purple-900/20 dark:text-purple-400 dark:border-purple-800/50'
                                    : 'bg-blue-50 text-blue-700 border-blue-200 dark:bg-blue-900/20 dark:text-blue-400 dark:border-blue-800/50'
                                }`}
                              >
                                {response.client_projects?.package_type || 'Unknown'}
                              </Badge>

                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={() => handleViewResponse(response)}
                                className="hover:bg-blue-50 dark:hover:bg-blue-900/20 font-medium"
                              >
                                <Eye className="h-4 w-4 mr-2" />
                                View
                              </Button>
                            </div>
                          </CardContent>
                        </Card>
                      </motion.div>
                    );
                  })}
                </AnimatePresence>
              </div>
              </>
            )}
          </CardContent>
          <CardFooter className="flex justify-between border-t p-4">
            <div className="text-sm text-muted-foreground">
              Showing {filteredResponses.length} of {responses.length} responses
            </div>
            <div className="flex gap-2">
              <Button variant="outline" size="sm" onClick={() => {
                setViewStatus('all');
                setDateFilter('all');
                setPackageFilter('all');
                setSearchQuery('');
              }}>
                Reset Filters
              </Button>
            </div>
          </CardFooter>
        </Card>
      </div>

      {/* Response Details Dialog */}
      <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
        <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
          <DialogHeader className="pb-4 border-b bg-gradient-to-r from-primary/5 to-transparent rounded-t-lg">
            <div className="flex items-center gap-3">
              <div className="bg-primary/10 p-2 rounded-full">
                <FileText className="h-5 w-5 text-primary" />
              </div>
              <div>
                <DialogTitle className="text-xl font-bold">
                  Questionnaire Response Details
                </DialogTitle>
                <DialogDescription className="text-sm mt-1">
                  Detailed information from the client questionnaire
                </DialogDescription>
              </div>
            </div>
          </DialogHeader>

          {!selectedResponse ? (
            <div className="flex justify-center items-center py-12">
              <Loader2 className="h-8 w-8 animate-spin text-primary" />
            </div>
          ) : (
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ duration: 0.3 }}
              className="space-y-8 py-2"
            >
              {/* Client Information */}
              <Card className="overflow-hidden border-blue-100 dark:border-blue-900/30 shadow-sm hover:shadow-md transition-shadow duration-300">
                <CardHeader className="bg-gradient-to-r from-blue-50 to-blue-50/50 dark:from-blue-900/30 dark:to-blue-900/10 pb-3">
                  <div className="flex items-center gap-2">
                    <div className="bg-blue-100 dark:bg-blue-800/50 p-1.5 rounded-full">
                      <User className="h-5 w-5 text-blue-600 dark:text-blue-400" />
                    </div>
                    <div>
                      <CardTitle className="text-lg text-blue-700 dark:text-blue-400">Client Information</CardTitle>
                      <CardDescription className="text-blue-600/70 dark:text-blue-400/70">
                        Basic details about the client and their package
                      </CardDescription>
                    </div>
                  </div>
                </CardHeader>
                <CardContent className="pt-5">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div className="bg-white dark:bg-gray-800/50 p-3 rounded-lg border border-gray-100 dark:border-gray-800 shadow-sm">
                      <p className="text-sm font-medium text-blue-600/80 dark:text-blue-400/80 mb-1.5">Name</p>
                      <p className="font-medium text-lg">{selectedResponse.client_projects?.client_name || 'Not provided'}</p>
                    </div>
                    <div className="bg-white dark:bg-gray-800/50 p-3 rounded-lg border border-gray-100 dark:border-gray-800 shadow-sm">
                      <p className="text-sm font-medium text-blue-600/80 dark:text-blue-400/80 mb-1.5">Email</p>
                      <p className="font-medium text-lg">{selectedResponse.client_projects?.client_email || 'Not provided'}</p>
                    </div>
                    <div className="bg-white dark:bg-gray-800/50 p-3 rounded-lg border border-gray-100 dark:border-gray-800 shadow-sm">
                      <p className="text-sm font-medium text-blue-600/80 dark:text-blue-400/80 mb-1.5">Phone</p>
                      <p className="font-medium text-lg">{selectedResponse.client_projects?.client_phone || 'Not provided'}</p>
                    </div>
                    <div className="bg-white dark:bg-gray-800/50 p-3 rounded-lg border border-gray-100 dark:border-gray-800 shadow-sm">
                      <p className="text-sm font-medium text-blue-600/80 dark:text-blue-400/80 mb-1.5">Package Type</p>
                      <Badge
                        variant="outline"
                        className={`capitalize font-medium text-base px-3 py-1 ${
                          selectedResponse.client_projects?.package_type === 'premium'
                            ? 'bg-amber-50 text-amber-700 border-amber-200 dark:bg-amber-900/20 dark:text-amber-400 dark:border-amber-800/50'
                            : selectedResponse.client_projects?.package_type === 'standard'
                            ? 'bg-purple-50 text-purple-700 border-purple-200 dark:bg-purple-900/20 dark:text-purple-400 dark:border-purple-800/50'
                            : 'bg-blue-50 text-blue-700 border-blue-200 dark:bg-blue-900/20 dark:text-blue-400 dark:border-blue-800/50'
                        }`}
                      >
                        {selectedResponse.client_projects?.package_type || 'Not provided'}
                      </Badge>
                    </div>
                    <div className="md:col-span-2 bg-gray-50 dark:bg-gray-800/30 p-3 rounded-lg border border-gray-100 dark:border-gray-800 shadow-sm">
                      <p className="text-sm font-medium text-blue-600/80 dark:text-blue-400/80 mb-1.5">Reference Code</p>
                      <div className="flex items-center gap-2">
                        <code className="bg-white dark:bg-gray-800 px-3 py-1.5 rounded text-sm font-mono border border-gray-100 dark:border-gray-700 flex-1">
                          {selectedResponse.client_projects?.reference_code || 'Not available'}
                        </code>
                        {selectedResponse.client_projects?.reference_code && (
                          <Button
                            variant="outline"
                            size="icon"
                            className="h-9 w-9 border-blue-200 dark:border-blue-800 hover:bg-blue-50 dark:hover:bg-blue-900/20"
                            onClick={() => {
                              navigator.clipboard.writeText(selectedResponse.client_projects?.reference_code || '');
                              toast.success('Reference code copied to clipboard');
                            }}
                          >
                            <Copy className="h-4 w-4 text-blue-600 dark:text-blue-400" />
                          </Button>
                        )}
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* General Information */}
              <Card className="overflow-hidden border-green-100 dark:border-green-900/30 shadow-sm hover:shadow-md transition-shadow duration-300">
                <CardHeader className="bg-gradient-to-r from-green-50 to-green-50/50 dark:from-green-900/30 dark:to-green-900/10 pb-3">
                  <div className="flex items-center gap-2">
                    <div className="bg-green-100 dark:bg-green-800/50 p-1.5 rounded-full">
                      <FileText className="h-5 w-5 text-green-600 dark:text-green-400" />
                    </div>
                    <div>
                      <CardTitle className="text-lg text-green-700 dark:text-green-400">General Information</CardTitle>
                      <CardDescription className="text-green-600/70 dark:text-green-400/70">
                        Project overview and requirements
                      </CardDescription>
                    </div>
                  </div>
                </CardHeader>
                <CardContent className="pt-5">
                  <div className="grid grid-cols-1 gap-6">
                    <div className="bg-white dark:bg-gray-800/50 p-4 rounded-lg border border-gray-100 dark:border-gray-800 shadow-sm">
                      <p className="text-sm font-medium text-green-600/80 dark:text-green-400/80 mb-1.5">Project Name</p>
                      <p className="font-medium text-lg">{selectedResponse.project_name || 'Not provided'}</p>
                    </div>

                    <div className="bg-white dark:bg-gray-800/50 p-4 rounded-lg border border-gray-100 dark:border-gray-800 shadow-sm">
                      <p className="text-sm font-medium text-green-600/80 dark:text-green-400/80 mb-1.5">Project Description</p>
                      <p className="whitespace-pre-line">{selectedResponse.project_description || 'Not provided'}</p>
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div className="bg-white dark:bg-gray-800/50 p-4 rounded-lg border border-gray-100 dark:border-gray-800 shadow-sm">
                        <p className="text-sm font-medium text-green-600/80 dark:text-green-400/80 mb-1.5">Target Audience</p>
                        <p>{selectedResponse.target_audience || 'Not provided'}</p>
                      </div>

                      <div className="bg-white dark:bg-gray-800/50 p-4 rounded-lg border border-gray-100 dark:border-gray-800 shadow-sm">
                        <p className="text-sm font-medium text-green-600/80 dark:text-green-400/80 mb-1.5">Color Scheme</p>
                        <p>{selectedResponse.color_scheme || 'Not provided'}</p>
                      </div>
                    </div>

                    <div className="bg-white dark:bg-gray-800/50 p-4 rounded-lg border border-gray-100 dark:border-gray-800 shadow-sm">
                      <p className="text-sm font-medium text-green-600/80 dark:text-green-400/80 mb-1.5">Project Goals</p>
                      {selectedResponse.project_goals && selectedResponse.project_goals.length > 0 ? (
                        <ul className="list-disc pl-5 space-y-1 mt-2">
                          {selectedResponse.project_goals.map((goal: string, index: number) => (
                            <li key={index} className="text-gray-800 dark:text-gray-200">{goal}</li>
                          ))}
                        </ul>
                      ) : (
                        <p className="text-muted-foreground italic">None specified</p>
                      )}
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div className="bg-white dark:bg-gray-800/50 p-4 rounded-lg border border-gray-100 dark:border-gray-800 shadow-sm">
                        <p className="text-sm font-medium text-green-600/80 dark:text-green-400/80 mb-1.5">Has Logo or Brand Guidelines</p>
                        <Badge
                          variant={selectedResponse.has_logo_or_brand_guidelines ? "default" : "outline"}
                          className={selectedResponse.has_logo_or_brand_guidelines
                            ? "bg-green-100 text-green-700 hover:bg-green-100 border-green-200 dark:bg-green-900/20 dark:text-green-400 dark:border-green-800/50"
                            : ""}
                        >
                          {selectedResponse.has_logo_or_brand_guidelines ? 'Yes' : 'No'}
                        </Badge>
                      </div>

                      <div className="bg-white dark:bg-gray-800/50 p-4 rounded-lg border border-gray-100 dark:border-gray-800 shadow-sm">
                        <p className="text-sm font-medium text-green-600/80 dark:text-green-400/80 mb-1.5">Preferred Launch Timeline</p>
                        <p>{selectedResponse.preferred_launch_timeline || 'Not provided'}</p>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Package-Specific Information */}
              <Card>
                <CardHeader className="pb-3">
                  <div className="flex items-center gap-2">
                    <Layers className="h-5 w-5 text-primary" />
                    <CardTitle className="text-lg">Package-Specific Information</CardTitle>
                  </div>
                  <CardDescription>
                    Details specific to the selected package
                  </CardDescription>
                </CardHeader>
                <CardContent className="pt-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div className="space-y-2 md:col-span-2">
                      <p className="text-sm font-medium text-muted-foreground">Requested Pages</p>
                      {selectedResponse.requested_pages && selectedResponse.requested_pages.length > 0 ? (
                        <ul className="list-disc pl-5 space-y-1">
                          {selectedResponse.requested_pages.map((page: string, index: number) => (
                            <li key={index}>{page}</li>
                          ))}
                        </ul>
                      ) : (
                        <p className="text-muted-foreground italic">None specified</p>
                      )}
                    </div>
                    <div className="space-y-1">
                      <p className="text-sm font-medium text-muted-foreground">Has Content Ready</p>
                      <Badge variant={selectedResponse.has_content_ready ? "default" : "outline"}>
                        {selectedResponse.has_content_ready ? 'Yes' : 'No'}
                      </Badge>
                    </div>
                    <div className="space-y-1">
                      <p className="text-sm font-medium text-muted-foreground">Has Domain Name</p>
                      <Badge variant={selectedResponse.has_domain_name ? "default" : "outline"}>
                        {selectedResponse.has_domain_name ? 'Yes' : 'No'}
                      </Badge>
                    </div>
                    <div className="space-y-1">
                      <p className="text-sm font-medium text-muted-foreground">Preferred Hosting</p>
                      <p>{selectedResponse.preferred_hosting || 'Not provided'}</p>
                    </div>
                    <div className="space-y-2 md:col-span-2">
                      <p className="text-sm font-medium text-muted-foreground">Contact Form Fields</p>
                      {selectedResponse.contact_form_fields && selectedResponse.contact_form_fields.length > 0 ? (
                        <div className="flex flex-wrap gap-2">
                          {selectedResponse.contact_form_fields.map((field: string, index: number) => (
                            <Badge key={index} variant="secondary">{field}</Badge>
                          ))}
                        </div>
                      ) : (
                        <p className="text-muted-foreground italic">None specified</p>
                      )}
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Standard/Premium Package Fields */}
              {(selectedResponse.client_projects?.package_type === 'standard' ||
                selectedResponse.client_projects?.package_type === 'premium') && (
                <Card className="border-purple-100 dark:border-purple-900/30">
                  <CardHeader className="bg-purple-50 dark:bg-purple-900/20 pb-3">
                    <div className="flex items-center gap-2">
                      <Server className="h-5 w-5 text-purple-600 dark:text-purple-400" />
                      <CardTitle className="text-lg text-purple-700 dark:text-purple-400">Advanced Features</CardTitle>
                    </div>
                    <CardDescription>
                      Additional features for Standard/Premium packages
                    </CardDescription>
                  </CardHeader>
                  <CardContent className="pt-4">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      <div className="space-y-1">
                        <p className="text-sm font-medium text-muted-foreground">Blog Content Type</p>
                        <p>{selectedResponse.blog_content_type || 'Not provided'}</p>
                      </div>
                      <div className="space-y-1">
                        <p className="text-sm font-medium text-muted-foreground">Blog Update Frequency</p>
                        <p>{selectedResponse.blog_update_frequency || 'Not provided'}</p>
                      </div>
                      <div className="space-y-1">
                        <p className="text-sm font-medium text-muted-foreground">CMS Preference</p>
                        <p>{selectedResponse.cms_preference || 'Not provided'}</p>
                      </div>
                      <div className="space-y-2 md:col-span-2">
                        <p className="text-sm font-medium text-muted-foreground">Authentication Features</p>
                        {selectedResponse.auth_features && selectedResponse.auth_features.length > 0 ? (
                          <div className="flex flex-wrap gap-2">
                            {selectedResponse.auth_features.map((feature: string, index: number) => (
                              <Badge key={index} variant="secondary">{feature}</Badge>
                            ))}
                          </div>
                        ) : (
                          <p className="text-muted-foreground italic">None specified</p>
                        )}
                      </div>
                      <div className="space-y-1 md:col-span-2">
                        <p className="text-sm font-medium text-muted-foreground">Design References</p>
                        <p>{selectedResponse.design_references || 'Not provided'}</p>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              )}

              {/* Premium Package Fields */}
              {selectedResponse.client_projects?.package_type === 'premium' && (
                <Card className="border-amber-100 dark:border-amber-900/30">
                  <CardHeader className="bg-amber-50 dark:bg-amber-900/20 pb-3">
                    <div className="flex items-center gap-2">
                      <Server className="h-5 w-5 text-amber-600 dark:text-amber-400" />
                      <CardTitle className="text-lg text-amber-700 dark:text-amber-400">Premium Features</CardTitle>
                    </div>
                    <CardDescription>
                      Advanced functionality for Premium package
                    </CardDescription>
                  </CardHeader>
                  <CardContent className="pt-4">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      <div className="space-y-2 md:col-span-2">
                        <p className="text-sm font-medium text-muted-foreground">E-commerce Features</p>
                        {selectedResponse.ecommerce_features && selectedResponse.ecommerce_features.length > 0 ? (
                          <div className="flex flex-wrap gap-2">
                            {selectedResponse.ecommerce_features.map((feature: string, index: number) => (
                              <Badge key={index} variant="secondary">{feature}</Badge>
                            ))}
                          </div>
                        ) : (
                          <p className="text-muted-foreground italic">None specified</p>
                        )}
                      </div>
                      <div className="space-y-2 md:col-span-2">
                        <p className="text-sm font-medium text-muted-foreground">Payment Methods</p>
                        {selectedResponse.payment_methods && selectedResponse.payment_methods.length > 0 ? (
                          <div className="flex flex-wrap gap-2">
                            {selectedResponse.payment_methods.map((method: string, index: number) => (
                              <Badge key={index} variant="secondary">{method}</Badge>
                            ))}
                          </div>
                        ) : (
                          <p className="text-muted-foreground italic">None specified</p>
                        )}
                      </div>
                      <div className="space-y-2 md:col-span-2">
                        <p className="text-sm font-medium text-muted-foreground">Third-Party APIs</p>
                        {selectedResponse.third_party_apis && selectedResponse.third_party_apis.length > 0 ? (
                          <div className="flex flex-wrap gap-2">
                            {selectedResponse.third_party_apis.map((api: string, index: number) => (
                              <Badge key={index} variant="secondary">{api}</Badge>
                            ))}
                          </div>
                        ) : (
                          <p className="text-muted-foreground italic">None specified</p>
                        )}
                      </div>
                      <div className="space-y-1 md:col-span-2">
                        <p className="text-sm font-medium text-muted-foreground">Database Requirements</p>
                        <p>{selectedResponse.database_requirements || 'Not provided'}</p>
                      </div>
                      <div className="space-y-2 md:col-span-2">
                        <p className="text-sm font-medium text-muted-foreground">Admin Panel Features</p>
                        {selectedResponse.admin_panel_features && selectedResponse.admin_panel_features.length > 0 ? (
                          <div className="flex flex-wrap gap-2">
                            {selectedResponse.admin_panel_features.map((feature: string, index: number) => (
                              <Badge key={index} variant="secondary">{feature}</Badge>
                            ))}
                          </div>
                        ) : (
                          <p className="text-muted-foreground italic">None specified</p>
                        )}
                      </div>
                      <div className="space-y-1">
                        <p className="text-sm font-medium text-muted-foreground">Needs User Roles</p>
                        <Badge variant={selectedResponse.needs_user_roles ? "default" : "outline"}>
                          {selectedResponse.needs_user_roles ? 'Yes' : 'No'}
                        </Badge>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              )}
            </motion.div>
          )}

          <DialogFooter className="mt-6 pt-4 border-t flex-col sm:flex-row gap-3 bg-gray-50 dark:bg-gray-900/20 sticky bottom-0 p-4 rounded-b-lg">
            <div className="flex items-center gap-2 bg-white dark:bg-gray-800 px-3 py-2 rounded-md border border-gray-200 dark:border-gray-700 shadow-sm">
              <Calendar className="h-4 w-4 text-primary" />
              <span className="text-sm font-medium">
                Last updated: {selectedResponse && format(new Date(selectedResponse.updated_at), 'MMM d, yyyy h:mm a')}
              </span>
            </div>
            <div className="flex gap-3 ml-auto">
              <Button
                variant="outline"
                size="default"
                onClick={() => setIsDialogOpen(false)}
                className="border-gray-300 hover:bg-gray-100 dark:border-gray-600 dark:hover:bg-gray-800 transition-colors"
              >
                <XCircle className="h-4 w-4 mr-2 text-gray-500" />
                Close
              </Button>
              <Button
                variant="default"
                size="default"
                onClick={() => {
                  // Mark as handled logic could go here
                  toast.success('Response marked as handled');
                  setIsDialogOpen(false);
                }}
                className="bg-primary hover:bg-primary/90 transition-colors shadow-sm"
              >
                <CheckCircle className="h-4 w-4 mr-2" />
                Mark as Handled
              </Button>
            </div>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Token Generation Dialog */}
      <Dialog open={isTokenDialogOpen} onOpenChange={setIsTokenDialogOpen}>
        <DialogContent className="max-w-3xl max-h-[95vh] overflow-y-auto p-0 mx-2 sm:mx-4 lg:mx-auto">
          <DialogHeader className="pb-4 sm:pb-5 sticky top-0 bg-background z-10 px-3 sm:px-4 lg:px-6 pt-4 sm:pt-6 bg-gradient-to-r from-primary/5 to-transparent rounded-t-lg">
            <div className="flex items-center gap-3">
              <div className="bg-primary/10 p-2 rounded-full">
                <Link className="h-5 w-5 text-primary" />
              </div>
              <div className="flex-1 min-w-0">
                <DialogTitle className="text-lg sm:text-xl font-bold">
                  Generate Access Token
                </DialogTitle>
                <DialogDescription className="text-sm mt-1 font-medium">
                  Create a package-specific access token for a client
                </DialogDescription>
              </div>
            </div>
          </DialogHeader>

          {accessToken ? (
            <motion.div
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.4 }}
              className="px-3 sm:px-4 lg:px-6 py-4 sm:py-6 space-y-6"
            >
              <div className="flex flex-col items-center text-center mb-6">
                <motion.div
                  initial={{ scale: 0, opacity: 0 }}
                  animate={{ scale: 1, opacity: 1 }}
                  transition={{ delay: 0.2, type: 'spring', stiffness: 200 }}
                  className="relative mb-4"
                >
                  <div className="absolute inset-0 bg-green-100 dark:bg-green-900/30 rounded-full scale-[1.6] blur-md animate-pulse" />
                  <div className="relative bg-white dark:bg-gray-800 rounded-full p-3 shadow-lg border border-green-200 dark:border-green-800">
                    <CheckCircle className="h-12 w-12 text-green-500" />
                  </div>
                </motion.div>
                <h2 className="text-2xl font-bold text-gray-900 dark:text-gray-100 mb-2 bg-gradient-to-r from-green-500 to-blue-500 bg-clip-text text-transparent">
                  Token Generated Successfully
                </h2>
                <p className="text-gray-600 dark:text-gray-300 max-w-md">
                  Share this token with your client to access the questionnaire tailored to their package
                </p>
              </div>

              <div className="bg-white dark:bg-gray-800/50 rounded-lg border border-gray-200 dark:border-gray-700 shadow-sm p-5">
                <div className="flex flex-wrap items-center gap-3 mb-4 pb-3 border-b border-gray-100 dark:border-gray-800">
                  <Badge
                    variant="outline"
                    className={`capitalize font-medium px-3 py-1 text-base ${
                      selectedPackage === 'premium'
                        ? 'bg-amber-50 text-amber-700 border-amber-200 dark:bg-amber-900/20 dark:text-amber-400 dark:border-amber-800/50'
                        : selectedPackage === 'standard'
                        ? 'bg-purple-50 text-purple-700 border-purple-200 dark:bg-purple-900/20 dark:text-purple-400 dark:border-purple-800/50'
                        : 'bg-blue-50 text-blue-700 border-blue-200 dark:bg-blue-900/20 dark:text-blue-400 dark:border-blue-800/50'
                    }`}
                  >
                    {selectedPackage} Package
                  </Badge>
                  <div className="flex items-center gap-2">
                    <User className="h-4 w-4 text-gray-500 dark:text-gray-400" />
                    <p className="text-sm font-medium text-gray-700 dark:text-gray-300">
                      {clientName}
                    </p>
                  </div>
                </div>

                <div className="space-y-5">
                  <div className="space-y-2">
                    <Label className="text-sm font-medium flex items-center gap-1.5">
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 text-primary" viewBox="0 0 20 20" fill="currentColor">
                        <path fillRule="evenodd" d="M5 9V7a5 5 0 0110 0v2a2 2 0 012 2v5a2 2 0 01-2 2H5a2 2 0 01-2-2v-5a2 2 0 012-2zm8-2v2H7V7a3 3 0 016 0z" clipRule="evenodd" />
                      </svg>
                      Access Token
                    </Label>
                    <div className="flex items-center gap-2">
                      <code className="bg-gray-50 dark:bg-gray-900 px-4 py-3 rounded-md text-sm font-mono flex-1 overflow-x-auto border border-gray-200 dark:border-gray-700">
                        {accessToken}
                      </code>
                      <Button
                        variant="outline"
                        size="icon"
                        onClick={handleCopyToken}
                        title="Copy token to clipboard"
                        className="h-10 w-10 border-primary/30 hover:border-primary hover:bg-primary/5 dark:border-primary/20 dark:hover:bg-primary/10"
                      >
                        <Copy className="h-4 w-4 text-primary" />
                      </Button>
                    </div>
                  </div>

                  <div className="space-y-2">
                    <Label className="text-sm font-medium flex items-center gap-1.5">
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 text-primary" viewBox="0 0 20 20" fill="currentColor">
                        <path fillRule="evenodd" d="M12.586 4.586a2 2 0 112.828 2.828l-3 3a2 2 0 01-2.828 0 1 1 0 00-1.414 1.414 4 4 0 005.656 0l3-3a4 4 0 00-5.656-5.656l-1.5 1.5a1 1 0 101.414 1.414l1.5-1.5zm-5 5a2 2 0 012.828 0 1 1 0 101.414-1.414 4 4 0 00-5.656 0l-3 3a4 4 0 105.656 5.656l1.5-1.5a1 1 0 10-1.414-1.414l-1.5 1.5a2 2 0 11-2.828-2.828l3-3z" clipRule="evenodd" />
                      </svg>
                      Questionnaire URL
                    </Label>
                    <div className="flex items-center gap-2">
                      <code className="bg-gray-50 dark:bg-gray-900 px-4 py-3 rounded-md text-sm font-mono flex-1 overflow-x-auto border border-gray-200 dark:border-gray-700">
                        {`${window.location.origin}/questionnaire/${accessToken}`}
                      </code>
                      <Button
                        variant="outline"
                        size="icon"
                        onClick={handleCopyQuestionnaireUrl}
                        title="Copy URL to clipboard"
                        className="h-10 w-10 border-primary/30 hover:border-primary hover:bg-primary/5 dark:border-primary/20 dark:hover:bg-primary/10"
                      >
                        <Copy className="h-4 w-4 text-primary" />
                      </Button>
                    </div>
                    <p className="text-xs text-muted-foreground mt-1">
                      Share this URL with your client to complete the {selectedPackage} package questionnaire.
                    </p>
                  </div>
                </div>
              </div>

              <div className="rounded-lg bg-blue-50 dark:bg-blue-900/20 p-4 text-sm text-blue-700 dark:text-blue-300 border border-blue-100 dark:border-blue-800/50 shadow-sm">
                <div className="flex items-start">
                  <div className="flex-shrink-0">
                    <Calendar className="h-5 w-5 text-blue-600 dark:text-blue-400" />
                  </div>
                  <div className="ml-3">
                    <h3 className="font-medium">Token Expiration</h3>
                    <p className="mt-1">
                      This token will expire on {tokenExpiresAt && format(tokenExpiresAt, 'MMMM d, yyyy h:mm a')}
                    </p>
                  </div>
                </div>
              </div>

              <div className="sticky bottom-0 left-0 right-0 mt-8 bg-gradient-to-t from-background via-background to-transparent pt-4 pb-4 sm:pb-6 -mx-3 sm:-mx-4 lg:-mx-6 -mb-4 sm:-mb-6 px-3 sm:px-4 lg:px-6 border-t border-gray-200 dark:border-gray-800">
                <div className="flex flex-col sm:flex-row gap-3 justify-end">
                  <Button
                    variant="outline"
                    onClick={closeTokenDialog}
                    className="border-gray-300 hover:bg-gray-100 dark:border-gray-600 dark:hover:bg-gray-800 transition-colors"
                  >
                    <XCircle className="h-4 w-4 mr-2 text-gray-500" />
                    Close
                  </Button>
                  <Button
                    variant="default"
                    onClick={() => {
                      handleCopyQuestionnaireUrl();
                      closeTokenDialog();
                    }}
                    className="bg-primary hover:bg-primary/90 transition-colors shadow-sm"
                  >
                    <Copy className="h-4 w-4 mr-2" />
                    Copy URL & Close
                  </Button>
                </div>
              </div>
            </motion.div>
          ) : (
            <form onSubmit={(e) => { e.preventDefault(); handleGenerateToken(); }} className="px-3 sm:px-4 lg:px-6 py-4 sm:py-6 space-y-6 sm:space-y-8">
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-4 sm:gap-6">
                {/* Client Information Section */}
                <div className="md:col-span-2 mb-2">
                  <div className="flex items-center gap-2 mb-4">
                    <div className="bg-blue-100 dark:bg-blue-900/30 p-1.5 rounded-full">
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 text-blue-600 dark:text-blue-400" viewBox="0 0 20 20" fill="currentColor">
                        <path fillRule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z" clipRule="evenodd" />
                      </svg>
                    </div>
                    <h3 className="font-semibold text-blue-700 dark:text-blue-400">Client Information</h3>
                  </div>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="clientName" className="text-sm font-medium">Client Name <span className="text-red-500">*</span></Label>
                  <Input
                    id="clientName"
                    value={clientName}
                    onChange={(e) => setClientName(e.target.value)}
                    placeholder="Enter client name"
                    required
                    className="border-gray-200 dark:border-gray-700 focus:border-blue-300 dark:focus:border-blue-600"
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="clientEmail" className="text-sm font-medium">Client Email <span className="text-red-500">*</span></Label>
                  <Input
                    id="clientEmail"
                    type="email"
                    value={clientEmail}
                    onChange={(e) => setClientEmail(e.target.value)}
                    placeholder="Enter client email"
                    required
                    className="border-gray-200 dark:border-gray-700 focus:border-blue-300 dark:focus:border-blue-600"
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="clientPhone" className="text-sm font-medium">Client Phone (Optional)</Label>
                  <Input
                    id="clientPhone"
                    value={clientPhone}
                    onChange={(e) => setClientPhone(e.target.value)}
                    placeholder="Enter client phone"
                    className="border-gray-200 dark:border-gray-700 focus:border-blue-300 dark:focus:border-blue-600"
                  />
                </div>

                {/* Package Selection Section */}
                <div className="md:col-span-2 mt-4 mb-2">
                  <div className="flex items-center gap-2 mb-4">
                    <div className="bg-purple-100 dark:bg-purple-900/30 p-1.5 rounded-full">
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 text-purple-600 dark:text-purple-400" viewBox="0 0 20 20" fill="currentColor">
                        <path fillRule="evenodd" d="M5 4a3 3 0 00-3 3v6a3 3 0 003 3h10a3 3 0 003-3V7a3 3 0 00-3-3H5zm-1 9v-1h5v2H5a1 1 0 01-1-1zm7 1h4a1 1 0 001-1v-1h-5v2zm0-4h5V8h-5v2zM9 8H4v2h5V8z" clipRule="evenodd" />
                      </svg>
                    </div>
                    <h3 className="font-semibold text-purple-700 dark:text-purple-400">Package Selection</h3>
                  </div>
                </div>

                <div className="md:col-span-2 space-y-3">
                  <Label htmlFor="packageType" className="text-sm font-medium">Package Type <span className="text-red-500">*</span></Label>
                  <Select value={selectedPackage} onValueChange={(value: 'basic' | 'standard' | 'premium') => setSelectedPackage(value)}>
                    <SelectTrigger id="packageType" className="border-gray-200 dark:border-gray-700 focus:border-purple-300 dark:focus:border-purple-600">
                      <SelectValue placeholder="Select package type" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="basic">
                        <div className="flex items-center gap-2">
                          <div className="w-3 h-3 rounded-full bg-blue-500"></div>
                          Basic Package
                        </div>
                      </SelectItem>
                      <SelectItem value="standard">
                        <div className="flex items-center gap-2">
                          <div className="w-3 h-3 rounded-full bg-purple-500"></div>
                          Standard Package
                        </div>
                      </SelectItem>
                      <SelectItem value="premium">
                        <div className="flex items-center gap-2">
                          <div className="w-3 h-3 rounded-full bg-amber-500"></div>
                          Premium Package
                        </div>
                      </SelectItem>
                    </SelectContent>
                  </Select>

                  <div className="grid grid-cols-1 md:grid-cols-3 gap-3 mt-3">
                    <div
                      className={`rounded-lg p-3 border transition-all duration-200 ${
                        selectedPackage === 'basic'
                          ? 'bg-blue-50 border-blue-200 dark:bg-blue-900/20 dark:border-blue-800/50 shadow-sm'
                          : 'bg-white dark:bg-gray-800 border-gray-200 dark:border-gray-700'
                      }`}
                    >
                      <div className="flex items-center gap-2 mb-1">
                        <div className="w-3 h-3 rounded-full bg-blue-500"></div>
                        <span className="font-medium text-gray-900 dark:text-gray-100">Basic</span>
                      </div>
                      <div className="text-sm font-bold text-blue-700 dark:text-blue-400">KES 75,000</div>
                      <div className="text-xs text-gray-500 dark:text-gray-400 mt-1">Deposit: KES 30,000</div>
                    </div>

                    <div
                      className={`rounded-lg p-3 border transition-all duration-200 ${
                        selectedPackage === 'standard'
                          ? 'bg-purple-50 border-purple-200 dark:bg-purple-900/20 dark:border-purple-800/50 shadow-sm'
                          : 'bg-white dark:bg-gray-800 border-gray-200 dark:border-gray-700'
                      }`}
                    >
                      <div className="flex items-center gap-2 mb-1">
                        <div className="w-3 h-3 rounded-full bg-purple-500"></div>
                        <span className="font-medium text-gray-900 dark:text-gray-100">Standard</span>
                      </div>
                      <div className="text-sm font-bold text-purple-700 dark:text-purple-400">KES 145,000</div>
                      <div className="text-xs text-gray-500 dark:text-gray-400 mt-1">Deposit: KES 58,000</div>
                    </div>

                    <div
                      className={`rounded-lg p-3 border transition-all duration-200 ${
                        selectedPackage === 'premium'
                          ? 'bg-amber-50 border-amber-200 dark:bg-amber-900/20 dark:border-amber-800/50 shadow-sm'
                          : 'bg-white dark:bg-gray-800 border-gray-200 dark:border-gray-700'
                      }`}
                    >
                      <div className="flex items-center gap-2 mb-1">
                        <div className="w-3 h-3 rounded-full bg-amber-500"></div>
                        <span className="font-medium text-gray-900 dark:text-gray-100">Premium</span>
                      </div>
                      <div className="text-sm font-bold text-amber-700 dark:text-amber-400">KES 225,000</div>
                      <div className="text-xs text-gray-500 dark:text-gray-400 mt-1">Deposit: KES 90,000</div>
                    </div>
                  </div>
                </div>

                {/* Notes Section */}
                <div className="md:col-span-2 mt-4 mb-2">
                  <div className="flex items-center gap-2 mb-4">
                    <div className="bg-green-100 dark:bg-green-900/30 p-1.5 rounded-full">
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 text-green-600 dark:text-green-400" viewBox="0 0 20 20" fill="currentColor">
                        <path fillRule="evenodd" d="M4 4a2 2 0 012-2h4.586A2 2 0 0112 2.586L15.414 6A2 2 0 0116 7.414V16a2 2 0 01-2 2H6a2 2 0 01-2-2V4zm2 6a1 1 0 011-1h6a1 1 0 110 2H7a1 1 0 01-1-1zm1 3a1 1 0 100 2h6a1 1 0 100-2H7z" clipRule="evenodd" />
                      </svg>
                    </div>
                    <h3 className="font-semibold text-green-700 dark:text-green-400">Additional Notes</h3>
                  </div>
                </div>

                <div className="space-y-2 md:col-span-2">
                  <Label htmlFor="notes" className="text-sm font-medium">Notes (Optional - for your reference only)</Label>
                  <textarea
                    id="notes"
                    value={tokenNotes}
                    onChange={(e) => setTokenNotes(e.target.value)}
                    placeholder="Add any notes about this client or project (these are not stored in the database)"
                    className="w-full min-h-[100px] px-3 py-2 rounded-md border border-gray-200 dark:border-gray-700 bg-background text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-green-300 dark:focus-visible:ring-green-600 focus-visible:ring-offset-2"
                  />
                  <p className="text-xs text-muted-foreground mt-1">
                    Note: These notes are for your reference during token creation and are not stored in the database.
                  </p>
                </div>
              </div>

              <div className="space-y-4 mt-6">
                <div className="rounded-lg bg-blue-50 dark:bg-blue-900/20 p-4 text-sm text-blue-700 dark:text-blue-300 border border-blue-100 dark:border-blue-800/50 shadow-sm">
                  <div className="flex">
                    <div className="flex-shrink-0">
                      <CheckCircle className="h-5 w-5 text-blue-600 dark:text-blue-400" />
                    </div>
                    <div className="ml-3">
                      <h3 className="font-medium">Package-Specific Questionnaire</h3>
                      <p className="mt-1">
                        The client will receive a questionnaire tailored to the selected package type.
                      </p>
                    </div>
                  </div>
                </div>
              </div>

              <div className="sticky bottom-0 left-0 right-0 mt-6 sm:mt-8 bg-gradient-to-t from-background via-background to-transparent pt-4 pb-4 sm:pb-6 px-3 sm:px-4 lg:px-6 -mx-3 sm:-mx-4 lg:-mx-6 border-t border-gray-200 dark:border-gray-800">
                <div className="flex flex-col sm:flex-row gap-3 justify-end">
                  <Button
                    type="button"
                    variant="outline"
                    onClick={closeTokenDialog}
                    disabled={isGeneratingToken}
                    className="w-full sm:w-auto border-gray-300 hover:bg-gray-100 dark:border-gray-600 dark:hover:bg-gray-800 transition-colors font-medium"
                  >
                    <XCircle className="h-4 w-4 mr-2 text-gray-500" />
                    Cancel
                  </Button>
                  <Button
                    type="submit"
                    disabled={isGeneratingToken}
                    className="w-full sm:w-auto bg-primary hover:bg-primary/90 transition-colors shadow-sm font-medium"
                  >
                    {isGeneratingToken ? (
                      <>
                        <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                        Generating...
                      </>
                    ) : (
                      <>
                        <Link className="h-4 w-4 mr-2" />
                        Generate Token
                      </>
                    )}
                  </Button>
                </div>
              </div>
            </form>
          )}
        </DialogContent>
      </Dialog>
    </div>
  );
}
