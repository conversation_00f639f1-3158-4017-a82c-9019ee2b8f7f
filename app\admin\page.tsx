"use client";

import { useEffect, useState } from "react";
import Link from "next/link";
import { useSession } from "next-auth/react";
import { FileText, Image as ImageIcon, Settings, Briefcase } from "lucide-react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { PortfolioContent, PortfolioImage } from "@/lib/cms/types";
import { useIsMobile } from "@/hooks/use-mobile";
import { getProjectRequests } from "@/app/actions/project-request-actions";

export default function AdminDashboard() {
  const { data: session } = useSession();
  const [contentCount, setContentCount] = useState<number>(0);
  const [imageCount, setImageCount] = useState<number>(0);
  const [pendingRequestsCount, setPendingRequestsCount] = useState<number>(0);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const isMobile = useIsMobile();

  useEffect(() => {
    const fetchCounts = async () => {
      try {
        // Fetch content count
        const contentRes = await fetch("/api/cms/content");
        if (contentRes.ok) {
          const contentData: PortfolioContent[] = await contentRes.json();
          setContentCount(contentData.length);
        }

        // Fetch image count
        const imageRes = await fetch("/api/cms/images");
        if (imageRes.ok) {
          const imageData: PortfolioImage[] = await imageRes.json();
          setImageCount(imageData.length);
        }

        // Fetch pending project requests count
        const projectRequestsResult = await getProjectRequests();
        if (projectRequestsResult.success && projectRequestsResult.data) {
          const pendingRequests = projectRequestsResult.data.filter(
            (request) => request.status === 'pending'
          );
          setPendingRequestsCount(pendingRequests.length);
        }
      } catch (error) {
        console.error("Error fetching dashboard data:", error);
      } finally {
        setIsLoading(false);
      }
    };

    fetchCounts();
  }, []);

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-2xl md:text-3xl font-black">Dashboard</h1>
      </div>

      <div className="grid grid-cols-2 md:grid-cols-4 gap-4 md:gap-6">
        <Link href="/admin/content" className="col-span-1">
          <Card className="hover:shadow-lg transition-all duration-200 cursor-pointer h-full border-blue-100 dark:border-blue-900/30 hover:border-blue-200 dark:hover:border-blue-800/50">
            <CardHeader className="flex flex-row items-center justify-between pb-3 p-4 md:p-6">
              <CardTitle className="text-sm font-bold text-blue-700 dark:text-blue-400">Content</CardTitle>
              <div className="bg-blue-100 dark:bg-blue-900/30 p-2 rounded-full">
                <FileText className="h-4 w-4 text-blue-600 dark:text-blue-400" />
              </div>
            </CardHeader>
            <CardContent className="p-4 md:p-6 pt-0">
              <div className="text-2xl md:text-3xl font-bold text-gray-900 dark:text-gray-100">
                {isLoading ? (
                  <div className="h-8 w-16 animate-pulse rounded bg-gray-200 dark:bg-gray-700"></div>
                ) : (
                  contentCount
                )}
              </div>
              <p className="text-xs font-semibold text-gray-600 dark:text-gray-400 mt-1">Total content entries</p>
            </CardContent>
          </Card>
        </Link>

        <Link href="/admin/images" className="col-span-1">
          <Card className="hover:shadow-lg transition-all duration-200 cursor-pointer h-full border-green-100 dark:border-green-900/30 hover:border-green-200 dark:hover:border-green-800/50">
            <CardHeader className="flex flex-row items-center justify-between pb-3 p-4 md:p-6">
              <CardTitle className="text-sm font-bold text-green-700 dark:text-green-400">Images</CardTitle>
              <div className="bg-green-100 dark:bg-green-900/30 p-2 rounded-full">
                <ImageIcon className="h-4 w-4 text-green-600 dark:text-green-400" />
              </div>
            </CardHeader>
            <CardContent className="p-4 md:p-6 pt-0">
              <div className="text-2xl md:text-3xl font-bold text-gray-900 dark:text-gray-100">
                {isLoading ? (
                  <div className="h-8 w-16 animate-pulse rounded bg-gray-200 dark:bg-gray-700"></div>
                ) : (
                  imageCount
                )}
              </div>
              <p className="text-xs font-semibold text-gray-600 dark:text-gray-400 mt-1">Total images</p>
            </CardContent>
          </Card>
        </Link>

        <Link href="/admin/project-requests" className="col-span-1">
          <Card className="hover:shadow-lg transition-all duration-200 cursor-pointer h-full border-purple-100 dark:border-purple-900/30 hover:border-purple-200 dark:hover:border-purple-800/50">
            <CardHeader className="flex flex-row items-center justify-between pb-3 p-4 md:p-6">
              <CardTitle className="text-sm font-bold text-purple-700 dark:text-purple-400">Project Requests</CardTitle>
              <div className="bg-purple-100 dark:bg-purple-900/30 p-2 rounded-full">
                <Briefcase className="h-4 w-4 text-purple-600 dark:text-purple-400" />
              </div>
            </CardHeader>
            <CardContent className="p-4 md:p-6 pt-0">
              <div className="text-2xl md:text-3xl font-bold text-gray-900 dark:text-gray-100 flex items-center">
                {isLoading ? (
                  <div className="h-8 w-16 animate-pulse rounded bg-gray-200 dark:bg-gray-700"></div>
                ) : (
                  <>
                    {pendingRequestsCount}
                    {pendingRequestsCount > 0 && (
                      <Badge variant="success" className="ml-2 text-xs font-semibold">New</Badge>
                    )}
                  </>
                )}
              </div>
              <p className="text-xs font-semibold text-gray-600 dark:text-gray-400 mt-1">Pending requests</p>
            </CardContent>
          </Card>
        </Link>

        <Link href="/admin/settings" className="col-span-1">
          <Card className="hover:shadow-lg transition-all duration-200 cursor-pointer h-full border-amber-100 dark:border-amber-900/30 hover:border-amber-200 dark:hover:border-amber-800/50">
            <CardHeader className="flex flex-row items-center justify-between pb-3 p-4 md:p-6">
              <CardTitle className="text-sm font-bold text-amber-700 dark:text-amber-400">Settings</CardTitle>
              <div className="bg-amber-100 dark:bg-amber-900/30 p-2 rounded-full">
                <Settings className="h-4 w-4 text-amber-600 dark:text-amber-400" />
              </div>
            </CardHeader>
            <CardContent className="p-4 md:p-6 pt-0">
              <div className="text-2xl md:text-3xl font-bold text-gray-900 dark:text-gray-100">
                <span>⚙️</span>
              </div>
              <p className="text-xs font-semibold text-gray-600 dark:text-gray-400 mt-1">Configure your portfolio</p>
            </CardContent>
          </Card>
        </Link>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4 md:gap-6">
        <Card className="border-blue-100 dark:border-blue-900/30 shadow-sm hover:shadow-md transition-shadow duration-200">
          <CardHeader className="p-4 md:p-6 bg-gradient-to-r from-blue-50/50 to-transparent dark:from-blue-900/10">
            <CardTitle className="text-lg md:text-xl font-bold text-blue-700 dark:text-blue-400">Welcome, {session?.user?.name || "Admin"}</CardTitle>
            <CardDescription className="font-medium">
              Manage your portfolio content and images from this dashboard.
            </CardDescription>
          </CardHeader>
          <CardContent className="p-4 md:p-6 pt-0">
            <p className="text-sm font-semibold text-gray-600 dark:text-gray-400">
              {isMobile ?
                "Use the menu at the top to navigate between different sections of the CMS." :
                "Use the sidebar to navigate between different sections of the CMS."
              }
              You can manage your content, upload images, and configure settings.
            </p>
          </CardContent>
        </Card>

        <Card className="border-green-100 dark:border-green-900/30 shadow-sm hover:shadow-md transition-shadow duration-200">
          <CardHeader className="p-4 md:p-6 bg-gradient-to-r from-green-50/50 to-transparent dark:from-green-900/10">
            <CardTitle className="text-lg md:text-xl font-bold text-green-700 dark:text-green-400">Quick Actions</CardTitle>
            <CardDescription className="font-medium">
              Common tasks you might want to perform
            </CardDescription>
          </CardHeader>
          <CardContent className="p-4 md:p-6 pt-0 space-y-3">
            <Link
              href="/admin/content/new"
              className="block rounded-lg bg-gray-100 p-3 text-sm font-semibold hover:bg-gray-200 dark:bg-gray-800 dark:hover:bg-gray-700 touch-manipulation transition-colors duration-200 border border-gray-200 dark:border-gray-700"
            >
              Add new content
            </Link>
            <Link
              href="/admin/images/upload"
              className="block rounded-lg bg-gray-100 p-3 text-sm font-semibold hover:bg-gray-200 dark:bg-gray-800 dark:hover:bg-gray-700 touch-manipulation transition-colors duration-200 border border-gray-200 dark:border-gray-700"
            >
              Upload new image
            </Link>
            <Link
              href="/"
              target="_blank"
              className="block rounded-lg bg-gray-100 p-3 text-sm font-semibold hover:bg-gray-200 dark:bg-gray-800 dark:hover:bg-gray-700 touch-manipulation transition-colors duration-200 border border-gray-200 dark:border-gray-700"
            >
              View your portfolio
            </Link>
            <Link
              href="/admin/project-requests"
              className="block rounded-lg bg-blue-100 p-3 text-sm font-semibold hover:bg-blue-200 dark:bg-blue-900 dark:hover:bg-blue-800 touch-manipulation transition-colors duration-200 border border-blue-200 dark:border-blue-800"
            >
              {pendingRequestsCount > 0 ? (
                <span className="flex items-center justify-between">
                  Manage Project Requests
                  <Badge variant="success" className="ml-2 font-semibold">
                    {pendingRequestsCount} new
                  </Badge>
                </span>
              ) : (
                "Manage Project Requests"
              )}
            </Link>
            <Link
              href="/admin/diagnostics"
              className="block rounded-lg bg-gray-100 p-3 text-sm font-semibold hover:bg-gray-200 dark:bg-gray-800 dark:hover:bg-gray-700 touch-manipulation transition-colors duration-200 border border-gray-200 dark:border-gray-700"
            >
              System Diagnostics
            </Link>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
