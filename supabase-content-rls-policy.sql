-- Enable Row Level Security on the portfolio_content table
ALTER TABLE portfolio_content ENABLE ROW LEVEL SECURITY;

-- Create a policy that allows anyone to read from the portfolio_content table
CREATE POLICY "Allow public read access to portfolio_content" 
ON portfolio_content
FOR SELECT 
USING (true);

-- Note: This policy allows anyone to read the content, but only the admin client can create, update, or delete content
-- The admin operations should be handled by the service role key
