#!/usr/bin/env node

/**
 * Security Review Script
 * Performs comprehensive security checks for deployment readiness
 */

const fs = require('fs');
const path = require('path');

class SecurityReviewer {
  constructor() {
    this.issues = [];
    this.warnings = [];
    this.passed = [];
  }

  addIssue(category, severity, message, recommendation) {
    this.issues.push({ category, severity, message, recommendation });
  }

  addWarning(category, message, recommendation) {
    this.warnings.push({ category, message, recommendation });
  }

  addPassed(category, message) {
    this.passed.push({ category, message });
  }

  checkEnvironmentVariables() {
    console.log('🔍 Checking Environment Variables...');
    
    const envFiles = ['.env.local', '.env.example', '.env.production.example'];
    
    envFiles.forEach(file => {
      const filePath = path.join(process.cwd(), file);
      if (fs.existsSync(filePath)) {
        const content = fs.readFileSync(filePath, 'utf8');
        
        // Check for exposed secrets in .env.example files
        if (file.includes('example')) {
          if (content.includes('eyJ') || content.includes('sk_') || content.includes('pk_')) {
            this.addIssue(
              'Environment Variables',
              'HIGH',
              `${file} contains what appears to be real secrets`,
              'Replace with placeholder values in example files'
            );
          } else {
            this.addPassed('Environment Variables', `${file} properly uses placeholder values`);
          }
        }
        
        // Check for weak secrets
        const lines = content.split('\n');
        lines.forEach((line, index) => {
          if (line.includes('NEXTAUTH_SECRET') && line.includes('=')) {
            const secret = line.split('=')[1]?.trim();
            if (secret && secret.length < 32) {
              this.addWarning(
                'Environment Variables',
                `NEXTAUTH_SECRET appears to be weak (line ${index + 1})`,
                'Use a strong random string of at least 32 characters'
              );
            }
          }
          
          if (line.includes('password') && line.includes('123')) {
            this.addIssue(
              'Environment Variables',
              'HIGH',
              `Weak password detected (line ${index + 1})`,
              'Use strong passwords for all accounts'
            );
          }
        });
      }
    });
    
    // Check if .env.local is in .gitignore
    const gitignorePath = path.join(process.cwd(), '.gitignore');
    if (fs.existsSync(gitignorePath)) {
      const gitignoreContent = fs.readFileSync(gitignorePath, 'utf8');
      if (gitignoreContent.includes('.env.local')) {
        this.addPassed('Environment Variables', '.env.local is properly ignored by git');
      } else {
        this.addIssue(
          'Environment Variables',
          'CRITICAL',
          '.env.local is not in .gitignore',
          'Add .env.local to .gitignore to prevent committing secrets'
        );
      }
    }
  }

  checkAPIEndpoints() {
    console.log('🔍 Checking API Endpoints...');
    
    const apiDir = path.join(process.cwd(), 'app', 'api');
    if (!fs.existsSync(apiDir)) {
      this.addWarning('API Security', 'No API directory found', 'Ensure API routes are properly secured');
      return;
    }

    const checkDirectory = (dirPath, basePath = '') => {
      const items = fs.readdirSync(dirPath);
      
      items.forEach(item => {
        const itemPath = path.join(dirPath, item);
        const relativePath = path.join(basePath, item);
        
        if (fs.statSync(itemPath).isDirectory()) {
          checkDirectory(itemPath, relativePath);
        } else if (item === 'route.ts' || item === 'route.js') {
          const content = fs.readFileSync(itemPath, 'utf8');
          
          // Check for authentication
          if (content.includes('POST') || content.includes('PUT') || content.includes('DELETE')) {
            if (!content.includes('auth') && !content.includes('session') && !content.includes('token')) {
              this.addWarning(
                'API Security',
                `${relativePath} may lack authentication`,
                'Ensure write operations require authentication'
              );
            } else {
              this.addPassed('API Security', `${relativePath} appears to have authentication checks`);
            }
          }
          
          // Check for input validation
          if (content.includes('request.json()') || content.includes('req.body')) {
            if (!content.includes('validate') && !content.includes('schema') && !content.includes('zod')) {
              this.addWarning(
                'API Security',
                `${relativePath} may lack input validation`,
                'Implement proper input validation for all user inputs'
              );
            }
          }
          
          // Check for error handling
          if (!content.includes('try') && !content.includes('catch')) {
            this.addWarning(
              'API Security',
              `${relativePath} may lack error handling`,
              'Implement proper error handling to prevent information leakage'
            );
          }
        }
      });
    };

    checkDirectory(apiDir);
  }

  checkFilePermissions() {
    console.log('🔍 Checking File Security...');
    
    const sensitiveFiles = [
      '.env.local',
      'package.json',
      'next.config.mjs',
      'middleware.ts'
    ];

    sensitiveFiles.forEach(file => {
      const filePath = path.join(process.cwd(), file);
      if (fs.existsSync(filePath)) {
        try {
          const stats = fs.statSync(filePath);
          // On Windows, file permissions work differently, so we'll just check existence
          this.addPassed('File Security', `${file} exists and is accessible`);
        } catch (error) {
          this.addWarning('File Security', `Cannot check permissions for ${file}`, 'Verify file permissions manually');
        }
      }
    });
  }

  checkDependencies() {
    console.log('🔍 Checking Dependencies...');
    
    const packageJsonPath = path.join(process.cwd(), 'package.json');
    if (!fs.existsSync(packageJsonPath)) {
      this.addIssue('Dependencies', 'HIGH', 'package.json not found', 'Ensure package.json exists');
      return;
    }

    const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'));
    const dependencies = { ...packageJson.dependencies, ...packageJson.devDependencies };
    
    // Check for known vulnerable packages (basic check)
    const potentiallyVulnerable = [
      'lodash', 'moment', 'request', 'node-uuid', 'debug'
    ];
    
    const foundVulnerable = Object.keys(dependencies).filter(dep => 
      potentiallyVulnerable.some(vuln => dep.includes(vuln))
    );
    
    if (foundVulnerable.length > 0) {
      this.addWarning(
        'Dependencies',
        `Potentially vulnerable packages found: ${foundVulnerable.join(', ')}`,
        'Run npm audit and update to secure versions'
      );
    } else {
      this.addPassed('Dependencies', 'No obviously vulnerable packages detected');
    }
    
    // Check for excessive dependencies
    const totalDeps = Object.keys(dependencies).length;
    if (totalDeps > 100) {
      this.addWarning(
        'Dependencies',
        `Large number of dependencies (${totalDeps})`,
        'Review and remove unnecessary dependencies to reduce attack surface'
      );
    }
  }

  checkSecurityHeaders() {
    console.log('🔍 Checking Security Configuration...');
    
    // Check Next.js config for security headers
    const nextConfigPath = path.join(process.cwd(), 'next.config.mjs');
    if (fs.existsSync(nextConfigPath)) {
      const content = fs.readFileSync(nextConfigPath, 'utf8');
      
      if (content.includes('headers') && content.includes('X-Frame-Options')) {
        this.addPassed('Security Headers', 'Security headers configuration found');
      } else {
        this.addWarning(
          'Security Headers',
          'No security headers configuration found',
          'Add security headers like X-Frame-Options, CSP, etc.'
        );
      }
    }
    
    // Check for middleware
    const middlewarePath = path.join(process.cwd(), 'middleware.ts');
    if (fs.existsSync(middlewarePath)) {
      this.addPassed('Security Configuration', 'Middleware file exists');
    } else {
      this.addWarning(
        'Security Configuration',
        'No middleware.ts found',
        'Consider adding middleware for additional security'
      );
    }
  }

  generateReport() {
    console.log('\n🛡️  Security Review Report');
    console.log('=' .repeat(50));
    
    const totalIssues = this.issues.length;
    const totalWarnings = this.warnings.length;
    const totalPassed = this.passed.length;
    
    console.log(`Total Checks: ${totalIssues + totalWarnings + totalPassed}`);
    console.log(`✅ Passed: ${totalPassed}`);
    console.log(`⚠️  Warnings: ${totalWarnings}`);
    console.log(`❌ Issues: ${totalIssues}`);
    
    if (this.issues.length > 0) {
      console.log('\n🚨 Critical Issues:');
      console.log('-'.repeat(50));
      this.issues.forEach((issue, index) => {
        console.log(`${index + 1}. [${issue.severity}] ${issue.category}: ${issue.message}`);
        console.log(`   💡 ${issue.recommendation}`);
        console.log('');
      });
    }
    
    if (this.warnings.length > 0) {
      console.log('\n⚠️  Warnings:');
      console.log('-'.repeat(50));
      this.warnings.forEach((warning, index) => {
        console.log(`${index + 1}. ${warning.category}: ${warning.message}`);
        console.log(`   💡 ${warning.recommendation}`);
        console.log('');
      });
    }
    
    if (this.passed.length > 0) {
      console.log('\n✅ Passed Checks:');
      console.log('-'.repeat(50));
      this.passed.forEach((pass, index) => {
        console.log(`${index + 1}. ${pass.category}: ${pass.message}`);
      });
    }
    
    console.log('\n📋 Deployment Readiness:');
    console.log('-'.repeat(50));
    
    if (totalIssues === 0) {
      console.log('✅ No critical security issues found');
    } else {
      console.log('❌ Critical issues must be resolved before deployment');
    }
    
    if (totalWarnings === 0) {
      console.log('✅ No security warnings');
    } else {
      console.log('⚠️  Consider addressing warnings for better security');
    }
    
    console.log('\n🔒 Additional Recommendations:');
    console.log('-'.repeat(50));
    console.log('• Enable HTTPS in production');
    console.log('• Set up proper CORS policies');
    console.log('• Implement rate limiting');
    console.log('• Regular security audits');
    console.log('• Monitor for security updates');
    console.log('• Use environment-specific configurations');
  }

  async runAllChecks() {
    console.log('🚀 Starting Security Review...\n');
    
    this.checkEnvironmentVariables();
    this.checkAPIEndpoints();
    this.checkFilePermissions();
    this.checkDependencies();
    this.checkSecurityHeaders();
    
    this.generateReport();
  }
}

// Run security review if called directly
if (require.main === module) {
  const reviewer = new SecurityReviewer();
  reviewer.runAllChecks().catch(console.error);
}

module.exports = SecurityReviewer;
