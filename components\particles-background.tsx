'use client';

import { useCallback, useEffect, useRef, useState } from 'react';
import { useTheme } from 'next-themes';

interface Particle {
  x: number;
  y: number;
  size: number;
  speedX: number;
  speedY: number;
  color: string;
}

export default function ParticlesBackground() {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const [dimensions, setDimensions] = useState({ width: 0, height: 0 });
  const particlesRef = useRef<Particle[]>([]);
  const animationRef = useRef<number>(0);
  const { resolvedTheme } = useTheme();
  const isDarkMode = resolvedTheme === 'dark';

  // Generate colors based on theme
  const getParticleColor = useCallback(() => {
    if (isDarkMode) {
      // Dark mode colors - blues and purples with higher opacity
      const colors = [
        'rgba(59, 130, 246, 0.6)', // blue-500
        'rgba(96, 165, 250, 0.5)', // blue-400
        'rgba(147, 197, 253, 0.4)', // blue-300
        'rgba(139, 92, 246, 0.5)', // purple-500
        'rgba(167, 139, 250, 0.4)', // purple-400
      ];
      return colors[Math.floor(Math.random() * colors.length)];
    } else {
      // Light mode colors - blues and cyans with lower opacity
      const colors = [
        'rgba(59, 130, 246, 0.3)', // blue-500
        'rgba(96, 165, 250, 0.25)', // blue-400
        'rgba(147, 197, 253, 0.2)', // blue-300
        'rgba(6, 182, 212, 0.25)', // cyan-500
        'rgba(34, 211, 238, 0.2)', // cyan-400
      ];
      return colors[Math.floor(Math.random() * colors.length)];
    }
  }, [isDarkMode]);

  // Initialize particles
  const initParticles = useCallback(() => {
    if (!canvasRef.current) return;

    const { width, height } = dimensions;
    const particles: Particle[] = [];

    // Create more particles for larger screens
    const particleCount = Math.min(Math.floor((width * height) / 9000), 100);

    for (let i = 0; i < particleCount; i++) {
      particles.push({
        x: Math.random() * width,
        y: Math.random() * height,
        size: Math.random() * 3 + 1, // Slightly larger particles (1-4px)
        speedX: (Math.random() - 0.5) * 0.8,
        speedY: (Math.random() - 0.5) * 0.8,
        color: getParticleColor(),
      });
    }

    particlesRef.current = particles;
  }, [dimensions, getParticleColor]);

  // Animation loop with performance optimization
  const animate = useCallback(() => {
    const canvas = canvasRef.current;
    const ctx = canvas?.getContext('2d');

    if (!canvas || !ctx) return;

    ctx.clearRect(0, 0, canvas.width, canvas.height);

    // Reduce animation frequency for better scrolling performance
    const now = Date.now();
    const shouldSkipFrame = now % 2 === 0; // Skip every other frame for smoother scrolling

    // Update and draw particles
    particlesRef.current.forEach((particle, i) => {
      // Update position (reduce speed for smoother effect)
      if (!shouldSkipFrame) {
        particle.x += particle.speedX * 0.8;
        particle.y += particle.speedY * 0.8;

        // Bounce off edges
        if (particle.x < 0 || particle.x > canvas.width) {
          particle.speedX *= -1;
        }

        if (particle.y < 0 || particle.y > canvas.height) {
          particle.speedY *= -1;
        }
      }

      // Draw particle
      ctx.beginPath();
      ctx.arc(particle.x, particle.y, particle.size, 0, Math.PI * 2);
      ctx.fillStyle = particle.color;
      ctx.fill();

      // Connect particles that are close to each other (less frequently)
      if (i % 2 === 0) { // Only connect every other particle for performance
        connectParticles(particle, i, ctx);
      }
    });

    animationRef.current = requestAnimationFrame(animate);
  }, []);

  // Connect nearby particles with lines
  const connectParticles = (particle: Particle, index: number, ctx: CanvasRenderingContext2D) => {
    const distance = 100; // Maximum distance to connect particles

    for (let i = index + 1; i < particlesRef.current.length; i++) {
      const otherParticle = particlesRef.current[i];
      const dx = particle.x - otherParticle.x;
      const dy = particle.y - otherParticle.y;
      const dist = Math.sqrt(dx * dx + dy * dy);

      if (dist < distance) {
        // Calculate opacity based on distance (closer = more opaque)
        const opacity = 1 - (dist / distance);

        // Draw line between particles
        ctx.beginPath();
        ctx.moveTo(particle.x, particle.y);
        ctx.lineTo(otherParticle.x, otherParticle.y);

        // Set line color based on theme
        if (isDarkMode) {
          ctx.strokeStyle = `rgba(147, 197, 253, ${opacity * 0.15})`; // blue-300 with low opacity
        } else {
          ctx.strokeStyle = `rgba(59, 130, 246, ${opacity * 0.1})`; // blue-500 with very low opacity
        }

        ctx.lineWidth = 0.5;
        ctx.stroke();
      }
    }
  };

  // Handle resize
  useEffect(() => {
    const handleResize = () => {
      if (canvasRef.current && canvasRef.current.parentElement) {
        const parent = canvasRef.current.parentElement;
        const width = parent.clientWidth;
        const height = parent.clientHeight;

        setDimensions({ width, height });

        // Update canvas size
        canvasRef.current.width = width;
        canvasRef.current.height = height;
      }
    };

    handleResize();
    window.addEventListener('resize', handleResize);

    return () => {
      window.removeEventListener('resize', handleResize);
    };
  }, []);

  // Initialize particles when dimensions or theme changes
  useEffect(() => {
    if (dimensions.width > 0 && dimensions.height > 0) {
      initParticles();
    }
  }, [dimensions, initParticles, isDarkMode]);

  // Start animation
  useEffect(() => {
    if (dimensions.width > 0 && dimensions.height > 0) {
      animationRef.current = requestAnimationFrame(animate);
    }

    return () => {
      cancelAnimationFrame(animationRef.current);
    };
  }, [animate, dimensions]);

  return (
    <canvas
      ref={canvasRef}
      className="absolute inset-0 z-0 pointer-events-none will-animate"
      style={{
        opacity: 0.8,
        transform: 'translateZ(0)', // Force hardware acceleration
        willChange: 'transform' // Optimize for animations
      }}
    />
  );
}
