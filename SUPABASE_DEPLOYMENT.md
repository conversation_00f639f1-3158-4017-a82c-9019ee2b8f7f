# Supabase Edge Function Deployment Guide

This guide will help you deploy the Supabase Edge Function for sending email notifications when someone submits the contact form on your portfolio website.

## Prerequisites

1. Sign up for a [Resend](https://resend.com) account to get an API key for sending emails.
2. Install the Supabase CLI locally (this is done automatically when you run `npm install` since it's added as a dev dependency).

## Deployment Steps

### 1. Install Dependencies

```bash
npm install
```

### 2. Login to Supabase

```bash
npm run supabase:login
```

This will open a browser window where you need to authenticate with your Supabase account.

### 3. Link Your Project

```bash
npm run supabase:link
```

This will link your local project to your Supabase project.

### 4. Set Environment Variables

Set the required environment variables for the Edge Function:

```bash
npx supabase secrets set RESEND_API_KEY=your_resend_api_key --project-ref caogszaytzuiqwwkbhhi
npx supabase secrets set RECIPIENT_EMAIL=<EMAIL> --project-ref caog<PERSON>tzuiqwwkbhhi
```

Replace `your_resend_api_key` with your actual Resend API key.

### 5. Deploy the Edge Function

```bash
npm run supabase:deploy
```

This will deploy the `send-contact-email` Edge Function to your Supabase project.

## Testing

You can test the Edge Function by submitting the contact form on your portfolio website. You should receive an email notification at the email address you specified in the `RECIPIENT_EMAIL` environment variable.

## Local Development

To test the Edge Function locally:

```bash
npm run supabase:serve
```

This will start a local server for the Edge Function. You can then send a POST request to `http://localhost:54321/functions/v1/send-contact-email` with a JSON body:

```json
{
  "name": "Test User",
  "email": "<EMAIL>",
  "subject": "Test Subject",
  "message": "This is a test message."
}
```

## Troubleshooting

If you encounter any issues:

1. Check the Supabase dashboard for logs of the Edge Function.
2. Make sure the environment variables are set correctly.
3. Verify that your Resend API key is valid.
4. Check that the Edge Function has been deployed successfully.

## Resources

- [Supabase Edge Functions Documentation](https://supabase.com/docs/guides/functions)
- [Resend Documentation](https://resend.com/docs)
