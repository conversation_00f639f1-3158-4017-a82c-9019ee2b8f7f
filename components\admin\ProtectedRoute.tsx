"use client";

import { useEffect, useState } from "react";
import { useSession } from "next-auth/react";
import { useRouter } from "next/navigation";
import { toast } from "sonner";

interface ProtectedRouteProps {
  children: React.ReactNode;
  fallback?: React.ReactNode;
  redirectTo?: string;
}

export function ProtectedRoute({ 
  children, 
  fallback,
  redirectTo = "/admin/login" 
}: ProtectedRouteProps) {
  const { data: session, status } = useSession();
  const router = useRouter();
  const [isAuthorized, setIsAuthorized] = useState(false);

  useEffect(() => {
    if (status === "loading") {
      return; // Still loading
    }

    if (status === "unauthenticated") {
      toast.error("Please log in to access this page");
      router.push(redirectTo);
      return;
    }

    if (status === "authenticated") {
      // Additional session validation
      if (session?.expires) {
        const expiryTime = new Date(session.expires).getTime();
        const currentTime = new Date().getTime();
        
        if (currentTime >= expiryTime) {
          toast.error("Your session has expired. Please log in again.");
          router.push(redirectTo);
          return;
        }
      }
      
      setIsAuthorized(true);
    }
  }, [status, session, router, redirectTo]);

  // Show loading state
  if (status === "loading" || (status === "authenticated" && !isAuthorized)) {
    return fallback || (
      <div className="flex h-screen w-full items-center justify-center bg-gray-50 dark:bg-gray-900">
        <div className="text-center">
          <div className="h-8 w-8 animate-spin rounded-full border-b-2 border-t-2 border-blue-500 mx-auto mb-4"></div>
          <p className="text-sm text-gray-600 dark:text-gray-400 font-medium">
            Verifying access...
          </p>
        </div>
      </div>
    );
  }

  // Show unauthorized state
  if (status === "unauthenticated") {
    return fallback || (
      <div className="flex h-screen w-full items-center justify-center bg-gray-50 dark:bg-gray-900">
        <div className="text-center">
          <p className="text-sm text-red-600 dark:text-red-400 font-medium">
            Unauthorized access. Redirecting to login...
          </p>
        </div>
      </div>
    );
  }

  // Render protected content
  return <>{children}</>;
}
