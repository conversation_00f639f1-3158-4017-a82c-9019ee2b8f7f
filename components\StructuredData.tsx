import Script from 'next/script'

interface StructuredDataProps {
  title?: string
  description?: string
  url?: string
}

export default function StructuredData({ 
  title = "<PERSON> Portfolio",
  description = "Personal portfolio website of <PERSON>",
  url = "https://zacharyodero.com"
}: StructuredDataProps) {
  const structuredData = {
    "@context": "https://schema.org",
    "@type": "Person",
    "name": "<PERSON>der<PERSON>",
    "jobTitle": "Web Developer",
    "description": description,
    "url": url,
    "sameAs": [
      "https://twitter.com/ZacharyOde83974",
      "https://github.com/ZacharyOdero"
    ],
    "knowsAbout": [
      "Web Development",
      "Full Stack Development",
      "React",
      "Next.js",
      "TypeScript",
      "JavaScript"
    ],
    "mainEntityOfPage": {
      "@type": "WebPage",
      "@id": url
    },
    "image": {
      "@type": "ImageObject",
      "url": `${url}/icon-512x512.png`,
      "width": 512,
      "height": 512
    }
  }

  return (
    <Script
      id="structured-data"
      type="application/ld+json"
      dangerouslySetInnerHTML={{
        __html: JSON.stringify(structuredData)
      }}
    />
  )
}
