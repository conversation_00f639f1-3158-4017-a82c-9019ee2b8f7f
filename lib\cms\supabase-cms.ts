import { createClient } from '@supabase/supabase-js';
import {
  PortfolioContent,
  PortfolioImage,
  CreateContentPayload,
  UpdateContentPayload,
  CreateImagePayload,
  UpdateImagePayload,
  ContentSection,
  ContentKey,
  ImageType
} from './types';

// Create a single supabase client for interacting with your database
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || '';
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || '';
const supabase = createClient(supabaseUrl, supabaseAnonKey);

// Create an admin client with service role for operations that need to bypass RLS
const supabaseAdmin = process.env.SUPABASE_SERVICE_ROLE_KEY
  ? createClient(supabaseUrl, process.env.SUPABASE_SERVICE_ROLE_KEY)
  : supabase; // Fallback to regular client if no service role key

// Content Management
export async function getAllContent(): Promise<PortfolioContent[]> {
  try {
    const { data, error } = await supabase
      .from('portfolio_content')
      .select('*')
      .order('section')
      .order('key');

    if (error) {
      console.error('Error fetching content:', error);
      return []; // Return empty array instead of throwing
    }

    return data || [];
  } catch (err) {
    console.error('Unexpected error fetching all content:', err);
    return []; // Return empty array for any unexpected errors
  }
}

export async function getContentBySection(section: ContentSection): Promise<PortfolioContent[]> {
  try {
    const { data, error } = await supabase
      .from('portfolio_content')
      .select('*')
      .eq('section', section)
      .order('key');

    if (error) {
      console.error(`Error fetching content for section ${section}:`, error);
      return []; // Return empty array instead of throwing
    }

    return data || [];
  } catch (err) {
    console.error(`Unexpected error fetching content for section ${section}:`, err);
    return []; // Return empty array for any unexpected errors
  }
}

export async function getContentBySectionAndKey(
  section: ContentSection,
  key: ContentKey
): Promise<PortfolioContent | null> {
  try {
    // Check if Supabase URL and key are available
    if (!supabaseUrl || !supabaseAnonKey) {
      console.warn(`Missing Supabase credentials for fetching ${section}.${key}`);
      return null;
    }

    const { data, error } = await supabase
      .from('portfolio_content')
      .select('*')
      .eq('section', section)
      .eq('key', key)
      .single();

    if (error) {
      if (error.code === 'PGRST116') { // PGRST116 is "no rows returned"
        console.log(`No content found for ${section}.${key}`);
        return null;
      }
      console.error(`Error fetching content for ${section}.${key}:`, error);
      return null; // Return null instead of throwing
    }

    return data || null;
  } catch (err) {
    console.error(`Unexpected error fetching content for ${section}.${key}:`, err);
    return null; // Return null for any unexpected errors
  }
}

export async function getContentById(id: string): Promise<PortfolioContent | null> {
  const { data, error } = await supabase
    .from('portfolio_content')
    .select('*')
    .eq('id', id)
    .single();

  if (error && error.code !== 'PGRST116') { // PGRST116 is "no rows returned"
    console.error(`Error fetching content with id ${id}:`, error);
    throw new Error(`Failed to fetch content with id ${id}: ${error.message}`);
  }

  return data || null;
}

export async function createContent(payload: CreateContentPayload): Promise<PortfolioContent> {
  // Use admin client to bypass RLS
  const { data, error } = await supabaseAdmin
    .from('portfolio_content')
    .insert([payload])
    .select()
    .single();

  if (error) {
    console.error('Error creating content:', error);
    throw new Error(`Failed to create content: ${error.message}`);
  }

  return data;
}

export async function updateContent(
  id: string,
  payload: UpdateContentPayload
): Promise<PortfolioContent> {
  // Use admin client to bypass RLS
  const { data, error } = await supabaseAdmin
    .from('portfolio_content')
    .update(payload)
    .eq('id', id)
    .select()
    .single();

  if (error) {
    console.error(`Error updating content with id ${id}:`, error);
    throw new Error(`Failed to update content: ${error.message}`);
  }

  return data;
}

export async function deleteContent(id: string): Promise<void> {
  // Use admin client to bypass RLS
  const { error } = await supabaseAdmin
    .from('portfolio_content')
    .delete()
    .eq('id', id);

  if (error) {
    console.error(`Error deleting content with id ${id}:`, error);
    throw new Error(`Failed to delete content: ${error.message}`);
  }
}

// Image Management
export async function getAllImages(): Promise<PortfolioImage[]> {
  // Use admin client to bypass RLS
  const { data, error } = await supabaseAdmin
    .from('portfolio_images')
    .select('*')
    .order('created_at', { ascending: false });

  if (error) {
    console.error('Error fetching images:', error);
    throw new Error(`Failed to fetch images: ${error.message}`);
  }

  return data || [];
}

export async function getImagesByType(type: ImageType): Promise<PortfolioImage[]> {
  // Use admin client to bypass RLS
  const { data, error } = await supabaseAdmin
    .from('portfolio_images')
    .select('*')
    .eq('type', type)
    .order('created_at', { ascending: false });

  if (error) {
    console.error(`Error fetching images of type ${type}:`, error);
    throw new Error(`Failed to fetch images of type ${type}: ${error.message}`);
  }

  return data || [];
}

export async function getImageById(id: string): Promise<PortfolioImage | null> {
  try {
    console.log(`getImageById: Fetching image with ID ${id}`);

    if (!id || typeof id !== 'string') {
      console.error(`getImageById: Invalid ID provided: ${id}`);
      throw new Error('Invalid image ID provided');
    }

    // Use admin client to bypass RLS
    const { data, error } = await supabaseAdmin
      .from('portfolio_images')
      .select('*')
      .eq('id', id)
      .single();

    if (error) {
      if (error.code === 'PGRST116') {
        // PGRST116 is "no rows returned"
        console.log(`getImageById: No image found with ID ${id}`);
        return null;
      }

      console.error(`getImageById: Error fetching image with id ${id}:`, error);
      throw new Error(`Failed to fetch image: ${error.message}`);
    }

    if (!data) {
      console.log(`getImageById: No data returned for image with ID ${id}`);
      return null;
    }

    console.log(`getImageById: Successfully retrieved image: ${data.name}`);
    return data;
  } catch (error: any) {
    console.error(`getImageById: Unexpected error:`, error);
    throw new Error(`Failed to fetch image: ${error.message}`);
  }
}

export async function uploadImage(
  file: File,
  path: string
): Promise<{ path: string; url: string }> {
  console.log('Uploading file to path:', path);
  console.log('File details:', file.name, file.type, file.size);

  try {
    // Convert File to ArrayBuffer for more reliable upload
    const arrayBuffer = await file.arrayBuffer();

    // Use admin client to bypass RLS
    const { data, error } = await supabaseAdmin.storage
      .from('portfolio-images')
      .upload(path, arrayBuffer, {
        cacheControl: '3600',
        upsert: true, // Changed to true to overwrite if file exists
        contentType: file.type // Explicitly set content type
      });

    if (error) {
      console.error('Error uploading image:', error);
      throw new Error(`Failed to upload image: ${error.message}`);
    }

    if (!data) {
      throw new Error('No data returned from upload');
    }

    const { data: urlData } = supabaseAdmin.storage
      .from('portfolio-images')
      .getPublicUrl(data.path);

    console.log('Upload successful, public URL:', urlData.publicUrl);

    return {
      path: data.path,
      url: urlData.publicUrl
    };
  } catch (error: any) {
    console.error('Exception during image upload:', error);
    throw new Error(`Upload failed: ${error.message}`);
  }
}

export async function createImage(payload: CreateImagePayload): Promise<PortfolioImage> {
  const timestamp = new Date().getTime();
  const fileExt = payload.file.name.split('.').pop();
  const filePath = `${payload.type}/${payload.name.replace(/\s+/g, '-').toLowerCase()}-${timestamp}.${fileExt}`;

  const { path, url } = await uploadImage(payload.file, filePath);

  const imageData = {
    name: payload.name,
    description: payload.description || null,
    alt_text: payload.alt_text,
    storage_path: path,
    public_url: url,
    type: payload.type
  };

  // Use admin client to bypass RLS
  const { data, error } = await supabaseAdmin
    .from('portfolio_images')
    .insert([imageData])
    .select()
    .single();

  if (error) {
    console.error('Error creating image record:', error);
    throw new Error(`Failed to create image record: ${error.message}`);
  }

  return data;
}

export async function updateImage(
  id: string,
  payload: UpdateImagePayload
): Promise<PortfolioImage> {
  try {
    console.log(`Updating image with id ${id}`);

    const currentImage = await getImageById(id);
    if (!currentImage) {
      throw new Error(`Image with id ${id} not found`);
    }

    let imageData: Partial<PortfolioImage> = {
      name: payload.name || currentImage.name,
      description: payload.description !== undefined ? payload.description : currentImage.description,
      alt_text: payload.alt_text || currentImage.alt_text,
      type: payload.type || currentImage.type
    };

    // If a new file is provided, upload it and update the paths
    if (payload.file) {
      console.log(`Updating file for image ${id}:`, payload.file.name, payload.file.type, payload.file.size);

      try {
        const timestamp = new Date().getTime();
        const fileExt = payload.file.name.split('.').pop();
        const filePath = `${imageData.type}/${imageData.name.replace(/\s+/g, '-').toLowerCase()}-${timestamp}.${fileExt}`;

        const { path, url } = await uploadImage(payload.file, filePath);
        console.log(`New file uploaded to ${path}`);

        // Delete the old file using admin client
        try {
          const { error: removeError } = await supabaseAdmin.storage
            .from('portfolio-images')
            .remove([currentImage.storage_path]);

          if (removeError) {
            console.warn(`Warning: Could not remove old file ${currentImage.storage_path}:`, removeError);
            // Continue anyway - this is not critical
          } else {
            console.log(`Old file ${currentImage.storage_path} removed successfully`);
          }
        } catch (removeError) {
          console.warn(`Exception removing old file ${currentImage.storage_path}:`, removeError);
          // Continue anyway - this is not critical
        }

        imageData = {
          ...imageData,
          storage_path: path,
          public_url: url
        };
      } catch (uploadError: any) {
        console.error(`Error uploading new file for image ${id}:`, uploadError);
        throw new Error(`Failed to upload new image file: ${uploadError.message}`);
      }
    }

    // Use admin client to bypass RLS
    const { data, error } = await supabaseAdmin
      .from('portfolio_images')
      .update(imageData)
      .eq('id', id)
      .select()
      .single();

    if (error) {
      console.error(`Error updating image record with id ${id}:`, error);
      throw new Error(`Failed to update image record: ${error.message}`);
    }

    console.log(`Image ${id} updated successfully`);
    return data;
  } catch (error: any) {
    console.error(`Exception in updateImage for id ${id}:`, error);
    throw error; // Re-throw to be handled by the caller
  }
}

export async function deleteImage(id: string): Promise<void> {
  const image = await getImageById(id);
  if (!image) {
    throw new Error(`Image with id ${id} not found`);
  }

  // Delete the file from storage using admin client
  const { error: storageError } = await supabaseAdmin.storage
    .from('portfolio-images')
    .remove([image.storage_path]);

  if (storageError) {
    console.error(`Error deleting image file for id ${id}:`, storageError);
    throw new Error(`Failed to delete image file: ${storageError.message}`);
  }

  // Delete the database record using admin client
  const { error } = await supabaseAdmin
    .from('portfolio_images')
    .delete()
    .eq('id', id);

  if (error) {
    console.error(`Error deleting image record with id ${id}:`, error);
    throw new Error(`Failed to delete image record: ${error.message}`);
  }
}
