'use client';

import { motion } from 'framer-motion';
import { But<PERSON> } from '@/components/ui/button';
import { CheckCircle, Calendar, Mail, ArrowRight, Home } from 'lucide-react';
import Link from 'next/link';
import { Card, CardContent } from '@/components/ui/card';

interface QuestionnaireCompleteProps {
  clientName: string;
  projectName: string;
}

export default function QuestionnaireComplete({
  clientName,
  projectName
}: QuestionnaireCompleteProps) {
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
      className="w-full max-w-3xl mx-auto px-4 sm:px-6"
    >
      {/* Success Header with Animation */}
      <div className="text-center mb-10 relative">
        <div className="flex justify-center items-center h-32 mb-6 relative">
          <motion.div
            initial={{ scale: 0.8, opacity: 0 }}
            animate={{ scale: [1, 1.1, 1], opacity: [0.5, 0.8, 0.5] }}
            transition={{
              duration: 3,
              repeat: Infinity,
              repeatType: "reverse"
            }}
            className="absolute w-24 h-24 sm:w-32 sm:h-32 rounded-full bg-green-100 dark:bg-green-900/20"
          />

          <motion.div
            initial={{ scale: 0, rotate: -10 }}
            animate={{ scale: 1, rotate: 0 }}
            transition={{
              delay: 0.2,
              type: 'spring',
              stiffness: 200,
              damping: 15
            }}
            className="relative z-10"
          >
            <div className="absolute inset-0 bg-green-200 dark:bg-green-800/30 rounded-full blur-md opacity-70 scale-110" />
            <div className="relative bg-white dark:bg-gray-800 rounded-full p-3 sm:p-4 shadow-md">
              <CheckCircle className="h-12 w-12 sm:h-16 sm:w-16 text-green-500" />
            </div>
          </motion.div>
        </div>

        <motion.h2
          initial={{ opacity: 0, y: 10 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.3, duration: 0.5 }}
          className="text-2xl sm:text-3xl font-bold mb-3 bg-clip-text text-transparent bg-gradient-to-r from-green-500 to-blue-500"
        >
          Questionnaire Completed!
        </motion.h2>

        <motion.p
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 0.4, duration: 0.5 }}
          className="text-base sm:text-lg text-muted-foreground max-w-md mx-auto"
        >
          Thank you for taking the time to complete the project questionnaire.
        </motion.p>
      </div>

      {/* Next Steps Card */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.5, duration: 0.5 }}
      >
        <Card className="mb-6 overflow-hidden border-0 shadow-lg">
          <div className="h-2 bg-gradient-to-r from-green-400 via-blue-500 to-purple-500" />
          <CardContent className="p-6">
            <h3 className="text-xl font-semibold mb-6 flex items-center">
              <span className="bg-primary/10 p-2 rounded-full mr-3">
                <ArrowRight className="h-5 w-5 text-primary" />
              </span>
              What Happens Next
            </h3>

            <div className="space-y-6">
              {/* Timeline Steps */}
              <div className="space-y-8">
                <div className="flex items-start">
                  <div className="flex-shrink-0 mr-4">
                    <div className="w-8 h-8 rounded-full bg-green-100 dark:bg-green-900/30 flex items-center justify-center">
                      <div className="w-4 h-4 rounded-full bg-green-500 border-2 border-white dark:border-gray-800" />
                    </div>
                  </div>
                  <div>
                    <h4 className="font-semibold text-base">Project Review</h4>
                    <p className="text-sm text-muted-foreground mt-1">
                      I'll review your questionnaire responses and may reach out with additional questions if needed.
                    </p>
                  </div>
                </div>

                <div className="flex items-start">
                  <div className="flex-shrink-0 mr-4">
                    <div className="w-8 h-8 rounded-full bg-blue-100 dark:bg-blue-900/30 flex items-center justify-center">
                      <div className="w-4 h-4 rounded-full bg-blue-500 border-2 border-white dark:border-gray-800" />
                    </div>
                  </div>
                  <div>
                    <h4 className="font-semibold text-base">Project Planning</h4>
                    <p className="text-sm text-muted-foreground mt-1">
                      Based on your responses, I'll create a detailed project plan and timeline.
                    </p>
                  </div>
                </div>

                <div className="flex items-start">
                  <div className="flex-shrink-0 mr-4">
                    <div className="w-8 h-8 rounded-full bg-purple-100 dark:bg-purple-900/30 flex items-center justify-center">
                      <div className="w-4 h-4 rounded-full bg-purple-500 border-2 border-white dark:border-gray-800" />
                    </div>
                  </div>
                  <div>
                    <h4 className="font-semibold text-base">Development Kickoff</h4>
                    <p className="text-sm text-muted-foreground mt-1">
                      Once the plan is approved, development will begin according to the agreed timeline.
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </motion.div>

      {/* Contact Information Card */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.7, duration: 0.5 }}
      >
        <Card className="mb-8 overflow-hidden border border-blue-100 dark:border-blue-900/30 shadow-md">
          <div className="bg-gradient-to-r from-blue-50 to-blue-100 dark:from-blue-900/20 dark:to-blue-800/20 p-6">
            <div className="flex items-center mb-4">
              <div className="bg-blue-100 dark:bg-blue-800/50 p-2 rounded-full mr-3">
                <Mail className="h-5 w-5 text-blue-600 dark:text-blue-400" />
              </div>
              <h3 className="text-xl font-semibold text-blue-700 dark:text-blue-400">Stay Connected</h3>
            </div>

            <p className="text-sm text-blue-700 dark:text-blue-300 mb-4">
              You'll receive a confirmation email with a summary of your responses and next steps.
            </p>

            <div className="bg-white dark:bg-gray-800/50 rounded-lg p-4 shadow-sm border border-blue-100 dark:border-blue-900/30">
              <p className="text-sm">
                If you have any questions or need to provide additional information, please contact me at{' '}
                <a
                  href="mailto:<EMAIL>"
                  className="font-medium text-blue-600 dark:text-blue-400 hover:underline"
                >
                  <EMAIL>
                </a>
              </p>
            </div>
          </div>
        </Card>
      </motion.div>

      {/* Thank You Message and Button */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.9, duration: 0.5 }}
        className="text-center mb-10"
      >
        <div className="bg-gray-50 dark:bg-gray-800/50 rounded-lg p-4 sm:p-6 mb-6 shadow-sm">
          <p className="text-muted-foreground">
            Thank you for choosing me for your web development project,
            <span className="font-medium text-foreground"> {clientName}</span>!
            <br className="hidden sm:block" />
            <span className="sm:hidden"> </span>
            I'm excited to work on <span className="font-medium text-foreground">{projectName}</span> with you.
          </p>
        </div>

        <Link href="/" passHref>
          <Button
            size="lg"
            className="bg-gradient-to-r from-primary to-primary/80 hover:from-primary/90 hover:to-primary shadow-md hover:shadow-lg transition-all duration-300 w-full sm:w-auto"
          >
            <Home className="mr-2 h-4 w-4" />
            Return to Homepage
          </Button>
        </Link>
      </motion.div>
    </motion.div>
  );
}
