ZACHARY ODERO
Computer Science Student | Full-Stack Web Developer | AI Enthusiast

CONTACT INFORMATION
Email: <EMAIL>
Phone: +254 796 564 593
LinkedIn: www.linkedin.com/in/zacharyodero
GitHub: github.com/zacharyodero
Location: Maseno University, Kisumu, Kenya
Website: zachweb.dev

PROFESSIONAL SUMMARY
Ambitious and driven third-year Computer Science student at Maseno University with hands-on experience in full-stack web development and a growing expertise in AI technologies. Passionate about creating modern web applications using Next.js and Django, with a keen interest in leveraging artificial intelligence to solve real-world problems. Active member of Google Developer's Club with experience in building scalable web applications and a commitment to continuous learning in emerging technologies.

EDUCATION
Bachelor of Science in Computer Science
Maseno University, Kisumu, Kenya
Expected Graduation: 2027
Current Year: Third Year
Relevant Coursework: Data Structures & Algorithms, Software Engineering, Database Systems, Web Development, Object-Oriented Programming, Artificial Intelligence Fundamentals

TECHNICAL SKILLS
Programming Languages: Python, JavaScript, HTML/CSS, SQL
Web Development: Next.js, React, Django, Node.js, Tailwind CSS
Databases: Supabase, PostgreSQL, SQLite
Tools & Platforms: Git/GitHub, VS Code, Vercel, Railway
Cloud Services: Supabase, Vercel deployment, Railway hosting
Emerging Technologies: AI/Machine Learning basics, OpenAI API integration
Other: REST APIs, JSON, Responsive Design, Modern Web Standards

PROJECTS

Professional Portfolio Website
Personal Project | zachweb.dev | January 2025 - March 2025
Designed and developed a modern, responsive portfolio website showcasing technical skills and projects, demonstrating proficiency in contemporary web development practices.

Technical Implementation:
• Built using Next.js with TypeScript for optimal performance and type safety
• Implemented responsive design with Tailwind CSS for seamless cross-device experience
• Integrated modern animations and interactive elements for enhanced user engagement
• Optimized for SEO and accessibility following web standards best practices
• Deployed on Vercel with continuous integration and deployment pipeline

Key Features:
• Dynamic project showcase with detailed case studies
• Contact form with email integration
• Dark/light theme toggle for improved user experience
• Mobile-first responsive design approach
• Fast loading times with Next.js optimization features

Local Business Job Finder Web Application
Academic Project | January 2025 - March 2025 | In Development
Currently developing a comprehensive web application to connect local job seekers with businesses in the community, addressing employment challenges in the local market.

Technical Stack:
• Backend developed using Django with Python for robust server-side functionality
• Supabase integration for real-time database management and authentication
• RESTful API design for scalable data operations
• Responsive frontend design for optimal user experience across devices

Planned Features:
• Advanced job search and filtering system with location-based recommendations
• Employer dashboard for job posting and application management
• User authentication and profile management system
• Real-time notifications for job matches and application updates
• Integration with local business directories

Current Status: Backend development 80% complete, preparing for deployment phase

LEADERSHIP & EXTRACURRICULAR ACTIVITIES

Google Developer's Club (GDC) - Active Member
Maseno University Chapter | September 2023 - Present

• Participate in weekly coding workshops and technical sessions focusing on modern web technologies
• Collaborate on community tech projects and participate in coding challenges
• Engage in peer learning sessions on emerging technologies including AI and machine learning
• Contribute to knowledge sharing through group discussions and collaborative problem-solving

Key Contributions:
• Actively participate in campus coding bootcamps and technical workshops
• Contribute to solution design sessions for local community tech challenges
• Share knowledge and insights with fellow members on web development best practices

CERTIFICATIONS & TRAINING
• Google Developer Student Clubs - Active Member since September 2023
• Next.js Development - Self-directed learning and practical application
• Django Web Framework - Academic and personal project implementation
• Supabase Database Management - Modern database solutions and real-time functionality

ACHIEVEMENTS & RECOGNITION
• Academic Excellence: Consistent performance in Computer Science coursework with focus on practical application
• Technical Portfolio: Successfully developed and deployed professional portfolio website
• Project Development: Currently developing full-stack web application addressing local employment challenges
• Community Engagement: Active participation in Google Developer's Club technical initiatives

INTERESTS & PERSONAL PROJECTS
Technology Interests: Full-Stack Web Development, Artificial Intelligence & Machine Learning, Modern JavaScript Frameworks, Database Design
Ongoing Learning: Exploring AI integration in web applications, staying current with Next.js and Django ecosystem updates
Community Engagement: Active participation in tech communities and continuous skill development through practical projects

REFERENCES
Jack Oraro
Google Developer's Club Leader & Senior Computer Science Student
Maseno University

Leadership Roles:

Android Team Lead
Google Developer Students Club (GDSC), KIIT
• Organized and led Android development study jams and hackathons
• Mentored and supported students in building Android applications and understanding mobile development frameworks

Project Leader – DecisionHub & Podstream
• Led a team of 4 developers in building DecisionHub, a rule-based decision-making platform for banks
• Directed frontend and backend integration, version control systems, and AI-enhanced rule creation
• Spearheaded Podstream, a podcast streaming platform, enhancing productivity through effective task delegation and project timelines

Founder – JSdynasty Programming School
• Established a tech learning platform serving both Maseno University students and an online audience
• Created and shared educational resources, coding sessions, and real-world project mentorship

Hackathon Leadership & Recognition:
• 1st Place – Corridor Platform Hackathon (2024): Outperformed 50 teams with innovative project delivery
• 1st Place – Flipr Hackathon 17 (2023): Led a team to win out of 200+ teams, showcasing full-stack and DevOps proficiency

Contact Information:
Email: <EMAIL>
Phone: +254 713 584 267
GitHub: https://github.com/orarojack

Available for internships, part-time opportunities, and collaborative projects. Particularly interested in roles involving full-stack web development, AI integration, and innovative technology solutions.
