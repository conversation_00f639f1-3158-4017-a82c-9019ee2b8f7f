// This script creates a test client project and generates a questionnaire access token
// Run with: node scripts/create-test-project.js

const { createClient } = require('@supabase/supabase-js');
const fs = require('fs');
const path = require('path');

// Read environment variables from .env.local
const envPath = path.resolve(process.cwd(), '.env.local');
const envContent = fs.readFileSync(envPath, 'utf8');
const envVars = envContent.split('\n').reduce((acc, line) => {
  const match = line.match(/^([^#=]+)=(.*)$/);
  if (match) {
    acc[match[1].trim()] = match[2].trim();
  }
  return acc;
}, {});

// Supabase configuration
const supabaseUrl = envVars.NEXT_PUBLIC_SUPABASE_URL || 'https://caogszaytzuiqwwkbhhi.supabase.co';
const supabaseKey = envVars.SUPABASE_SERVICE_ROLE_KEY || envVars.NEXT_PUBLIC_SUPABASE_ANON_KEY;

if (!supabaseKey) {
  console.error('Error: Could not find Supabase key in .env.local file');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey);

// Generate a reference code
function generateReferenceCode() {
  const prefix = 'ZO';
  const timestamp = Date.now().toString().slice(-6);
  const random = Math.floor(Math.random() * 1000).toString().padStart(3, '0');
  return `${prefix}${timestamp}${random}`;
}

// Create a test client project
async function createTestProject() {
  try {
    // Create client project
    const referenceCode = generateReferenceCode();
    const { data: projectData, error: projectError } = await supabase
      .from('client_projects')
      .insert([{
        client_name: 'Test Client',
        client_email: '<EMAIL>',
        client_phone: '0712345678',
        reference_code: referenceCode,
        package_type: 'basic',
        total_amount: 75000,
        deposit_amount: 30000,
        status: 'verified'
      }])
      .select()
      .single();

    if (projectError) {
      console.error('Error creating client project:', projectError);
      return;
    }

    console.log('Client project created successfully:', projectData);

    // Generate questionnaire access token
    const expiresAt = new Date();
    expiresAt.setDate(expiresAt.getDate() + 7); // Token expires in 7 days

    const accessToken = Array.from(
      { length: 32 },
      () => Math.floor(Math.random() * 36).toString(36)
    ).join('');

    const { data: accessData, error: accessError } = await supabase
      .from('questionnaire_access')
      .insert([{
        client_project_id: projectData.id,
        access_token: accessToken,
        expires_at: expiresAt.toISOString()
      }])
      .select()
      .single();

    if (accessError) {
      console.error('Error generating questionnaire access:', accessError);
      return;
    }

    console.log('Questionnaire access generated successfully:', accessData);
    console.log('\nTest the questionnaire with this URL:');
    console.log(`http://localhost:3000/questionnaire/${accessToken}`);
  } catch (error) {
    console.error('Unexpected error:', error);
  }
}

// Run the function
createTestProject();
