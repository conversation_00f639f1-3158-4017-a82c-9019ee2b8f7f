import { PackageType } from '@/lib/mpesa/types';

// Interface for project request notification data
export interface ProjectRequestNotificationData {
  client_name: string;
  client_email: string;
  client_phone: string;
  package_type: PackageType;
  additional_services?: string[];
  maintenance_plan?: string | null;
  total_amount: number;
  reference_code: string;
  created_at: string;
}

// Interface for questionnaire completion notification data
export interface QuestionnaireCompletionNotificationData {
  client_name: string;
  client_email: string;
  client_phone: string;
  package_type: PackageType;
  reference_code: string;
  questionnaire_id: string;
  completed_at: string;
  // General information
  project_name?: string;
  project_description?: string;
  target_audience?: string;
  project_goals?: string[];
  color_scheme?: string;
  has_logo_or_brand_guidelines?: boolean;
  preferred_launch_timeline?: string;
  // Package-specific data (will vary based on package type)
  package_data?: any;
}

// Function to send project request notification
export async function sendProjectRequestNotification(
  data: ProjectRequestNotificationData
): Promise<{ success: boolean; error?: string }> {
  try {
    const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || '';
    const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || '';

    if (!supabaseUrl || !supabaseAnonKey) {
      console.error('Missing Supabase URL or anon key');
      return { success: false, error: 'Server configuration error' };
    }

    console.log('Sending project request notification email');

    const edgeFunctionUrl = `${supabaseUrl}/functions/v1/send-project-request-notification`;

    const response = await fetch(edgeFunctionUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${supabaseAnonKey}`,
        'apikey': supabaseAnonKey,
      },
      body: JSON.stringify(data),
    });

    const responseText = await response.text();
    console.log('Project request notification response status:', response.status);
    console.log('Project request notification response text:', responseText);

    let result;
    try {
      result = JSON.parse(responseText);
    } catch (e) {
      console.error('Failed to parse response as JSON:', e);
      console.error('Raw response text:', responseText);
      return { success: false, error: `Unexpected response format: ${responseText}` };
    }

    console.log('Parsed result:', result);

    if (!response.ok) {
      console.error('HTTP error - status:', response.status);
      console.error('Error details:', result);
      return { success: false, error: `HTTP ${response.status}: ${result.error || 'Failed to send notification'}` };
    }

    if (!result.success) {
      console.error('Edge Function returned success=false:', result);
      return { success: false, error: `Edge Function error: ${result.error || 'Unknown error'}` };
    }

    console.log('Project request notification sent successfully');
    return { success: true };
  } catch (error) {
    console.error('Error sending project request notification:', error);
    return { success: false, error: 'Failed to send notification' };
  }
}

// Function to send questionnaire completion notification
export async function sendQuestionnaireCompletionNotification(
  data: QuestionnaireCompletionNotificationData
): Promise<{ success: boolean; error?: string }> {
  try {
    const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || '';
    const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || '';

    if (!supabaseUrl || !supabaseAnonKey) {
      console.error('Missing Supabase URL or anon key');
      return { success: false, error: 'Server configuration error' };
    }

    console.log('Sending questionnaire completion notification email');

    const edgeFunctionUrl = `${supabaseUrl}/functions/v1/send-questionnaire-completion-notification`;

    const response = await fetch(edgeFunctionUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${supabaseAnonKey}`,
        'apikey': supabaseAnonKey,
      },
      body: JSON.stringify(data),
    });

    const responseText = await response.text();
    console.log('Questionnaire completion notification response:', responseText);

    let result;
    try {
      result = JSON.parse(responseText);
    } catch (e) {
      console.error('Failed to parse response as JSON:', e);
      return { success: false, error: 'Unexpected response format' };
    }

    if (!response.ok || !result.success) {
      console.error('Error sending questionnaire completion notification:', result);
      return { success: false, error: result.error || 'Failed to send notification' };
    }

    console.log('Questionnaire completion notification sent successfully');
    return { success: true };
  } catch (error) {
    console.error('Error sending questionnaire completion notification:', error);
    return { success: false, error: 'Failed to send notification' };
  }
}

// Helper function to format package type for display
export function formatPackageType(packageType: PackageType): string {
  switch (packageType) {
    case 'basic':
      return 'Basic Package';
    case 'standard':
      return 'Standard Package';
    case 'premium':
      return 'Premium Package';
    default:
      return packageType;
  }
}

// Helper function to format currency
export function formatCurrency(amount: number): string {
  return `KES ${amount.toLocaleString()}`;
}

// Helper function to format additional services
export function formatAdditionalServices(services?: string[]): string {
  if (!services || services.length === 0) {
    return 'None';
  }
  return services.join(', ');
}
