'use server';

import { z } from 'zod';
import {
  generalInformationSchema,
  basicPackageSchema,
  standardPackageSchema,
  premiumPackageSchema,
  PackageType
} from '@/lib/questionnaire/types';
import {
  validateQuestionnaireToken,
  getQuestionnaireResponse,
  createQuestionnaireResponse,
  updateGeneralInformation,
  updatePackageInformation
} from '@/lib/questionnaire/supabase-questionnaire';
import { sendQuestionnaireCompletionNotification } from '@/lib/email-notifications';
import { createClient } from '@supabase/supabase-js';

// Create Supabase client for admin operations
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || '';
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY || '';
const supabaseAdmin = createClient(supabaseUrl, supabaseServiceKey);

// Validate access token
export async function validateToken(token: string) {
  try {
    console.log('Server action: validateToken called with token:', token);

    const result = await validateQuestionnaireToken(token);

    console.log('Server action: Token validation result:', result);

    return result;
  } catch (error) {
    console.error('Error in validateToken:', error);
    return {
      isValid: false,
      error: error instanceof Error ? error.message : 'An unexpected error occurred'
    };
  }
}

// Initialize questionnaire
export async function initializeQuestionnaire(clientProjectId: string) {
  try {
    console.log('Server action: initializeQuestionnaire called for client project:', clientProjectId);

    // Check if questionnaire response already exists
    let response = await getQuestionnaireResponse(clientProjectId);

    // If not, create a new one
    if (!response) {
      response = await createQuestionnaireResponse(clientProjectId);
      console.log('Server action: Created new questionnaire response:', response);
    } else {
      console.log('Server action: Found existing questionnaire response:', response);
    }

    return {
      success: true,
      data: response
    };
  } catch (error) {
    console.error('Error in initializeQuestionnaire:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'An unexpected error occurred'
    };
  }
}

// Submit general information (Section 1)
export async function submitGeneralInformation(formData: {
  responseId: string;
  project_name: string;
  project_description: string;
  target_audience: string;
  project_goals: string[];
  color_scheme: string;
  has_logo_or_brand_guidelines: boolean;
  preferred_launch_timeline: string;
}) {
  try {
    console.log('Server action: submitGeneralInformation called with data:', JSON.stringify(formData));

    // Validate required fields
    if (!formData.responseId) {
      console.error('Missing responseId in submitGeneralInformation');
      return {
        success: false,
        error: 'Response ID is required'
      };
    }

    // Validate form data
    let validatedData;
    try {
      const { responseId, ...validationData } = formData;
      validatedData = generalInformationSchema.parse(validationData);
      console.log('Server action: Validated general information data:', validatedData);
    } catch (validationError) {
      console.error('Validation error in submitGeneralInformation:', validationError);
      if (validationError instanceof z.ZodError) {
        return {
          success: false,
          error: validationError.errors.map(e => e.message).join(', ')
        };
      }
      throw validationError;
    }

    // Update questionnaire response
    const response = await updateGeneralInformation(formData.responseId, validatedData);

    console.log('Server action: Updated general information:', response);

    return {
      success: true,
      data: response
    };
  } catch (error) {
    console.error('Error in submitGeneralInformation:', error);
    // Log more details about the error
    if (error instanceof Error) {
      console.error('Error details:', {
        name: error.name,
        message: error.message,
        stack: error.stack
      });
    }

    if (error instanceof z.ZodError) {
      return {
        success: false,
        error: error.errors.map(e => e.message).join(', ')
      };
    }

    return {
      success: false,
      error: error instanceof Error ? error.message : 'An unexpected error occurred'
    };
  }
}

// Submit package-specific information (Section 2)
export async function submitPackageInformation(formData: {
  responseId: string;
  packageType: PackageType;
  [key: string]: any;
}) {
  try {
    console.log('Server action: submitPackageInformation called with data:', JSON.stringify(formData));

    // Validate required fields
    if (!formData.responseId) {
      console.error('Missing responseId in submitPackageInformation');
      return {
        success: false,
        error: 'Response ID is required'
      };
    }

    if (!formData.packageType) {
      console.error('Missing packageType in submitPackageInformation');
      return {
        success: false,
        error: 'Package type is required'
      };
    }

    const { responseId, packageType, ...validationData } = formData;

    // Validate form data based on package type
    let validatedData;
    try {
      switch (packageType) {
        case 'basic':
          validatedData = basicPackageSchema.parse(validationData);
          break;
        case 'standard':
          validatedData = standardPackageSchema.parse(validationData);
          break;
        case 'premium':
          validatedData = premiumPackageSchema.parse(validationData);
          break;
        default:
          throw new Error('Invalid package type');
      }
    } catch (validationError) {
      console.error('Validation error in submitPackageInformation:', validationError);
      if (validationError instanceof z.ZodError) {
        return {
          success: false,
          error: validationError.errors.map(e => e.message).join(', ')
        };
      }
      throw validationError;
    }

    console.log('Server action: Validated data:', validatedData);

    // Update questionnaire response
    const response = await updatePackageInformation(responseId, packageType, validatedData);

    // Add packageType to the response for UI purposes (not stored in DB)
    const enhancedResponse = {
      ...response,
      packageType: packageType
    };

    console.log('Server action: Updated package information:', enhancedResponse);

    // Send email notification if questionnaire is completed
    if (response.completion_status === 'completed') {
      try {
        console.log('Questionnaire completed, sending notification email...');

        // Get client project information
        const { data: clientProject, error: clientError } = await supabaseAdmin
          .from('client_projects')
          .select('*')
          .eq('id', response.client_project_id)
          .single();

        if (clientError || !clientProject) {
          console.error('Error fetching client project for email notification:', clientError);
        } else {
          // Send email notification
          await sendQuestionnaireCompletionNotification({
            client_name: clientProject.client_name,
            client_email: clientProject.client_email,
            client_phone: clientProject.client_phone,
            package_type: clientProject.package_type,
            reference_code: clientProject.reference_code,
            questionnaire_id: response.id,
            completed_at: response.updated_at,
            // General information
            project_name: response.project_name,
            project_description: response.project_description,
            target_audience: response.target_audience,
            project_goals: response.project_goals,
            color_scheme: response.color_scheme,
            has_logo_or_brand_guidelines: response.has_logo_or_brand_guidelines,
            preferred_launch_timeline: response.preferred_launch_timeline,
            // Package-specific data
            package_data: validatedData,
          });
          console.log('Questionnaire completion notification email sent successfully');
        }
      } catch (emailError) {
        console.error('Failed to send questionnaire completion notification email:', emailError);
        // Don't fail the entire request if email fails
      }
    }

    return {
      success: true,
      data: enhancedResponse
    };
  } catch (error) {
    console.error('Error in submitPackageInformation:', error);
    // Log more details about the error
    if (error instanceof Error) {
      console.error('Error details:', {
        name: error.name,
        message: error.message,
        stack: error.stack
      });
    }

    if (error instanceof z.ZodError) {
      return {
        success: false,
        error: error.errors.map(e => e.message).join(', ')
      };
    }

    return {
      success: false,
      error: error instanceof Error ? error.message : 'An unexpected error occurred'
    };
  }
}
