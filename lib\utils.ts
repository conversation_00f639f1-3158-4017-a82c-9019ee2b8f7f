import { clsx, type ClassValue } from "clsx"
import { twMerge } from "tailwind-merge"

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}

/**
 * Converts text with line breaks to an array of paragraphs
 * This is useful for rendering content from the CMS that contains paragraphs
 */
export function textToParagraphs(text: string): string[] {
  if (!text) return []
  // Split by line breaks and filter out empty lines
  return text.split(/\r?\n/).filter(paragraph => paragraph.trim() !== '')
}
