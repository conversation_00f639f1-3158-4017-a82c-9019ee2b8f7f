# Supabase Edge Function Deployment

This package contains the Edge Function code for sending email notifications from your portfolio contact form.

## Manual Deployment Steps

Since we're having issues with the Supabase CLI, you can deploy the Edge Function manually through the Supabase web interface:

1. Go to the Supabase Dashboard: https://supabase.com/dashboard/project/caogszaytzuiqwwkbhhi
2. Navigate to "Edge Functions" in the left sidebar
3. Click "Create a new function"
4. Name the function "send-contact-email"
5. Upload the code from the `send-contact-email/index.ts` file
6. Make sure to also create the `_shared/cors.ts` file in the Edge Functions editor

## Setting Environment Variables

After deploying the Edge Function, you need to set the environment variables:

1. In the Supabase Dashboard, go to "Edge Functions"
2. Select the "send-contact-email" function
3. Click on "Environment variables"
4. Add the following variables:
   - `RESEND_API_KEY`: re_bHXZQij1_KrBUYTuM943sN4ViHBi5qaW3
   - `RECIPIENT_EMAIL`: <EMAIL>

## Testing the Function

After deployment, you can test the function by:

1. Going to your portfolio website
2. Filling out and submitting the contact form
3. You should receive an email <NAME_EMAIL>

## Troubleshooting

If you encounter any issues:

1. Check the Edge Function logs in the Supabase Dashboard
2. Verify that the environment variables are set correctly
3. Make sure the Edge Function is deployed and active
