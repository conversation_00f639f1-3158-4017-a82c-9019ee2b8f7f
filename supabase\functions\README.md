# Supabase Edge Functions

This directory contains Supabase Edge Functions for the portfolio website.

## Functions

### send-contact-email

This function sends an email notification when someone submits the contact form on the portfolio website.

## Deployment

To deploy the Edge Functions to your Supabase project, follow these steps:

1. Install the Supabase CLI locally:
   ```bash
   npm install supabase --save-dev
   ```

2. Login to Supabase:
   ```bash
   npx supabase login
   ```

3. Link your project:
   ```bash
   npx supabase link --project-ref <project-id>
   ```
   Replace `<project-id>` with your Supabase project ID (e.g., "caogszaytzuiqwwkbhhi").

4. Deploy the functions:
   ```bash
   npx supabase functions deploy send-contact-email --project-ref <project-id>
   ```

## Environment Variables

The Edge Functions require the following environment variables:

- `RESEND_API_KEY`: Your Resend.com API key
- `RECIPIENT_EMAIL`: The email address to receive notifications (default: <EMAIL>)

To set these variables, use the Supabase CLI:

```bash
npx supabase secrets set RESEND_API_KEY=<your-resend-api-key> --project-ref <project-id>
npx supabase secrets set RECIPIENT_EMAIL=<EMAIL> --project-ref <project-id>
```

## Testing

You can test the Edge Function locally using the Supabase CLI:

```bash
npx supabase functions serve send-contact-email
```

Then, send a POST request to `http://localhost:54321/functions/v1/send-contact-email` with a JSON body:

```json
{
  "name": "Test User",
  "email": "<EMAIL>",
  "subject": "Test Subject",
  "message": "This is a test message."
}
```
