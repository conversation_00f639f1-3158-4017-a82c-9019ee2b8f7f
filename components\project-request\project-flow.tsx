'use client';

import { useState, useEffect } from 'react';
import { toast } from 'sonner';
import {
  PackageSelectionFormData,
  ClientInformationFormData,
  PackageType,
  calculateTotalPrice,
} from '@/lib/mpesa/types';
import { createProjectRequest } from '@/app/actions/project-request-actions';
import PackageSelectionForm from '@/components/mpesa/package-selection-form';
import ClientInformationForm from '@/components/mpesa/client-information-form';
import ProjectRequestConfirmation from './project-request-confirmation';
import { StepProgress } from '@/components/ui/step-progress';

type Step = 'package-selection' | 'client-information' | 'confirmation';

export default function ProjectFlow() {
  const [currentStep, setCurrentStep] = useState<Step>('package-selection');
  const [progress, setProgress] = useState(33);

  // Form data state
  const [packageData, setPackageData] = useState<PackageSelectionFormData | null>(null);
  const [clientData, setClientData] = useState<ClientInformationFormData | null>(null);
  const [referenceCode, setReferenceCode] = useState<string>('');
  const [totalAmount, setTotalAmount] = useState<number>(0);
  const [isNetworkError, setIsNetworkError] = useState<boolean>(false);

  // Handle package selection form submission
  const handlePackageSelection = (data: PackageSelectionFormData) => {
    setPackageData(data);
    setCurrentStep('client-information');
    setProgress(66);
  };

  // Handle client information form submission
  const handleClientInformation = async (data: ClientInformationFormData) => {
    setClientData(data);

    try {
      // Show loading toast
      const loadingToast = toast.loading('Submitting your project request...');

      // Ensure packageData is defined before proceeding
      if (!packageData || !packageData.package_type) {
        toast.dismiss(loadingToast);
        toast.error('Package selection is missing. Please go back and select a package.');
        return;
      }

      console.log('Submitting project request with data:', {
        client_name: data.client_name,
        client_email: data.client_email,
        client_phone: data.client_phone,
        package_type: packageData.package_type,
        additional_services: Array.isArray(packageData.additional_services) ? packageData.additional_services : [],
        maintenance_plan: packageData.maintenance_plan,
      });

      try {
        // Create project request
        const result = await createProjectRequest({
          client_name: data.client_name,
          client_email: data.client_email,
          client_phone: data.client_phone,
          package_type: packageData.package_type as PackageType,
          additional_services: Array.isArray(packageData.additional_services) ? packageData.additional_services : [],
          maintenance_plan: packageData.maintenance_plan,
        });

        // Dismiss loading toast
        toast.dismiss(loadingToast);

        console.log('Project request submission result:', result);

        // Check if result is defined before accessing properties
        if (result && result.success && result.data) {
          setReferenceCode(result.data.reference_code);
          setTotalAmount(result.data.total_amount);
          setCurrentStep('confirmation');
          setProgress(100);
          toast.success('Project request submitted successfully!');
        } else {
          console.error('Failed to submit project request:', result?.error || 'Unknown error');

          // Check if it's a fetch error (network issue)
          if (result?.error && result.error.includes('fetch failed')) {
            // Create a mock reference code for the user to continue
            const mockReferenceCode = `ZO${Date.now().toString().slice(-6)}${Math.floor(Math.random() * 1000).toString().padStart(3, '0')}`;
            const totalAmount = calculateTotalPrice(
              packageData.package_type as PackageType,
              Array.isArray(packageData.additional_services) ? packageData.additional_services : [],
              packageData.maintenance_plan
            );

            setReferenceCode(mockReferenceCode);
            setTotalAmount(totalAmount);
            setCurrentStep('confirmation');
            setProgress(100);
            setIsNetworkError(true);

            toast.warning(
              'We encountered a network issue while saving your request, but you can continue. Please contact Zachary <NAME_EMAIL> with your information.',
              { duration: 8000 }
            );
          } else {
            toast.error(result?.error || 'Failed to submit project request. Please try again.');
          }
        }
      } catch (serverActionError) {
        console.error('Server action error:', serverActionError);
        toast.dismiss(loadingToast);

        // Check if it's a fetch error (network issue)
        const errorMessage = serverActionError instanceof Error ? serverActionError.message : 'Unknown error';

        if (errorMessage.includes('fetch failed') || errorMessage.includes('network')) {
          // Create a mock reference code for the user to continue
          const mockReferenceCode = `ZO${Date.now().toString().slice(-6)}${Math.floor(Math.random() * 1000).toString().padStart(3, '0')}`;
          const totalAmount = calculateTotalPrice(
            packageData.package_type as PackageType,
            Array.isArray(packageData.additional_services) ? packageData.additional_services : [],
            packageData.maintenance_plan
          );

          setReferenceCode(mockReferenceCode);
          setTotalAmount(totalAmount);
          setCurrentStep('confirmation');
          setProgress(100);
          setIsNetworkError(true);

          toast.warning(
            'We encountered a network issue while saving your request, but you can continue. Please contact Zachary <NAME_EMAIL> with your information.',
            { duration: 8000 }
          );
        } else {
          toast.error(`Server action error: ${errorMessage}`);
        }
      }
    } catch (error) {
      console.error('Unexpected error submitting project request:', error);

      // Check if it's a fetch error (network issue)
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';

      if (errorMessage.includes('fetch failed') || errorMessage.includes('network')) {
        // Create a mock reference code for the user to continue
        const mockReferenceCode = `ZO${Date.now().toString().slice(-6)}${Math.floor(Math.random() * 1000).toString().padStart(3, '0')}`;
        const totalAmount = calculateTotalPrice(
          packageData?.package_type as PackageType,
          Array.isArray(packageData?.additional_services) ? packageData.additional_services : [],
          packageData?.maintenance_plan
        );

        setReferenceCode(mockReferenceCode);
        setTotalAmount(totalAmount);
        setCurrentStep('confirmation');
        setProgress(100);
        setIsNetworkError(true);

        toast.warning(
          'We encountered a network issue while saving your request, but you can continue. Please contact Zachary <NAME_EMAIL> with your information.',
          { duration: 8000 }
        );
      } else {
        toast.error(`An unexpected error occurred: ${errorMessage}`);
      }
    }
  };

  // Handle back button clicks
  const handleBack = (step: Step) => {
    setCurrentStep(step);

    // Update progress based on step
    switch (step) {
      case 'package-selection':
        setProgress(33);
        break;
      case 'client-information':
        setProgress(66);
        break;
      default:
        break;
    }
  };

  // Render current step
  const renderStep = () => {
    switch (currentStep) {
      case 'package-selection':
        return <PackageSelectionForm onSubmit={handlePackageSelection} />;

      case 'client-information':
        return (
          <ClientInformationForm
            onSubmit={handleClientInformation}
            onBack={() => handleBack('package-selection')}
          />
        );

      case 'confirmation':
        return (
          <ProjectRequestConfirmation
            clientName={clientData?.client_name || ''}
            clientEmail={clientData?.client_email || ''}
            clientPhone={clientData?.client_phone || ''}
            referenceCode={referenceCode}
            packageType={packageData?.package_type || 'basic'}
            totalAmount={totalAmount}
            additionalServices={packageData?.additional_services}
            maintenancePlan={packageData?.maintenance_plan}
            isNetworkError={isNetworkError}
          />
        );

      default:
        return null;
    }
  };

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-center mb-6">Start Your Web Development Project</h1>

        <div className="w-full max-w-3xl mx-auto mb-8 px-4 sm:px-0">
          <StepProgress
            steps={[
              {
                label: "Package Selection",
                status: currentStep === 'package-selection'
                  ? 'current'
                  : (progress > 33 ? 'completed' : 'upcoming')
              },
              {
                label: "Your Information",
                status: currentStep === 'client-information'
                  ? 'current'
                  : (progress > 66 ? 'completed' : 'upcoming')
              },
              {
                label: "Confirmation",
                status: currentStep === 'confirmation'
                  ? 'current'
                  : 'upcoming'
              }
            ]}
            currentStep={
              currentStep === 'package-selection' ? 1 :
              currentStep === 'client-information' ? 2 : 3
            }
            progress={progress}
          />
        </div>
      </div>

      {renderStep()}
    </div>
  );
}
