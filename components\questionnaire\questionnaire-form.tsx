'use client';

import { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { Card, CardContent } from '@/components/ui/card';
import { StepProgress } from '@/components/ui/step-progress';
import { toast } from 'sonner';
import { initializeQuestionnaire } from '@/app/actions/questionnaire-actions';
import { QuestionnaireResponse, PackageType } from '@/lib/questionnaire/types';
import GeneralInformationForm from './general-information-form';
import BasicPackageForm from './basic-package-form';
import StandardPackageForm from './standard-package-form';
import PremiumPackageForm from './premium-package-form';
import QuestionnaireComplete from './questionnaire-complete';

interface QuestionnaireFormProps {
  clientProjectId: string;
  packageType: PackageType;
}

type Step = 'general-information' | 'package-specific' | 'complete';

export default function QuestionnaireForm({
  clientProjectId,
  packageType
}: QuestionnaireFormProps) {
  const [isLoading, setIsLoading] = useState(true);
  const [currentStep, setCurrentStep] = useState<Step>('general-information');
  const [progress, setProgress] = useState(50);
  const [responseId, setResponseId] = useState<string>('');
  const [questionnaireData, setQuestionnaireData] = useState<QuestionnaireResponse | null>(null);

  // Initialize questionnaire
  useEffect(() => {
    const initialize = async () => {
      try {
        setIsLoading(true);

        console.log('Initializing questionnaire for client project:', clientProjectId);

        // Check if client project ID is valid
        if (!clientProjectId) {
          console.error('Invalid client project ID');
          toast.error('Invalid client project ID');
          setIsLoading(false);
          return;
        }

        const result = await initializeQuestionnaire(clientProjectId);
        console.log('Questionnaire initialization result:', result);

        if (result.success && result.data) {
          setResponseId(result.data.id);
          setQuestionnaireData(result.data);

          // Set initial step based on last completed section
          if (result.data.completion_status === 'completed') {
            setCurrentStep('complete');
            setProgress(100);
          } else if (result.data.last_section_completed >= 1) {
            setCurrentStep('package-specific');
            setProgress(75);
          } else {
            setCurrentStep('general-information');
            setProgress(50);
          }
        } else {
          console.error('Failed to initialize questionnaire:', result.error);
          toast.error(result.error || 'Failed to initialize questionnaire');
        }
      } catch (error) {
        console.error('Error initializing questionnaire:', error);
        // More detailed error logging
        if (error instanceof Error) {
          console.error('Error name:', error.name);
          console.error('Error message:', error.message);
          console.error('Error stack:', error.stack);
        }
        toast.error('An unexpected error occurred while initializing the questionnaire');
      } finally {
        setIsLoading(false);
      }
    };

    initialize();
  }, [clientProjectId]);

  // Handle general information form completion
  const handleGeneralInformationComplete = (data: QuestionnaireResponse) => {
    setQuestionnaireData(data);
    setCurrentStep('package-specific');
    setProgress(75);
  };

  // Handle package-specific form completion
  const handlePackageFormComplete = (data: QuestionnaireResponse) => {
    setQuestionnaireData(data);
    setCurrentStep('complete');
    setProgress(100);
  };

  // Handle back button
  const handleBack = () => {
    setCurrentStep('general-information');
    setProgress(50);
  };

  // Render loading state
  if (isLoading) {
    return (
      <Card className="w-full mobile-card">
        <CardContent className="pt-4 sm:pt-6">
          <div className="flex flex-col items-center justify-center py-8 sm:py-12">
            <div className="animate-spin rounded-full h-10 w-10 sm:h-12 sm:w-12 border-b-2 border-primary mb-4"></div>
            <p className="text-muted-foreground text-sm sm:text-base">Loading questionnaire...</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  // Render current step
  const renderStep = () => {
    switch (currentStep) {
      case 'general-information':
        return (
          <GeneralInformationForm
            responseId={responseId}
            initialData={questionnaireData || undefined}
            onComplete={handleGeneralInformationComplete}
          />
        );

      case 'package-specific':
        // Render the appropriate package form based on the package type
        switch (packageType) {
          case 'premium':
            return (
              <PremiumPackageForm
                responseId={responseId}
                initialData={questionnaireData || undefined}
                onComplete={handlePackageFormComplete}
                onBack={handleBack}
              />
            );
          case 'standard':
            return (
              <StandardPackageForm
                responseId={responseId}
                initialData={questionnaireData || undefined}
                onComplete={handlePackageFormComplete}
                onBack={handleBack}
              />
            );
          case 'basic':
          default:
            return (
              <BasicPackageForm
                responseId={responseId}
                initialData={questionnaireData || undefined}
                onComplete={handlePackageFormComplete}
                onBack={handleBack}
              />
            );
        }

      case 'complete':
        return (
          <QuestionnaireComplete
            clientName={questionnaireData?.project_name || 'Client'}
            projectName={questionnaireData?.project_name || 'Your Project'}
          />
        );

      default:
        return null;
    }
  };

  return (
    <div className="w-full">
      <div className="mb-4 sm:mb-8 px-4 sm:px-0">
        <StepProgress
          steps={[
            {
              label: "General Info",
              status: currentStep === 'general-information'
                ? 'current'
                : (progress > 50 ? 'completed' : 'upcoming')
            },
            {
              label: "Package Details",
              status: currentStep === 'package-specific'
                ? 'current'
                : (progress > 75 ? 'completed' : 'upcoming')
            },
            {
              label: "Complete",
              status: currentStep === 'complete'
                ? 'current'
                : 'upcoming'
            }
          ]}
          currentStep={
            currentStep === 'general-information' ? 1 :
            currentStep === 'package-specific' ? 2 : 3
          }
          progress={progress}
        />
      </div>

      <motion.div
        key={currentStep}
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        exit={{ opacity: 0 }}
        transition={{ duration: 0.3 }}
      >
        <Card className="w-full mobile-card">
          <CardContent className="pt-4 sm:pt-6 px-3 sm:px-6">
            {renderStep()}
          </CardContent>
        </Card>
      </motion.div>
    </div>
  );
}
