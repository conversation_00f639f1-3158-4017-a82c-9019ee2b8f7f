// This script seeds the CMS database with initial content
const fs = require('fs');
const path = require('path');
const { createClient } = require('@supabase/supabase-js');

// Load environment variables from .env.local
const envPath = path.resolve(process.cwd(), '.env.local');
const envContent = fs.readFileSync(envPath, 'utf8');
const envVars = {};

envContent.split('\n').forEach(line => {
  const match = line.match(/^([^#=]+)=(.*)$/);
  if (match) {
    const key = match[1].trim();
    const value = match[2].trim();
    envVars[key] = value;
  }
});

// Create a Supabase client
const supabaseUrl = envVars.NEXT_PUBLIC_SUPABASE_URL;
const supabaseAnonKey = envVars.NEXT_PUBLIC_SUPABASE_ANON_KEY;
const supabase = createClient(supabaseUrl, supabaseAnonKey);

// Initial content data
const initialContent = [
  // Hero section
  { section: 'hero', key: 'title', content: "Zachary Odero" },
  { section: 'hero', key: 'subtitle', content: "Full Stack Developer" },
  { section: 'hero', key: 'description', content: "A passionate full-stack developer specializing in creating elegant, user-friendly web applications." },

  // About section
  { section: 'about', key: 'bio', content: "I'm a full-stack developer with over 5 years of experience building web applications. I specialize in JavaScript, React, and Node.js, with a strong focus on creating intuitive user experiences." },
  { section: 'about', key: 'education', content: "My journey in web development began during college, where I discovered my passion for creating digital solutions. Since then, I've worked with startups and established companies to bring their visions to life." },
  { section: 'about', key: 'experience', content: "When I'm not coding, you can find me hiking, reading, or experimenting with new technologies." },

  // Projects section
  { section: 'projects', key: 'project_intro', content: "Here are some of my recent projects:" },
  { section: 'projects', key: 'project_1_title', content: "E-commerce Platform" },
  { section: 'projects', key: 'project_1_description', content: "A full-featured e-commerce platform with product management, cart functionality, and payment processing." },
  { section: 'projects', key: 'project_1_tags', content: "Next.js,Stripe,Tailwind CSS" },
  { section: 'projects', key: 'project_1_github', content: "https://github.com/zacharyodero/ecommerce-platform" },
  { section: 'projects', key: 'project_1_demo', content: "https://ecommerce-platform-demo.vercel.app" },
  { section: 'projects', key: 'project_2_title', content: "Task Management App" },
  { section: 'projects', key: 'project_2_description', content: "A productivity application with real-time updates and team collaboration features." },
  { section: 'projects', key: 'project_2_tags', content: "React,Firebase,Material UI" },
  { section: 'projects', key: 'project_2_github', content: "https://github.com/zacharyodero/task-management-app" },
  { section: 'projects', key: 'project_2_demo', content: "https://task-management-app-demo.vercel.app" },
  { section: 'projects', key: 'project_3_title', content: "Portfolio Website" },
  { section: 'projects', key: 'project_3_description', content: "A minimalist portfolio website showcasing my work and skills." },
  { section: 'projects', key: 'project_3_tags', content: "Next.js,Tailwind CSS,Framer Motion" },
  { section: 'projects', key: 'project_3_github', content: "https://github.com/zacharyodero/my-nextjs-portfolio" },
  { section: 'projects', key: 'project_3_demo', content: "https://zacharyodero.com" },

  // Skills section
  { section: 'skills', key: 'skills_intro', content: "My technical skills include:" },
  { section: 'skills', key: 'frontend_skills', content: "JavaScript,TypeScript,React,Next.js,HTML,CSS,Tailwind CSS,Framer Motion" },
  { section: 'skills', key: 'backend_skills', content: "Node.js,Express,Supabase,Firebase,PostgreSQL,MongoDB" },
  { section: 'skills', key: 'other_skills', content: "Git,GitHub,Vercel,AWS,Docker,Jest" },

  // Contact section
  { section: 'contact', key: 'contact_intro', content: "Let's connect! Feel free to reach out to me through any of the following channels:" },
  { section: 'contact', key: 'email', content: "<EMAIL>" },
  { section: 'contact', key: 'linkedin', content: "https://www.linkedin.com/in/zacharyodero" },
  { section: 'contact', key: 'github', content: "https://github.com/ZacharyOdero" },

  // General section
  { section: 'general', key: 'site_title', content: "Zachary Odero - Portfolio" },
  { section: 'general', key: 'site_description', content: "Personal portfolio website of Zachary Odero" },
  { section: 'general', key: 'meta_keywords', content: "web development, full stack, react, next.js, portfolio" },
];

async function seedContent() {
  console.log('Starting to seed content...');

  try {
    // Insert content
    for (const item of initialContent) {
      // Check if content already exists
      const { data: existingContent, error: checkError } = await supabase
        .from('portfolio_content')
        .select('*')
        .eq('section', item.section)
        .eq('key', item.key)
        .single();

      if (checkError && checkError.code !== 'PGRST116') {
        console.error(`Error checking for existing content (${item.section}.${item.key}):`, checkError);
        continue;
      }

      if (existingContent) {
        console.log(`Content for ${item.section}.${item.key} already exists, skipping...`);
        continue;
      }

      // Insert new content
      const { error: insertError } = await supabase
        .from('portfolio_content')
        .insert([item]);

      if (insertError) {
        console.error(`Error inserting content for ${item.section}.${item.key}:`, insertError);
      } else {
        console.log(`Successfully inserted content for ${item.section}.${item.key}`);
      }
    }

    console.log('Content seeding completed!');
  } catch (error) {
    console.error('Error seeding content:', error);
  }
}

// Run the seed function
seedContent();
