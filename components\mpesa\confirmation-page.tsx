'use client';

import { motion } from 'framer-motion';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { CheckCircle, Calendar, Mail, Phone, ArrowRight } from 'lucide-react';
import Link from 'next/link';

interface ConfirmationPageProps {
  clientName: string;
  clientEmail: string;
  clientPhone: string;
  referenceCode: string;
  packageType: string;
  totalAmount: number;
  depositAmount: number;
  questionnaireUrl?: string;
}

export default function ConfirmationPage({
  clientName,
  clientEmail,
  clientPhone,
  referenceCode,
  packageType,
  totalAmount,
  depositAmount,
  questionnaireUrl,
}: ConfirmationPageProps) {
  // Format package type for display
  const formatPackageType = (type: string) => {
    return type.charAt(0).toUpperCase() + type.slice(1);
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
      className="w-full max-w-2xl mx-auto"
    >
      <div className="text-center mb-8">
        <motion.div
          initial={{ scale: 0 }}
          animate={{ scale: 1 }}
          transition={{ delay: 0.2, type: 'spring', stiffness: 200, damping: 15 }}
          className="inline-block mb-4"
        >
          <CheckCircle className="h-16 w-16 text-green-500" />
        </motion.div>
        <h2 className="text-2xl font-bold mb-2">Payment Confirmed!</h2>
        <p className="text-muted-foreground">
          Thank you for your payment. Your project has been registered successfully.
        </p>
      </div>
      
      <Card className="mb-6">
        <CardContent className="pt-6">
          <div className="space-y-6">
            <div className="space-y-4">
              <h3 className="text-lg font-semibold border-b pb-2">Project Details</h3>
              
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <p className="text-sm text-muted-foreground">Reference Code</p>
                  <p className="font-medium">{referenceCode}</p>
                </div>
                <div>
                  <p className="text-sm text-muted-foreground">Package</p>
                  <p className="font-medium">{formatPackageType(packageType)} Package</p>
                </div>
                <div>
                  <p className="text-sm text-muted-foreground">Total Amount</p>
                  <p className="font-medium">KSh {totalAmount.toLocaleString()}</p>
                </div>
                <div>
                  <p className="text-sm text-muted-foreground">Deposit Paid</p>
                  <p className="font-medium">KSh {depositAmount.toLocaleString()}</p>
                </div>
              </div>
            </div>
            
            <div className="space-y-4">
              <h3 className="text-lg font-semibold border-b pb-2">Client Information</h3>
              
              <div className="space-y-3">
                <div className="flex items-start">
                  <span className="flex-shrink-0 w-5 mr-2 text-muted-foreground">
                    <CheckCircle className="h-5 w-5" />
                  </span>
                  <div>
                    <p className="text-sm text-muted-foreground">Name</p>
                    <p className="font-medium">{clientName}</p>
                  </div>
                </div>
                <div className="flex items-start">
                  <span className="flex-shrink-0 w-5 mr-2 text-muted-foreground">
                    <Mail className="h-5 w-5" />
                  </span>
                  <div>
                    <p className="text-sm text-muted-foreground">Email</p>
                    <p className="font-medium">{clientEmail}</p>
                  </div>
                </div>
                <div className="flex items-start">
                  <span className="flex-shrink-0 w-5 mr-2 text-muted-foreground">
                    <Phone className="h-5 w-5" />
                  </span>
                  <div>
                    <p className="text-sm text-muted-foreground">Phone</p>
                    <p className="font-medium">{clientPhone}</p>
                  </div>
                </div>
              </div>
            </div>
            
            <div className="space-y-4">
              <h3 className="text-lg font-semibold border-b pb-2">Next Steps</h3>
              
              <div className="space-y-3">
                <div className="flex items-start">
                  <span className="flex-shrink-0 w-5 mr-2 text-muted-foreground">
                    <Calendar className="h-5 w-5" />
                  </span>
                  <div>
                    <p className="font-medium">Complete Project Questionnaire</p>
                    <p className="text-sm text-muted-foreground">
                      Please complete the project questionnaire to help us understand your requirements better.
                    </p>
                  </div>
                </div>
                
                {questionnaireUrl && (
                  <div className="ml-7">
                    <Link href={questionnaireUrl} passHref>
                      <Button className="w-full sm:w-auto">
                        Go to Questionnaire <ArrowRight className="ml-2 h-4 w-4" />
                      </Button>
                    </Link>
                  </div>
                )}
                
                <div className="flex items-start mt-4">
                  <span className="flex-shrink-0 w-5 mr-2 text-muted-foreground">
                    <Mail className="h-5 w-5" />
                  </span>
                  <div>
                    <p className="font-medium">Check Your Email</p>
                    <p className="text-sm text-muted-foreground">
                      We've sent a confirmation email to {clientEmail} with all the details and next steps.
                    </p>
                  </div>
                </div>
              </div>
            </div>
            
            <div className="bg-muted p-4 rounded-lg">
              <p className="text-sm">
                <strong>Note:</strong> Please save your reference code ({referenceCode}) for future communications.
                If you have any questions, feel free to contact me at{' '}
                <a href="mailto:<EMAIL>" className="text-primary hover:underline">
                  <EMAIL>
                </a>
              </p>
            </div>
          </div>
        </CardContent>
      </Card>
      
      <div className="text-center">
        <Link href="/" passHref>
          <Button variant="outline">
            Return to Homepage
          </Button>
        </Link>
      </div>
    </motion.div>
  );
}
