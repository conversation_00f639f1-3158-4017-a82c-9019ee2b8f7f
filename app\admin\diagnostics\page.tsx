"use client";

import { useState, useEffect } from "react";
import { useSession } from "next-auth/react";
import { useRouter } from "next/navigation";
import { Button } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { toast } from "sonner";
import { useIsMobile } from "@/hooks/use-mobile";

export default function DiagnosticsPage() {
  const { data: session, status } = useSession();
  const router = useRouter();
  const [diagnostics, setDiagnostics] = useState<any>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const isMobile = useIsMobile();

  useEffect(() => {
    // Don't fetch if not authenticated
    if (status === "unauthenticated") {
      router.push("/admin/login");
      return;
    }

    // Wait for authentication to complete
    if (status === "loading") {
      return;
    }

    const fetchDiagnostics = async () => {
      try {
        setIsLoading(true);
        const res = await fetch("/api/cms/diagnostics", {
          cache: "no-store",
        });

        if (!res.ok) {
          if (res.status === 401) {
            toast.error("You must be logged in to view diagnostics");
            router.push("/admin/login");
            return;
          }
          throw new Error(`Server error: ${res.status}`);
        }

        const data = await res.json();
        setDiagnostics(data);
        setError(null);
      } catch (err: any) {
        console.error("Error fetching diagnostics:", err);
        setError(err.message || "Failed to load diagnostics");
        toast.error("Failed to load diagnostics");
      } finally {
        setIsLoading(false);
      }
    };

    fetchDiagnostics();
  }, [router, status]);

  if (isLoading) {
    return (
      <div className="flex justify-center py-8">
        <div className="h-10 w-10 animate-spin rounded-full border-b-2 border-t-2 border-blue-500"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex flex-col items-center justify-center p-4 py-8 text-center">
        <div className="p-4 mb-6 bg-red-50 dark:bg-red-900/20 rounded-lg border border-red-200 dark:border-red-800">
          <p className="text-red-600 dark:text-red-400">{error}</p>
        </div>
        <Button
          variant="outline"
          onClick={() => router.push("/admin")}
          className="w-full sm:w-auto h-10 touch-manipulation"
        >
          Back to Dashboard
        </Button>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4">
        <h1 className="text-2xl md:text-3xl font-bold">System Diagnostics</h1>
        <Button
          variant="outline"
          onClick={() => router.push("/admin")}
          className="w-full sm:w-auto"
        >
          Back to Dashboard
        </Button>
      </div>

      <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
        <Card>
          <CardHeader className="p-4 md:p-6">
            <CardTitle className="text-lg md:text-xl">Authentication</CardTitle>
          </CardHeader>
          <CardContent className="p-4 md:p-6 pt-0">
            <div className="space-y-3">
              <div className="p-3 bg-gray-50 dark:bg-gray-800 rounded-md">
                <p className="flex flex-wrap justify-between">
                  <strong className="mr-2">Session:</strong>
                  <span className={diagnostics?.session?.exists ? "text-green-600 dark:text-green-400" : "text-red-600 dark:text-red-400"}>
                    {diagnostics?.session?.exists ? "Active" : "Not active"}
                  </span>
                </p>
              </div>
              <div className="p-3 bg-gray-50 dark:bg-gray-800 rounded-md">
                <p className="flex flex-wrap justify-between">
                  <strong className="mr-2">User:</strong>
                  <span className="text-blue-600 dark:text-blue-400 break-all">{diagnostics?.session?.user}</span>
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="p-4 md:p-6">
            <CardTitle className="text-lg md:text-xl">Supabase Connection</CardTitle>
          </CardHeader>
          <CardContent className="p-4 md:p-6 pt-0">
            <div className="space-y-3">
              <div className="p-3 bg-gray-50 dark:bg-gray-800 rounded-md">
                <p className="flex flex-wrap justify-between">
                  <strong className="mr-2">URL:</strong>
                  <span>{diagnostics?.supabase?.url}</span>
                </p>
              </div>
              <div className="p-3 bg-gray-50 dark:bg-gray-800 rounded-md">
                <p className="flex flex-wrap justify-between">
                  <strong className="mr-2">Connection:</strong>
                  <span className={diagnostics?.supabase?.connection === 'Connected' ? "text-green-600 dark:text-green-400" : "text-red-600 dark:text-red-400"}>
                    {diagnostics?.supabase?.connection}
                  </span>
                </p>
                {diagnostics?.supabase?.connection.startsWith('Error:') && (
                  <div className="mt-2 text-sm text-red-600 dark:text-red-400 break-words">
                    <p>Error details: {diagnostics?.supabase?.connection.substring(7)}</p>
                    <p className="mt-1">
                      <Button
                        variant="link"
                        className="p-0 h-auto text-blue-600 dark:text-blue-400"
                        onClick={() => {
                          router.refresh();
                          window.location.reload();
                        }}
                      >
                        Try refreshing
                      </Button>
                    </p>
                  </div>
                )}
              </div>
              <div className="p-3 bg-gray-50 dark:bg-gray-800 rounded-md">
                <p className="flex flex-wrap justify-between">
                  <strong className="mr-2">Image Count:</strong>
                  <span>{diagnostics?.supabase?.imageCount}</span>
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="md:col-span-2">
          <CardHeader className="p-4 md:p-6">
            <CardTitle className="text-lg md:text-xl">Environment Variables</CardTitle>
          </CardHeader>
          <CardContent className="p-4 md:p-6 pt-0">
            <div className="grid grid-cols-1 gap-3 sm:grid-cols-2">
              {diagnostics?.environment &&
                Object.entries(diagnostics.environment).map(([key, value]) => (
                  <div key={key} className="p-3 bg-gray-50 dark:bg-gray-800 rounded-md">
                    <p className="flex flex-col sm:flex-row sm:justify-between">
                      <strong className="mr-2 break-all">{key}:</strong>
                      <span className={value === 'Set' ? "text-green-600 dark:text-green-400" : value === 'Not set' ? "text-red-600 dark:text-red-400" : ""}>
                        {value as string}
                      </span>
                    </p>
                  </div>
                ))}
            </div>
          </CardContent>
        </Card>
      </div>

      <div className="flex justify-center mt-8">
        <Button
          onClick={() => {
            router.refresh();
            window.location.reload();
          }}
          className="w-full sm:w-auto h-10 touch-manipulation"
        >
          Refresh Diagnostics
        </Button>
      </div>
    </div>
  );
}
