import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { createClient } from '@supabase/supabase-js';

// Create a Supabase client
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || '';
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || '';
const supabase = createClient(supabaseUrl, supabaseAnonKey);

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);

    // Check if user is authenticated
    if (!session) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Initialize variables for Supabase connection test
    let connectionTest = 0;
    let connectionError = null;
    let connectionStatus = 'Unknown';

    // Check Supabase connection
    try {
      const result = await supabase
        .from('portfolio_images')
        .select('*', { count: 'exact', head: true });

      connectionTest = result.count || 0;
      connectionError = result.error;
      connectionStatus = connectionError ? `Error: ${connectionError.message}` : 'Connected';
    } catch (supabaseError: any) {
      console.error('Error testing Supabase connection:', supabaseError);
      connectionStatus = `Connection error: ${supabaseError.message}`;
    }

    // Check environment variables (mask sensitive values)
    const envCheck = {
      NEXT_PUBLIC_SUPABASE_URL: process.env.NEXT_PUBLIC_SUPABASE_URL ? 'Set' : 'Not set',
      NEXT_PUBLIC_SUPABASE_ANON_KEY: process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY ? 'Set' : 'Not set',
      SUPABASE_SERVICE_ROLE_KEY: process.env.SUPABASE_SERVICE_ROLE_KEY ? 'Set' : 'Not set',
      NEXTAUTH_SECRET: process.env.NEXTAUTH_SECRET ? 'Set' : 'Not set',
      NEXTAUTH_URL: process.env.NEXTAUTH_URL,
      ADMIN_EMAIL: process.env.ADMIN_EMAIL ? 'Set' : 'Not set',
      ADMIN_PASSWORD_HASH: process.env.ADMIN_PASSWORD_HASH ? 'Set' : 'Not set',
    };

    return NextResponse.json({
      status: 'ok',
      session: {
        exists: !!session,
        user: session?.user?.email || 'Not authenticated'
      },
      supabase: {
        url: supabaseUrl ? 'Set' : 'Not set',
        connection: connectionStatus,
        imageCount: connectionTest
      },
      environment: envCheck
    });
  } catch (error: any) {
    console.error('Error in diagnostics route:', error);
    return NextResponse.json(
      { error: error.message || 'Diagnostics check failed' },
      { status: 500 }
    );
  }
}
