#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

/**
 * Simple bundle analyzer script
 * Analyzes the Next.js build output and provides insights
 */

function formatBytes(bytes) {
  if (bytes === 0) return '0 Bytes';
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

function analyzeDirectory(dirPath, basePath = '') {
  const results = [];
  
  if (!fs.existsSync(dirPath)) {
    return results;
  }

  const items = fs.readdirSync(dirPath);
  
  for (const item of items) {
    const itemPath = path.join(dirPath, item);
    const relativePath = path.join(basePath, item);
    const stats = fs.statSync(itemPath);
    
    if (stats.isDirectory()) {
      results.push(...analyzeDirectory(itemPath, relativePath));
    } else if (stats.isFile()) {
      results.push({
        path: relativePath,
        size: stats.size,
        type: path.extname(item)
      });
    }
  }
  
  return results;
}

function analyzeBuild() {
  console.log('🔍 Analyzing Next.js Build Output...\n');
  
  const buildDir = path.join(process.cwd(), '.next');
  
  if (!fs.existsSync(buildDir)) {
    console.log('❌ Build directory not found. Please run "npm run build" first.');
    return;
  }

  // Analyze static files
  const staticDir = path.join(buildDir, 'static');
  const staticFiles = analyzeDirectory(staticDir, 'static');
  
  // Analyze server files
  const serverDir = path.join(buildDir, 'server');
  const serverFiles = analyzeDirectory(serverDir, 'server');
  
  // Combine and sort by size
  const allFiles = [...staticFiles, ...serverFiles].sort((a, b) => b.size - a.size);
  
  // Calculate totals
  const totalSize = allFiles.reduce((sum, file) => sum + file.size, 0);
  const jsFiles = allFiles.filter(f => f.type === '.js');
  const cssFiles = allFiles.filter(f => f.type === '.css');
  const imageFiles = allFiles.filter(f => ['.png', '.jpg', '.jpeg', '.gif', '.svg', '.webp'].includes(f.type));
  
  console.log('📊 Bundle Analysis Results');
  console.log('=' .repeat(50));
  console.log(`Total Build Size: ${formatBytes(totalSize)}`);
  console.log(`JavaScript Files: ${jsFiles.length} (${formatBytes(jsFiles.reduce((sum, f) => sum + f.size, 0))})`);
  console.log(`CSS Files: ${cssFiles.length} (${formatBytes(cssFiles.reduce((sum, f) => sum + f.size, 0))})`);
  console.log(`Image Files: ${imageFiles.length} (${formatBytes(imageFiles.reduce((sum, f) => sum + f.size, 0))})`);
  console.log('');
  
  // Show largest files
  console.log('🔥 Largest Files (Top 10):');
  console.log('-'.repeat(50));
  allFiles.slice(0, 10).forEach((file, index) => {
    console.log(`${index + 1}. ${file.path} - ${formatBytes(file.size)}`);
  });
  console.log('');
  
  // Show JavaScript chunks
  const chunks = jsFiles.filter(f => f.path.includes('static/chunks/'));
  if (chunks.length > 0) {
    console.log('📦 JavaScript Chunks:');
    console.log('-'.repeat(50));
    chunks.forEach(chunk => {
      console.log(`${chunk.path} - ${formatBytes(chunk.size)}`);
    });
    console.log('');
  }
  
  // Performance recommendations
  console.log('💡 Performance Recommendations:');
  console.log('-'.repeat(50));
  
  const largeJsFiles = jsFiles.filter(f => f.size > 500 * 1024); // > 500KB
  if (largeJsFiles.length > 0) {
    console.log('⚠️  Large JavaScript files detected:');
    largeJsFiles.forEach(file => {
      console.log(`   - ${file.path} (${formatBytes(file.size)})`);
    });
    console.log('   Consider code splitting or lazy loading.');
    console.log('');
  }
  
  const largeCssFiles = cssFiles.filter(f => f.size > 100 * 1024); // > 100KB
  if (largeCssFiles.length > 0) {
    console.log('⚠️  Large CSS files detected:');
    largeCssFiles.forEach(file => {
      console.log(`   - ${file.path} (${formatBytes(file.size)})`);
    });
    console.log('   Consider CSS optimization or critical CSS extraction.');
    console.log('');
  }
  
  const largeImages = imageFiles.filter(f => f.size > 1024 * 1024); // > 1MB
  if (largeImages.length > 0) {
    console.log('⚠️  Large image files detected:');
    largeImages.forEach(file => {
      console.log(`   - ${file.path} (${formatBytes(file.size)})`);
    });
    console.log('   Consider image optimization or WebP format.');
    console.log('');
  }
  
  if (totalSize > 10 * 1024 * 1024) { // > 10MB
    console.log('⚠️  Large total bundle size detected.');
    console.log('   Consider implementing more aggressive code splitting.');
    console.log('');
  }
  
  console.log('✅ Analysis complete!');
  console.log('');
  console.log('💡 Tips for optimization:');
  console.log('   - Use dynamic imports for large components');
  console.log('   - Optimize images with Next.js Image component');
  console.log('   - Remove unused dependencies');
  console.log('   - Use tree shaking for libraries');
  console.log('   - Consider using a CDN for static assets');
}

// Run the analysis
analyzeBuild();
