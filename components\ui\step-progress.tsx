"use client"

import * as React from "react"
import { motion } from "framer-motion"
import { cn } from "@/lib/utils"

interface StepProgressProps {
  steps: {
    label: string
    status: 'completed' | 'current' | 'upcoming'
  }[]
  currentStep: number
  progress: number
  className?: string
}

export function StepProgress({
  steps,
  currentStep,
  progress,
  className
}: StepProgressProps) {
  return (
    <div className={cn("w-full", className)}>
      {/* Step Labels */}
      <div className="flex justify-between mb-4">
        {steps.map((step, index) => (
          <div
            key={index}
            className="flex flex-col items-center"
          >
            <span
              className={cn(
                "text-xs sm:text-sm font-medium whitespace-nowrap",
                step.status === 'current' ? "text-primary" :
                step.status === 'completed' ? "text-primary/70" : "text-muted-foreground"
              )}
            >
              {step.label}
            </span>
          </div>
        ))}
      </div>

      {/* Simple Progress Bar */}
      <div className="relative h-1.5 bg-gray-200 dark:bg-gray-700 rounded-full overflow-hidden">
        <motion.div
          className="absolute top-0 left-0 h-full bg-primary rounded-full"
          initial={{ width: 0 }}
          animate={{ width: `${progress}%` }}
          transition={{ duration: 0.4, ease: "easeOut" }}
        />

        {/* Step Dots */}
        <div className="absolute top-0 left-0 w-full h-full flex justify-between items-center">
          {steps.map((step, index) => {
            const stepPosition = index / (steps.length - 1) * 100
            const isCompleted = stepPosition <= progress

            return (
              <motion.div
                key={index}
                className={cn(
                  "h-3 w-3 rounded-full border border-white dark:border-gray-800 shadow-sm",
                  step.status === 'current'
                    ? "bg-primary ring-2 ring-primary/30 ring-offset-1 ring-offset-background"
                    : isCompleted
                      ? "bg-primary"
                      : "bg-gray-200 dark:bg-gray-700"
                )}
                style={{
                  transform: `translateX(${index === 0 ? '50%' : index === steps.length - 1 ? '-50%' : '0'})`
                }}
                initial={{ scale: 0.8 }}
                animate={{ scale: step.status === 'current' ? 1.2 : 1 }}
                transition={{ duration: 0.3 }}
              />
            )
          })}
        </div>
      </div>


    </div>
  )
}
