'use server';

import { z } from 'zod';
import {
  clientInformationSchema,
  packageSelectionSchema,
  paymentVerificationSchema,
  calculateTotalPrice,
  calculateDepositAmount,
  PackageType,
} from '@/lib/mpesa/types';
import {
  createClientProject,
  getClientProjectByReferenceCode,
  recordPayment,
  verifyPayment,
  generateQuestionnaireAccess,
  updateClientProjectStatus,
} from '@/lib/mpesa/supabase-mpesa-fixed';

// Register a new client and create a project
export async function registerClient(formData: {
  client_name: string;
  client_email: string;
  client_phone: string;
  package_type: PackageType;
  additional_services: string[];
  maintenance_plan?: string;
}) {
  try {
    console.log('Server action: registerClient called with data:', JSON.stringify(formData));

    // Validate client information
    const validatedClientInfo = clientInformationSchema.parse({
      client_name: formData.client_name,
      client_email: formData.client_email,
      client_phone: formData.client_phone,
    });

    // Validate package selection
    const validatedPackageSelection = packageSelectionSchema.parse({
      package_type: formData.package_type,
      additional_services: formData.additional_services,
      maintenance_plan: formData.maintenance_plan,
    });

    // Create client project
    const clientProject = await createClientProject(
      validatedClientInfo.client_name,
      validatedClientInfo.client_email,
      validatedClientInfo.client_phone,
      validatedPackageSelection.package_type,
      validatedPackageSelection.additional_services,
      validatedPackageSelection.maintenance_plan
    );

    console.log('Server action: Client project created successfully:', JSON.stringify(clientProject));

    // Calculate total and deposit amounts
    const totalAmount = calculateTotalPrice(
      validatedPackageSelection.package_type,
      validatedPackageSelection.additional_services,
      validatedPackageSelection.maintenance_plan
    );
    const depositAmount = calculateDepositAmount(totalAmount);

    return {
      success: true,
      data: {
        client_project_id: clientProject.id,
        reference_code: clientProject.reference_code,
        total_amount: totalAmount,
        deposit_amount: depositAmount,
      },
    };
  } catch (error) {
    console.error('Error in registerClient:', error);
    if (error instanceof z.ZodError) {
      return {
        success: false,
        error: error.errors.map(e => e.message).join(', '),
      };
    }
    return {
      success: false,
      error: error instanceof Error ? error.message : 'An unexpected error occurred',
    };
  }
}

// Verify M-Pesa payment
export async function verifyMpesaPayment(formData: {
  reference_code: string;
  mpesa_code: string;
}) {
  try {
    console.log('Server action: verifyMpesaPayment called with data:', JSON.stringify(formData));

    // Validate inputs
    if (!formData.reference_code) {
      return {
        success: false,
        error: 'Reference code is required',
      };
    }

    if (!formData.mpesa_code) {
      return {
        success: false,
        error: 'M-Pesa code is required',
      };
    }

    // Validate M-Pesa code format
    try {
      const validatedPaymentData = paymentVerificationSchema.parse({
        mpesa_code: formData.mpesa_code,
      });

      // Get client project by reference code
      const clientProject = await getClientProjectByReferenceCode(formData.reference_code);
      if (!clientProject) {
        return {
          success: false,
          error: `Invalid reference code: ${formData.reference_code}. Please check and try again.`,
        };
      }

      console.log('Server action: Found client project:', JSON.stringify(clientProject));

      try {
        // Record the payment
        const payment = await recordPayment(
          clientProject.id,
          validatedPaymentData.mpesa_code,
          clientProject.deposit_amount,
          'deposit'
        );

        console.log('Server action: Payment recorded successfully:', JSON.stringify(payment));

        try {
          // For this implementation, we'll manually verify the payment
          // In a production environment, you would integrate with M-Pesa API to verify the payment
          const verifiedPayment = await verifyPayment(payment.id);

          console.log('Server action: Payment verified successfully:', JSON.stringify(verifiedPayment));

          try {
            // Generate questionnaire access token
            const questionnaireAccess = await generateQuestionnaireAccess(clientProject.id);

            console.log('Server action: Questionnaire access generated successfully:', JSON.stringify(questionnaireAccess));

            return {
              success: true,
              data: {
                client_project_id: clientProject.id,
                payment_id: verifiedPayment.id,
                questionnaire_access_token: questionnaireAccess.access_token,
              },
            };
          } catch (questionnaireError) {
            console.error('Error generating questionnaire access:', questionnaireError);
            return {
              success: false,
              error: `Payment verified, but failed to generate questionnaire access: ${questionnaireError instanceof Error ? questionnaireError.message : 'Unknown error'}`,
            };
          }
        } catch (verificationError) {
          console.error('Error verifying payment:', verificationError);
          return {
            success: false,
            error: `Failed to verify payment: ${verificationError instanceof Error ? verificationError.message : 'Unknown error'}`,
          };
        }
      } catch (paymentError) {
        console.error('Error recording payment:', paymentError);
        return {
          success: false,
          error: `Failed to record payment: ${paymentError instanceof Error ? paymentError.message : 'Unknown error'}`,
        };
      }
    } catch (validationError) {
      if (validationError instanceof z.ZodError) {
        return {
          success: false,
          error: `Invalid M-Pesa code: ${validationError.errors.map(e => e.message).join(', ')}`,
        };
      }
      return {
        success: false,
        error: `Invalid M-Pesa code: ${validationError instanceof Error ? validationError.message : 'Unknown validation error'}`,
      };
    }
  } catch (error) {
    console.error('Unexpected error in verifyMpesaPayment:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'An unexpected error occurred',
    };
  }
}

// Generate questionnaire access
export async function generateQuestionnaireAccessToken(formData: {
  client_project_id: string;
}) {
  try {
    console.log('Server action: generateQuestionnaireAccessToken called with data:', JSON.stringify(formData));

    // Generate questionnaire access token
    const questionnaireAccess = await generateQuestionnaireAccess(formData.client_project_id);

    console.log('Server action: Questionnaire access generated successfully:', JSON.stringify(questionnaireAccess));

    return {
      success: true,
      data: {
        questionnaire_access_token: questionnaireAccess.access_token,
      },
    };
  } catch (error) {
    console.error('Error in generateQuestionnaireAccessToken:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'An unexpected error occurred',
    };
  }
}

// Get project details by reference code
export async function getProjectDetailsByReferenceCode(referenceCode: string) {
  try {
    console.log('Server action: getProjectDetailsByReferenceCode called with reference code:', referenceCode);

    // Get client project by reference code
    const clientProject = await getClientProjectByReferenceCode(referenceCode);
    if (!clientProject) {
      return {
        success: false,
        error: 'Invalid reference code. Please check and try again.',
      };
    }

    console.log('Server action: Client project found:', JSON.stringify(clientProject));

    return {
      success: true,
      data: clientProject,
    };
  } catch (error) {
    console.error('Error in getProjectDetailsByReferenceCode:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'An unexpected error occurred',
    };
  }
}
