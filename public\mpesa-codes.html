<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>M-Pesa Test Codes Generator</title>
  <style>
    body {
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
      line-height: 1.6;
      color: #333;
      max-width: 800px;
      margin: 0 auto;
      padding: 20px;
    }
    h1 {
      color: #2563eb;
      margin-bottom: 1rem;
    }
    h2 {
      color: #4b5563;
      margin-top: 2rem;
      margin-bottom: 1rem;
    }
    .card {
      background-color: #fff;
      border-radius: 8px;
      box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
      padding: 20px;
      margin-bottom: 20px;
    }
    .code-grid {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
      gap: 10px;
      margin-top: 20px;
    }
    .code-item {
      background-color: #f3f4f6;
      border-radius: 4px;
      padding: 10px;
      font-family: monospace;
      font-size: 16px;
      display: flex;
      justify-content: space-between;
      align-items: center;
    }
    .code-item:hover {
      background-color: #e5e7eb;
    }
    button {
      background-color: #2563eb;
      color: white;
      border: none;
      padding: 10px 15px;
      border-radius: 4px;
      cursor: pointer;
      font-size: 16px;
      transition: background-color 0.2s;
    }
    button:hover {
      background-color: #1d4ed8;
    }
    .copy-btn {
      background-color: transparent;
      color: #4b5563;
      padding: 5px;
      font-size: 14px;
    }
    .copy-btn:hover {
      background-color: #d1d5db;
    }
    .success {
      background-color: #d1fae5;
      color: #065f46;
      padding: 10px;
      border-radius: 4px;
      margin-bottom: 20px;
      display: none;
    }
    .controls {
      display: flex;
      gap: 10px;
      margin-bottom: 20px;
    }
    input {
      padding: 10px;
      border: 1px solid #d1d5db;
      border-radius: 4px;
      font-size: 16px;
    }
    .loading {
      display: inline-block;
      width: 20px;
      height: 20px;
      border: 3px solid rgba(255,255,255,.3);
      border-radius: 50%;
      border-top-color: #fff;
      animation: spin 1s ease-in-out infinite;
      margin-left: 10px;
    }
    @keyframes spin {
      to { transform: rotate(360deg); }
    }
  </style>
</head>
<body>
  <h1>M-Pesa Test Codes Generator</h1>
  
  <div class="success" id="success-message">
    Code copied to clipboard!
  </div>
  
  <div class="card">
    <h2>Generate New Test Codes</h2>
    <p>Generate random M-Pesa confirmation codes for testing your payment verification system.</p>
    
    <div class="controls">
      <input type="number" id="code-count" min="5" max="50" value="10" placeholder="Number of codes">
      <button id="generate-btn">Generate Codes</button>
    </div>
    
    <div id="code-container" class="code-grid">
      <!-- Codes will be inserted here -->
      <div class="code-item">Loading codes...</div>
    </div>
  </div>
  
  <div class="card">
    <h2>How to Use These Codes</h2>
    <p>These codes simulate M-Pesa confirmation codes that you would receive after making a payment. To test your payment verification system:</p>
    <ol>
      <li>Copy one of the generated codes</li>
      <li>Go to your payment verification page</li>
      <li>Enter the reference code for your test project</li>
      <li>Paste the M-Pesa code into the verification form</li>
      <li>Submit the form to verify the payment</li>
    </ol>
    <p><strong>Note:</strong> These codes are randomly generated and are not real M-Pesa confirmation codes. They are only for testing purposes.</p>
    
    <div style="margin-top: 20px;">
      <a href="/mpesa-test.html">
        <button>Go to Test Page</button>
      </a>
    </div>
  </div>
  
  <script>
    // Function to show success message
    function showSuccess(message) {
      const successElement = document.getElementById('success-message');
      successElement.textContent = message;
      successElement.style.display = 'block';
      
      setTimeout(() => {
        successElement.style.display = 'none';
      }, 3000);
    }
    
    // Function to copy code to clipboard
    function copyToClipboard(code) {
      navigator.clipboard.writeText(code)
        .then(() => {
          showSuccess('Code copied to clipboard!');
        })
        .catch(err => {
          console.error('Failed to copy: ', err);
          showSuccess('Failed to copy code. Please try again.');
        });
    }
    
    // Function to generate codes
    async function generateCodes() {
      const codeCount = document.getElementById('code-count').value || 10;
      const generateBtn = document.getElementById('generate-btn');
      const codeContainer = document.getElementById('code-container');
      
      // Show loading state
      generateBtn.disabled = true;
      generateBtn.innerHTML = 'Generating... <span class="loading"></span>';
      codeContainer.innerHTML = '<div class="code-item">Loading codes...</div>';
      
      try {
        // Fetch codes from the API
        const response = await fetch(`/api/mpesa/generate-codes?count=${codeCount}`);
        const data = await response.json();
        
        if (data.success && data.data.codes) {
          // Clear container
          codeContainer.innerHTML = '';
          
          // Add each code to the container
          data.data.codes.forEach(code => {
            const codeItem = document.createElement('div');
            codeItem.className = 'code-item';
            codeItem.innerHTML = `
              <span>${code}</span>
              <button class="copy-btn" onclick="copyToClipboard('${code}')">Copy</button>
            `;
            codeContainer.appendChild(codeItem);
          });
        } else {
          codeContainer.innerHTML = '<div class="code-item">Failed to generate codes</div>';
        }
      } catch (error) {
        console.error('Error generating codes:', error);
        codeContainer.innerHTML = '<div class="code-item">Error generating codes</div>';
      } finally {
        // Reset button state
        generateBtn.disabled = false;
        generateBtn.innerHTML = 'Generate Codes';
      }
    }
    
    // Add event listener to generate button
    document.getElementById('generate-btn').addEventListener('click', generateCodes);
    
    // Generate codes on page load
    document.addEventListener('DOMContentLoaded', generateCodes);
    
    // Make copyToClipboard function global
    window.copyToClipboard = copyToClipboard;
  </script>
</body>
</html>
