"use client"

import Link from "next/link"
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>ed<PERSON>, Mail } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import ProjectCard from "@/components/project-card"
import SkillBadge from "@/components/skill-badge"
import ContactForm from "@/components/contact-form"
import { motion } from "framer-motion"
import { useEffect, useState } from "react"
import { useTheme } from "next-themes"
import ParticlesBackground from "@/components/particles-background"
import AnimatedButton from "@/components/animated-button"
import { useTextAnimation } from "@/hooks/useTextAnimation"
import { useSmoothScroll } from "@/hooks/useSmoothScroll"
import ScrollToTop from "@/components/scroll-to-top"
import MobileMenu from "@/components/mobile-menu"

export default function Home() {
  const { setTheme, resolvedTheme } = useTheme()
  const [mounted, setMounted] = useState(false)
  const { scrollToElement } = useSmoothScroll({
    offset: 80, // Offset for header height
    duration: 1000, // Longer duration for smoother effect
  })

  // Text animation for the name
  const fullName = "Zachary Odero";
  const { displayedText: animatedName, isComplete } = useTextAnimation({
    text: fullName,
    typingSpeed: 80,
    startDelay: 1000
  })

  // Handle initial theme based on system preference
  useEffect(() => {
    const isDarkMode = window.matchMedia && window.matchMedia("(prefers-color-scheme: dark)").matches
    if (isDarkMode) {
      document.documentElement.classList.add("dark")
    }
    setMounted(true)
  }, [])

  const toggleTheme = () => {
    setTheme(resolvedTheme === 'dark' ? 'light' : 'dark')
  }

  return (
    <div className="min-h-screen bg-white dark:bg-gray-950 transition-colors duration-300">
      {/* Scroll to top button */}
      <ScrollToTop />

      {/* Navigation */}
      <motion.header
        className="sticky top-0 z-50 bg-white/90 dark:bg-gray-950/90 backdrop-blur-sm shadow-sm"
        initial={{ y: -100 }}
        animate={{ y: 0 }}
        transition={{ type: 'spring', stiffness: 300, damping: 30 }}
      >
        <div className="container mx-auto px-4 py-4">
          <nav className="flex items-center justify-between">
            <motion.div
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
            >
              <Link href="/" className="text-xl font-bold text-black dark:text-white">
                Portfolio
              </Link>
            </motion.div>

            {/* Desktop Navigation */}
            <div className="hidden md:flex items-center gap-6">
              <motion.a
                href="#about"
                onClick={(e) => {
                  e.preventDefault();
                  scrollToElement('about');
                }}
                className="text-sm font-medium text-gray-600 hover:text-blue-600 dark:text-gray-300 dark:hover:text-blue-400 cursor-pointer"
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
              >
                About
              </motion.a>
              <motion.a
                href="#projects"
                onClick={(e) => {
                  e.preventDefault();
                  scrollToElement('projects');
                }}
                className="text-sm font-medium text-gray-600 hover:text-blue-600 dark:text-gray-300 dark:hover:text-blue-400 cursor-pointer"
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
              >
                Projects
              </motion.a>
              <motion.a
                href="#skills"
                onClick={(e) => {
                  e.preventDefault();
                  scrollToElement('skills');
                }}
                className="text-sm font-medium text-gray-600 hover:text-blue-600 dark:text-gray-300 dark:hover:text-blue-400 cursor-pointer"
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
              >
                Skills
              </motion.a>
              <motion.a
                href="#contact"
                onClick={(e) => {
                  e.preventDefault();
                  scrollToElement('contact');
                }}
                className="text-sm font-medium text-gray-600 hover:text-blue-600 dark:text-gray-300 dark:hover:text-blue-400 cursor-pointer"
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
              >
                Contact
              </motion.a>
            </div>

            {/* Mobile Menu */}
            <div className="flex items-center gap-3">
              <button
                className="rounded-md p-2 bg-gray-100 text-gray-600 hover:text-blue-600 dark:bg-gray-800 dark:text-gray-300 dark:hover:text-blue-400 transition-colors"
                onClick={toggleTheme}
                aria-label="Toggle dark mode"
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="20"
                  height="20"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  className="hidden dark:block" // Sun icon (shows in dark mode)
                >
                  <circle cx="12" cy="12" r="5"></circle>
                  <line x1="12" y1="1" x2="12" y2="3"></line>
                  <line x1="12" y1="21" x2="12" y2="23"></line>
                  <line x1="4.22" y1="4.22" x2="5.64" y2="5.64"></line>
                  <line x1="18.36" y1="18.36" x2="19.78" y2="19.78"></line>
                  <line x1="1" y1="12" x2="3" y2="12"></line>
                  <line x1="21" y1="12" x2="23" y2="12"></line>
                  <line x1="4.22" y1="19.78" x2="5.64" y2="18.36"></line>
                  <line x1="18.36" y1="5.64" x2="19.78" y2="4.22"></line>
                </svg>
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="20"
                  height="20"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  className="block dark:hidden" // Moon icon (shows in light mode)
                >
                  <path d="M21 12.79A9 9 0 1 1 11.21 3 7 7 0 0 0 21 12.79z"></path>
                </svg>
              </button>
              <MobileMenu scrollToElement={scrollToElement} />
            </div>
          </nav>
        </div>
      </motion.header>

      <main>
        {/* Hero Section */}
        <section className="relative container mx-auto px-4 py-12 md:py-20 overflow-hidden min-h-[80vh] flex items-center">
          {/* Particle background */}
          <div className="absolute inset-0 z-0">
            <ParticlesBackground />
          </div>

          {/* Content */}
          <div className="relative z-10 w-full">
            <motion.div
              className="flex flex-col items-start gap-4 md:gap-6 w-full md:w-2/3"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ duration: 0.8 }}
            >
              {/* Animated heading with staggered children */}
              <motion.div
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ duration: 0.5, delay: 0.2 }}
                className="w-full"
              >
                <motion.h1
                  className="text-4xl sm:text-5xl font-bold text-black dark:text-white md:text-6xl leading-tight"
                  initial={{ y: 20 }}
                  animate={{ y: 0 }}
                  transition={{
                    type: "spring",
                    stiffness: 100,
                    damping: 15
                  }}
                >
                  Hi, I'm{" "}
                  <motion.span
                    className="text-blue-600 dark:text-blue-400 inline-block"
                    initial={{ opacity: 0 }}
                    animate={{
                      opacity: 1,
                      transition: { delay: 0.5, duration: 0.5 }
                    }}
                    whileHover={{
                      scale: 1.05,
                      color: "#3b82f6",
                      transition: { duration: 0.2 }
                    }}
                  >
                    {animatedName || fullName}
                    {!isComplete && animatedName && (
                      <motion.span
                        initial={{ opacity: 0 }}
                        animate={{ opacity: [0, 1, 0] }}
                        transition={{
                          repeat: Infinity,
                          duration: 1,
                          repeatDelay: 0.2
                        }}
                        className="ml-1 inline-block"
                      >
                        |
                      </motion.span>
                    )}
                  </motion.span>
                </motion.h1>
              </motion.div>

              {/* Animated paragraph */}
              <motion.p
                className="text-lg sm:text-xl text-gray-600 dark:text-gray-300"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{
                  duration: 0.5,
                  delay: 0.8,
                  ease: "easeOut"
                }}
              >
                A passionate full-stack developer specializing in creating elegant, user-friendly web applications.
              </motion.p>

              {/* Animated button */}
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: 1.2 }}
                className="w-full sm:w-auto"
              >
                <AnimatedButton
                  className="mt-4 w-full sm:w-auto bg-blue-600 hover:bg-blue-700 dark:bg-blue-500 dark:hover:bg-blue-600"
                  onClick={() => scrollToElement("projects")}
                >
                  View My Work <ArrowRight className="ml-2 h-4 w-4" />
                </AnimatedButton>
              </motion.div>
            </motion.div>
          </div>

          {/* Decorative elements */}
          <motion.div
            className="absolute bottom-0 right-0 w-48 md:w-64 h-48 md:h-64 bg-blue-500 rounded-full filter blur-3xl opacity-10 dark:opacity-20 z-0"
            animate={{
              scale: [1, 1.2, 1],
              opacity: [0.1, 0.15, 0.1],
            }}
            transition={{
              duration: 8,
              repeat: Infinity,
              repeatType: "reverse"
            }}
          />

          <motion.div
            className="absolute top-20 right-10 md:right-20 w-24 md:w-32 h-24 md:h-32 bg-purple-500 rounded-full filter blur-3xl opacity-10 dark:opacity-15 z-0"
            animate={{
              scale: [1, 1.3, 1],
              opacity: [0.1, 0.2, 0.1],
            }}
            transition={{
              duration: 6,
              repeat: Infinity,
              repeatType: "reverse",
              delay: 1
            }}
          />
        </section>

        {/* About Section */}
        <section id="about" className="bg-gray-50 dark:bg-gray-900 py-12 md:py-20 transition-colors duration-300">
          <div className="container mx-auto px-4">
            <motion.h2
              className="mb-8 md:mb-12 text-2xl md:text-3xl font-bold text-black dark:text-white text-center md:text-left"
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.5 }}
            >
              About Me
            </motion.h2>
            <div className="flex flex-col gap-8 md:flex-row">
              <motion.div
                className="w-full md:w-1/3 max-w-[250px] mx-auto md:mx-0"
                initial={{ opacity: 0, x: -50 }}
                whileInView={{ opacity: 1, x: 0 }}
                viewport={{ once: true }}
                transition={{ duration: 0.6 }}
              >
                <motion.div
                  className="aspect-square w-full overflow-hidden rounded-md bg-gray-200 dark:bg-gray-800 shadow-md"
                  whileHover={{
                    scale: 1.03,
                    boxShadow: "0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 8px 10px -6px rgba(0, 0, 0, 0.1)"
                  }}
                  transition={{ duration: 0.3 }}
                >
                  <img
                    src="/images/projects/profile.jpeg"
                    alt="Profile"
                    className="h-full w-full object-cover"
                  />
                </motion.div>
              </motion.div>
              <motion.div
                className="w-full md:w-2/3"
                initial={{ opacity: 0, x: 50 }}
                whileInView={{ opacity: 1, x: 0 }}
                viewport={{ once: true }}
                transition={{ duration: 0.6 }}
              >
                <motion.p
                  className="mb-4 text-gray-600 dark:text-gray-300 text-sm md:text-base p-2 rounded-md"
                  whileHover={{
                    backgroundColor: "rgba(59, 130, 246, 0.05)",
                    x: 5
                  }}
                  transition={{ duration: 0.2 }}
                >
                  I'm a full-stack developer with over 5 years of experience building web applications. I specialize in
                  JavaScript, React, and Node.js, with a strong focus on creating intuitive user experiences.
                </motion.p>
                <motion.p
                  className="mb-4 text-gray-600 dark:text-gray-300 text-sm md:text-base p-2 rounded-md"
                  whileHover={{
                    backgroundColor: "rgba(59, 130, 246, 0.05)",
                    x: 5
                  }}
                  transition={{ duration: 0.2 }}
                >
                  My journey in web development began during college, where I discovered my passion for creating digital
                  solutions. Since then, I've worked with startups and established companies to bring their visions to
                  life.
                </motion.p>
                <motion.p
                  className="text-gray-600 dark:text-gray-300 text-sm md:text-base p-2 rounded-md"
                  whileHover={{
                    backgroundColor: "rgba(59, 130, 246, 0.05)",
                    x: 5
                  }}
                  transition={{ duration: 0.2 }}
                >
                  When I'm not coding, you can find me hiking, reading, or experimenting with new technologies.
                </motion.p>
                <Button
                  variant="outline"
                  className="mt-6 w-full sm:w-auto border-blue-600 text-blue-600 hover:bg-blue-600 hover:text-white dark:border-blue-400 dark:text-blue-400 dark:hover:bg-blue-500 dark:hover:text-white"
                >
                  Download Resume
                </Button>
              </motion.div>
            </div>
          </div>
        </section>

        {/* Projects Section */}
        <section id="projects" className="py-12 md:py-20 dark:bg-gray-950 transition-colors duration-300">
          <div className="container mx-auto px-4">
            <motion.h2
              className="mb-8 md:mb-12 text-2xl md:text-3xl font-bold text-black dark:text-white text-center md:text-left"
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.5 }}
            >
              My Projects
            </motion.h2>
            <motion.div
              className="grid gap-6 sm:grid-cols-1 md:grid-cols-2 lg:grid-cols-3"
              initial={{ opacity: 0 }}
              whileInView={{ opacity: 1 }}
              viewport={{ once: true }}
              transition={{ staggerChildren: 0.1 }}
            >
              <ProjectCard
                title="E-commerce Platform"
                description="A full-featured online store built with Next.js and Stripe integration."
                tags={["Next.js", "Stripe", "Tailwind CSS"]}
                imageUrl="/images/projects/ecommerce-neon.jpeg"
                githubUrl="https://github.com/zacharyodero/ecommerce-platform"
                demoUrl="https://ecommerce-platform-demo.vercel.app"
              />
              <ProjectCard
                title="Task Management App"
                description="A productivity application with real-time updates and team collaboration features."
                tags={["React", "Firebase", "Material UI"]}
                imageUrl="/images/projects/task-management.jpeg"
                githubUrl="https://github.com/zacharyodero/task-management-app"
                demoUrl="https://task-management-app-demo.vercel.app"
              />
              <ProjectCard
                title="Portfolio Website"
                description="A minimalist portfolio website showcasing my work and skills."
                tags={["Next.js", "Tailwind CSS", "Framer Motion"]}
                imageUrl="/images/projects/portfolio.jpeg"
                githubUrl="https://github.com/zacharyodero/my-nextjs-portfolio"
                demoUrl="https://zacharyodero.com"
              />
            </motion.div>
          </div>
        </section>

        {/* Skills Section */}
        <section id="skills" className="bg-gray-50 dark:bg-gray-900 py-12 md:py-20 transition-colors duration-300 overflow-hidden">
          <div className="container mx-auto px-4">
            <motion.h2
              className="mb-8 md:mb-12 text-2xl md:text-3xl font-bold text-black dark:text-white text-center md:text-left"
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.5 }}
            >
              Skills
            </motion.h2>

            {/* Skills container with continuous sliding animation */}
            <div className="relative">
              {/* First row of skills - continuous left to right */}
              <div className="overflow-hidden mb-6 py-2">
                <motion.div
                  className="flex gap-4 md:gap-6"
                  animate={{
                    x: ["-15%", "0%", "-15%"]
                  }}
                  transition={{
                    repeat: Infinity,
                    duration: 30,
                    ease: "linear",
                  }}
                >
                  {/* Double the skills to create seamless loop */}
                  {[...Array(2)].flatMap((_, i) => [
                    "JavaScript",
                    "TypeScript",
                    "React",
                    "Next.js",
                    "Node.js",
                    "Express",
                    "MongoDB",
                  ].map((skill) => (
                    <SkillBadge key={`${skill}-${i}`} name={skill} />
                  )))}
                </motion.div>
              </div>

              {/* Second row of skills - continuous right to left */}
              <div className="overflow-hidden py-2">
                <motion.div
                  className="flex gap-4 md:gap-6"
                  animate={{
                    x: ["0%", "-15%", "0%"]
                  }}
                  transition={{
                    repeat: Infinity,
                    duration: 30,
                    ease: "linear",
                  }}
                >
                  {/* Double the skills to create seamless loop */}
                  {[...Array(2)].flatMap((_, i) => [
                    "PostgreSQL",
                    "Tailwind CSS",
                    "Git",
                    "Docker",
                    "AWS",
                    "JavaScript",
                    "TypeScript",
                  ].map((skill) => (
                    <SkillBadge key={`${skill}-${i}`} name={skill} />
                  )))}
                </motion.div>
              </div>
            </div>
          </div>
        </section>

        {/* Contact Section */}
        <section id="contact" className="py-12 md:py-20 dark:bg-gray-950 transition-colors duration-300">
          <div className="container mx-auto px-4">
            <motion.h2
              className="mb-8 md:mb-12 text-2xl md:text-3xl font-bold text-black dark:text-white text-center md:text-left"
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.5 }}
            >
              Get In Touch
            </motion.h2>
            <div className="flex flex-col gap-8 md:flex-row">
              <motion.div
                className="w-full md:w-1/2"
                initial={{ opacity: 0, x: -50 }}
                whileInView={{ opacity: 1, x: 0 }}
                viewport={{ once: true }}
                transition={{ duration: 0.6 }}
              >
                <motion.p
                  className="mb-6 text-gray-600 dark:text-gray-300 text-sm md:text-base p-2 rounded-md"
                  whileHover={{
                    backgroundColor: "rgba(59, 130, 246, 0.05)",
                    x: 5
                  }}
                  transition={{ duration: 0.2 }}
                >
                  I'm always open to discussing new projects, creative ideas, or opportunities to be part of your
                  vision.
                </motion.p>
                <div className="flex flex-col gap-4">
                  <motion.div
                    className="flex items-center gap-3 p-2 rounded-md"
                    whileHover={{
                      backgroundColor: "rgba(59, 130, 246, 0.05)",
                      x: 5
                    }}
                    transition={{ duration: 0.2 }}
                  >
                    <motion.div
                      whileHover={{ rotate: 15, scale: 1.1 }}
                      transition={{ duration: 0.2 }}
                    >
                      <Mail className="h-5 w-5 text-blue-600 dark:text-blue-400 flex-shrink-0" />
                    </motion.div>
                    <span className="text-gray-600 dark:text-gray-300 text-sm md:text-base break-all"><EMAIL></span>
                  </motion.div>
                  <motion.div
                    className="flex items-center gap-3 p-2 rounded-md"
                    whileHover={{
                      backgroundColor: "rgba(59, 130, 246, 0.05)",
                      x: 5
                    }}
                    transition={{ duration: 0.2 }}
                  >
                    <motion.div
                      whileHover={{ rotate: 15, scale: 1.1 }}
                      transition={{ duration: 0.2 }}
                    >
                      <Github className="h-5 w-5 text-blue-600 dark:text-blue-400 flex-shrink-0" />
                    </motion.div>
                    <a
                      href="https://github.com/zacharyodero"
                      target="_blank"
                      rel="noopener noreferrer"
                      className="text-gray-600 hover:text-blue-600 dark:text-gray-300 dark:hover:text-blue-400 text-sm md:text-base"
                    >
                      github.com/zacharyodero
                    </a>
                  </motion.div>
                  <motion.div
                    className="flex items-center gap-3 p-2 rounded-md"
                    whileHover={{
                      backgroundColor: "rgba(59, 130, 246, 0.05)",
                      x: 5
                    }}
                    transition={{ duration: 0.2 }}
                  >
                    <motion.div
                      whileHover={{ rotate: 15, scale: 1.1 }}
                      transition={{ duration: 0.2 }}
                    >
                      <Linkedin className="h-5 w-5 text-blue-600 dark:text-blue-400 flex-shrink-0" />
                    </motion.div>
                    <a
                      href="https://www.linkedin.com/in/zacharyodero"
                      target="_blank"
                      rel="noopener noreferrer"
                      className="text-gray-600 hover:text-blue-600 dark:text-gray-300 dark:hover:text-blue-400 text-sm md:text-base"
                    >
                      linkedin.com/in/zacharyodero
                    </a>
                  </motion.div>
                </div>
              </motion.div>
              <div className="w-full md:w-1/2">
                <ContactForm />
              </div>
            </div>
          </div>
        </section>
      </main>

      {/* Footer */}
      <footer className="bg-black py-6 md:py-8 text-white">
        <div className="container mx-auto px-4">
          <div className="flex flex-col items-center justify-between gap-4 md:flex-row">
            <p className="text-sm md:text-base text-center md:text-left">© 2025 Zachary Odero. All rights reserved.</p>
            <div className="flex gap-6">
              <a href="https://github.com/zacharyodero" target="_blank" rel="noopener noreferrer" className="hover:text-blue-400 transition-colors">
                <Github className="h-5 w-5" />
              </a>
              <a href="https://www.linkedin.com/in/zacharyodero" target="_blank" rel="noopener noreferrer" className="hover:text-blue-400 transition-colors">
                <Linkedin className="h-5 w-5" />
              </a>
              <a href="mailto:<EMAIL>" className="hover:text-blue-400 transition-colors">
                <Mail className="h-5 w-5" />
              </a>
            </div>
          </div>
        </div>
      </footer>
    </div>
  )
}
