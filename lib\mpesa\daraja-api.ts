// Types for Daraja API
export interface DarajaAuthResponse {
  access_token: string;
  expires_in: string;
}

export interface STKPushRequest {
  BusinessShortCode: string;
  Password: string;
  Timestamp: string;
  TransactionType: 'CustomerPayBillOnline' | 'CustomerBuyGoodsOnline';
  Amount: string;
  PartyA: string;
  PartyB: string;
  PhoneNumber: string;
  CallBackURL: string;
  AccountReference: string;
  TransactionDesc: string;
}

export interface STKPushResponse {
  MerchantRequestID: string;
  CheckoutRequestID: string;
  ResponseCode: string;
  ResponseDescription: string;
  CustomerMessage: string;
}

export interface STKPushCallback {
  Body: {
    stkCallback: {
      MerchantRequestID: string;
      CheckoutRequestID: string;
      ResultCode: number;
      ResultDesc: string;
      CallbackMetadata?: {
        Item: Array<{
          Name: string;
          Value?: string | number;
        }>;
      };
    };
  };
}

export interface STKPushQueryRequest {
  BusinessShortCode: string;
  Password: string;
  Timestamp: string;
  CheckoutRequestID: string;
}

export interface STKPushQueryResponse {
  ResponseCode: string;
  ResponseDescription: string;
  MerchantRequestID: string;
  CheckoutRequestID: string;
  ResultCode: string;
  ResultDesc: string;
}

// Environment configuration
const isSandbox = process.env.MPESA_ENVIRONMENT === 'sandbox';
const baseUrl = isSandbox
  ? 'https://sandbox.safaricom.co.ke'
  : 'https://api.safaricom.co.ke';

// Credentials
const consumerKey = process.env.MPESA_CONSUMER_KEY || '';
const consumerSecret = process.env.MPESA_CONSUMER_SECRET || '';
const shortCode = process.env.MPESA_SHORTCODE || '';
const passkey = process.env.MPESA_PASSKEY || '';

// Generate base64 encoded auth string
const getAuthString = (): string => {
  const auth = `${consumerKey}:${consumerSecret}`;
  if (typeof Buffer !== 'undefined') {
    return Buffer.from(auth).toString('base64');
  }
  // For environments where Buffer is not available
  return btoa(auth);
};

// Generate timestamp in the format YYYYMMDDHHmmss
export const generateTimestamp = (): string => {
  const date = new Date();
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const day = String(date.getDate()).padStart(2, '0');
  const hours = String(date.getHours()).padStart(2, '0');
  const minutes = String(date.getMinutes()).padStart(2, '0');
  const seconds = String(date.getSeconds()).padStart(2, '0');

  return `${year}${month}${day}${hours}${minutes}${seconds}`;
};

// Generate password for STK Push
export const generatePassword = (timestamp: string): string => {
  const password = `${shortCode}${passkey}${timestamp}`;
  if (typeof Buffer !== 'undefined') {
    return Buffer.from(password).toString('base64');
  }
  // For environments where Buffer is not available
  return btoa(password);
};

// Get OAuth token
export const getOAuthToken = async (): Promise<string> => {
  try {
    console.log('Getting OAuth token...');
    console.log('Base URL:', baseUrl);
    console.log('Consumer Key exists:', !!consumerKey);
    console.log('Consumer Secret exists:', !!consumerSecret);

    const authString = getAuthString();
    console.log('Auth string generated (first 10 chars):', authString.substring(0, 10) + '...');

    const url = `${baseUrl}/oauth/v1/generate?grant_type=client_credentials`;
    console.log('OAuth URL:', url);

    const response = await fetch(url, {
      method: 'GET',
      headers: {
        Authorization: `Basic ${authString}`,
      },
    });

    console.log('OAuth response status:', response.status);

    if (!response.ok) {
      const errorText = await response.text();
      console.error('OAuth error response:', errorText);
      throw new Error(`HTTP error! Status: ${response.status}, Response: ${errorText}`);
    }

    const data = await response.json() as DarajaAuthResponse;
    console.log('OAuth token received successfully');
    return data.access_token;
  } catch (error) {
    console.error('Error getting OAuth token:', error);
    if (error instanceof Error) {
      console.error('Error message:', error.message);
      console.error('Error stack:', error.stack);
    }
    throw new Error('Failed to get OAuth token');
  }
};

// Initiate STK Push
export const initiateSTKPush = async (
  phoneNumber: string,
  amount: number,
  accountReference: string,
  transactionDesc: string
): Promise<STKPushResponse> => {
  try {
    console.log('Initiating STK Push...');
    console.log('Input parameters:', { phoneNumber, amount, accountReference, transactionDesc });
    console.log('Shortcode exists:', !!shortCode);
    console.log('Passkey exists:', !!passkey);

    // Get OAuth token
    console.log('Getting OAuth token...');
    const token = await getOAuthToken();
    console.log('OAuth token received (first 10 chars):', token.substring(0, 10) + '...');

    // Generate timestamp
    const timestamp = generateTimestamp();
    console.log('Generated timestamp:', timestamp);

    // Generate password
    const password = generatePassword(timestamp);
    console.log('Generated password (first 10 chars):', password.substring(0, 10) + '...');

    // Format phone number (remove leading 0 and add country code if needed)
    const formattedPhoneNumber = phoneNumber.startsWith('0')
      ? `254${phoneNumber.substring(1)}`
      : phoneNumber;
    console.log('Formatted phone number:', formattedPhoneNumber);

    // Prepare request body
    const requestBody: STKPushRequest = {
      BusinessShortCode: shortCode,
      Password: password,
      Timestamp: timestamp,
      TransactionType: 'CustomerPayBillOnline',
      Amount: amount.toString(),
      PartyA: formattedPhoneNumber,
      PartyB: shortCode,
      PhoneNumber: formattedPhoneNumber,
      CallBackURL: `${process.env.NEXT_PUBLIC_APP_URL}/api/mpesa/callback`,
      AccountReference: accountReference,
      TransactionDesc: transactionDesc,
    };

    console.log('STK Push request body:', JSON.stringify(requestBody));

    const url = `${baseUrl}/mpesa/stkpush/v1/processrequest`;
    console.log('STK Push URL:', url);

    // Make API request
    console.log('Sending STK Push request...');
    const response = await fetch(url, {
      method: 'POST',
      headers: {
        Authorization: `Bearer ${token}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(requestBody),
    });

    console.log('STK Push response status:', response.status);

    if (!response.ok) {
      const errorText = await response.text();
      console.error('STK Push error response:', errorText);
      throw new Error(`HTTP error! Status: ${response.status}, Response: ${errorText}`);
    }

    const data = await response.json() as STKPushResponse;
    console.log('STK Push response data:', JSON.stringify(data));
    return data;
  } catch (error) {
    console.error('Error initiating STK Push:', error);
    if (error instanceof Error) {
      console.error('Error message:', error.message);
      console.error('Error stack:', error.stack);
    }
    throw new Error('Failed to initiate STK Push');
  }
};

// Query STK Push status
export const querySTKPushStatus = async (checkoutRequestId: string): Promise<STKPushQueryResponse> => {
  try {
    // Get OAuth token
    const token = await getOAuthToken();

    // Generate timestamp
    const timestamp = generateTimestamp();

    // Generate password
    const password = generatePassword(timestamp);

    // Prepare request body
    const requestBody: STKPushQueryRequest = {
      BusinessShortCode: shortCode,
      Password: password,
      Timestamp: timestamp,
      CheckoutRequestID: checkoutRequestId,
    };

    // Make API request
    const response = await fetch(
      `${baseUrl}/mpesa/stkpushquery/v1/query`,
      {
        method: 'POST',
        headers: {
          Authorization: `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(requestBody),
      }
    );

    if (!response.ok) {
      throw new Error(`HTTP error! Status: ${response.status}`);
    }

    const data = await response.json() as STKPushQueryResponse;
    return data;
  } catch (error) {
    console.error('Error querying STK Push status:', error);
    throw new Error('Failed to query STK Push status');
  }
};
