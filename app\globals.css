@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --foreground-rgb: 0, 0, 0;
  --background-rgb: 255, 255, 255;
  --background: 0 0% 100%;
  --foreground: 222.2 84% 4.9%;
  --card: 0 0% 100%;
  --card-foreground: 222.2 84% 4.9%;
  --popover: 0 0% 100%;
  --popover-foreground: 222.2 84% 4.9%;
  --primary: 221.2 83.2% 53.3%;
  --primary-foreground: 210 40% 98%;
  --secondary: 210 40% 96.1%;
  --secondary-foreground: 222.2 47.4% 11.2%;
  --muted: 210 40% 96.1%;
  --muted-foreground: 215.4 16.3% 46.9%;
  --accent: 210 40% 96.1%;
  --accent-foreground: 222.2 47.4% 11.2%;
  --destructive: 0 84.2% 60.2%;
  --destructive-foreground: 210 40% 98%;
  --border: 214.3 31.8% 91.4%;
  --input: 214.3 31.8% 91.4%;
  --ring: 221.2 83.2% 53.3%;
  --radius: 0.5rem;
}

.dark {
  --foreground-rgb: 255, 255, 255;
  --background-rgb: 10, 10, 10;
  --background: 222.2 84% 4.9%;
  --foreground: 210 40% 98%;
  --card: 222.2 84% 4.9%;
  --card-foreground: 210 40% 98%;
  --popover: 222.2 84% 4.9%;
  --popover-foreground: 210 40% 98%;
  --primary: 217.2 91.2% 59.8%;
  --primary-foreground: 222.2 47.4% 11.2%;
  --secondary: 217.2 32.6% 17.5%;
  --secondary-foreground: 210 40% 98%;
  --muted: 217.2 32.6% 17.5%;
  --muted-foreground: 215 20.2% 65.1%;
  --accent: 217.2 32.6% 17.5%;
  --accent-foreground: 210 40% 98%;
  --destructive: 0 62.8% 30.6%;
  --destructive-foreground: 210 40% 98%;
  --border: 217.2 32.6% 17.5%;
  --input: 217.2 32.6% 17.5%;
  --ring: 224.3 76.3% 48%;
}

body {
  color: rgb(var(--foreground-rgb));
  background: rgb(var(--background-rgb));
  font-family: Arial, Helvetica, sans-serif;
}

@layer utilities {
  .text-balance {
    text-wrap: balance;
  }

  /* Mobile-friendly utilities */
  .touch-manipulation {
    touch-action: manipulation;
    -webkit-tap-highlight-color: transparent;
  }

  /* Improved mobile button styles */
  @media (max-width: 640px) {
    .touch-manipulation {
      min-height: 44px;
    }
  }

  /* Mobile-optimized form controls */
  .mobile-input {
    @apply h-12 text-base px-4;
  }

  .mobile-button {
    @apply h-12 text-base min-w-[120px];
  }

  .mobile-card {
    @apply p-4 sm:p-6;
  }

  .mobile-container {
    @apply px-4 sm:px-6 md:px-8;
  }

  .mobile-spacing {
    @apply space-y-4 sm:space-y-6 md:space-y-8;
  }

  .mobile-touch-target {
    @apply min-h-[44px] min-w-[44px];
  }

  /* Mobile menu specific optimizations */
  .mobile-menu-grid {
    display: grid;
    grid-template-columns: repeat(2, minmax(0, 1fr));
    gap: 0.75rem;
    padding: 1rem;
    overflow-y: auto;
    flex: 1;
  }

  .mobile-menu-item {
    min-height: 100px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 1rem;
    border-radius: 0.75rem;
    cursor: pointer;
    user-select: none;
    -webkit-tap-highlight-color: transparent;
  }

  /* Ensure mobile menu doesn't get squeezed */
  .mobile-menu-container {
    max-height: 80vh;
    min-height: 400px;
    display: flex;
    flex-direction: column;
  }

  /* Mobile menu responsive adjustments */
  @media (max-width: 480px) {
    .mobile-menu-item {
      min-height: 90px;
      padding: 0.75rem;
    }

    .mobile-menu-grid {
      gap: 0.5rem;
      padding: 0.75rem;
    }
  }

  /* Prevent transform conflicts with Framer Motion */
  .motion-safe {
    transform: none;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  html {
    scroll-behavior: smooth;
    scroll-padding-top: 80px; /* Account for sticky header */
  }

  body {
    @apply bg-background text-foreground;
    /* Optimize scrolling performance */
    -webkit-overflow-scrolling: touch;
    overscroll-behavior: contain;
  }

  /* Performance optimizations for smooth scrolling - Applied selectively */
  .hardware-accelerated {
    /* Enable hardware acceleration for transforms */
    -webkit-transform: translateZ(0);
    -moz-transform: translateZ(0);
    -ms-transform: translateZ(0);
    -o-transform: translateZ(0);
    transform: translateZ(0);

    /* Optimize repaints */
    -webkit-backface-visibility: hidden;
    backface-visibility: hidden;
    -webkit-perspective: 1000;
    perspective: 1000;
  }

  /* Optimize elements that will be animated */
  .will-animate {
    will-change: transform, opacity;
  }

  /* Smooth transitions for theme changes */
  .theme-transition {
    transition: background-color 0.3s ease, color 0.3s ease,
      border-color 0.3s ease;
  }

  /* Additional scroll optimizations */
  .scroll-smooth {
    scroll-behavior: smooth;
  }

  /* Optimize sticky elements */
  .sticky {
    position: -webkit-sticky;
    position: sticky;
    will-change: transform;
  }

  /* Reduce motion for users who prefer it */
  @media (prefers-reduced-motion: reduce) {
    html {
      scroll-behavior: auto;
    }

    *,
    *::before,
    *::after {
      animation-duration: 0.01ms !important;
      animation-iteration-count: 1 !important;
      transition-duration: 0.01ms !important;
    }
  }
}

/* Custom animations */
@keyframes float {
  0% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
  100% {
    transform: translateY(0px);
  }
}

@keyframes slideInFromTop {
  0% {
    transform: translateY(-100%);
    opacity: 0;
  }
  100% {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes slideOutToTop {
  0% {
    transform: translateY(0);
    opacity: 1;
  }
  100% {
    transform: translateY(-100%);
    opacity: 0;
  }
}

@keyframes fadeInScale {
  0% {
    opacity: 0;
    transform: scale(0.9);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes pulse-light {
  0%,
  100% {
    box-shadow: 0 0 15px 5px rgba(59, 130, 246, 0.2);
  }
  50% {
    box-shadow: 0 0 25px 10px rgba(59, 130, 246, 0.3);
  }
}

@keyframes pulse-dark {
  0%,
  100% {
    box-shadow: 0 0 15px 5px rgba(96, 165, 250, 0.3);
  }
  50% {
    box-shadow: 0 0 25px 10px rgba(96, 165, 250, 0.5);
  }
}

.animate-float {
  animation: float 4s ease-in-out infinite;
}

.animate-slide-in-top {
  animation: slideInFromTop 0.5s cubic-bezier(0.4, 0, 0.2, 1) forwards;
}

.animate-slide-out-top {
  animation: slideOutToTop 0.3s cubic-bezier(0.4, 0, 0.2, 1) forwards;
}

.animate-fade-in-scale {
  animation: fadeInScale 0.3s cubic-bezier(0.4, 0, 0.2, 1) forwards;
}

.light .animate-pulse-glow {
  animation: pulse-light 4s ease-in-out infinite;
}

.dark .animate-pulse-glow {
  animation: pulse-dark 4s ease-in-out infinite;
}
