import type React from "react"
import { <PERSON>ada<PERSON> } from "next"
// import { Inter } from "next/font/google"
import { getContentBySectionAndKey } from "@/lib/cms/supabase-cms"
import { Providers } from "./providers"
import StructuredData from "@/components/StructuredData"
// import WebVitals from "@/components/WebVitals"
// import PerformanceMonitor from "@/components/PerformanceMonitor"
// import ErrorBoundary from "@/components/ErrorBoundary"
// import GlobalErrorHandler from "@/components/GlobalErrorHandler"
import "./globals.css"

// Temporarily disable Google Fonts for build
// const inter = Inter({
//   subsets: ["latin"],
//   display: 'swap',
//   fallback: ['system-ui', 'arial']
// })

export async function generateMetadata(): Promise<Metadata> {
  // Default values
  const defaultTitle = "Zachary Odero - Portfolio";
  const defaultDescription = "Personal portfolio website of Zachary <PERSON>der<PERSON>";
  const defaultKeywords = "web development, full stack, react, next.js, portfolio";

  try {
    // Fetch SEO settings from CMS
    const siteTitle = await getContentBySectionAndKey('general', 'site_title');
    const siteDescription = await getContentBySectionAndKey('general', 'site_description');
    const metaKeywords = await getContentBySectionAndKey('general', 'meta_keywords');

    return {
      title: siteTitle?.content || defaultTitle,
      description: siteDescription?.content || defaultDescription,
      keywords: metaKeywords?.content || defaultKeywords,
      generator: 'v0.dev',
      icons: {
        icon: [
          { url: '/favicon.ico', sizes: '16x16', type: 'image/x-icon' },
          { url: '/favicon.svg', type: 'image/svg+xml' },
          { url: '/icon-72x72.png', sizes: '72x72', type: 'image/png' },
          { url: '/icon-96x96.png', sizes: '96x96', type: 'image/png' },
          { url: '/icon-144x144.png', sizes: '144x144', type: 'image/png' },
          { url: '/icon-192x192.png', sizes: '192x192', type: 'image/png' },
          { url: '/icon-512x512.png', sizes: '512x512', type: 'image/png' }
        ],
        apple: [
          { url: '/apple-touch-icon.png', sizes: '180x180', type: 'image/png' },
          { url: '/apple-touch-icon-152x152.png', sizes: '152x152', type: 'image/png' },
          { url: '/apple-touch-icon-167x167.png', sizes: '167x167', type: 'image/png' }
        ]
      },
      openGraph: {
        type: 'website',
        locale: 'en_US',
        url: 'https://zacharyodero.com',
        siteName: siteTitle?.content || defaultTitle,
        title: siteTitle?.content || defaultTitle,
        description: siteDescription?.content || defaultDescription,
        images: [
          {
            url: '/icon-512x512.png',
            width: 512,
            height: 512,
            alt: 'Zachary Odero Portfolio Logo'
          }
        ]
      },
      twitter: {
        card: 'summary_large_image',
        title: siteTitle?.content || defaultTitle,
        description: siteDescription?.content || defaultDescription,
        images: ['/icon-512x512.png'],
        creator: '@ZacharyOde83974',
        site: '@ZacharyOde83974'
      },

      robots: {
        index: true,
        follow: true,
        googleBot: {
          index: true,
          follow: true,
          'max-video-preview': -1,
          'max-image-preview': 'large',
          'max-snippet': -1,
        },
      },
      verification: {
        google: 'your-google-verification-code',
      },
      alternates: {
        canonical: 'https://zacharyodero.com',
      },
      category: 'technology'
    };
  } catch (error) {
    console.log('Error fetching metadata from CMS, using defaults:', error);
    // Return default values if there's an error
    return {
      title: defaultTitle,
      description: defaultDescription,
      keywords: defaultKeywords,
      generator: 'v0.dev',
      icons: {
        icon: [
          { url: '/favicon.ico', sizes: '16x16', type: 'image/x-icon' },
          { url: '/favicon.svg', type: 'image/svg+xml' },
          { url: '/icon-72x72.png', sizes: '72x72', type: 'image/png' },
          { url: '/icon-96x96.png', sizes: '96x96', type: 'image/png' },
          { url: '/icon-144x144.png', sizes: '144x144', type: 'image/png' },
          { url: '/icon-192x192.png', sizes: '192x192', type: 'image/png' },
          { url: '/icon-512x512.png', sizes: '512x512', type: 'image/png' }
        ],
        apple: [
          { url: '/apple-touch-icon.png', sizes: '180x180', type: 'image/png' },
          { url: '/apple-touch-icon-152x152.png', sizes: '152x152', type: 'image/png' },
          { url: '/apple-touch-icon-167x167.png', sizes: '167x167', type: 'image/png' }
        ]
      },
      openGraph: {
        type: 'website',
        locale: 'en_US',
        url: 'https://zacharyodero.com',
        siteName: defaultTitle,
        title: defaultTitle,
        description: defaultDescription,
        images: [
          {
            url: '/icon-512x512.png',
            width: 512,
            height: 512,
            alt: 'Zachary Odero Portfolio Logo'
          }
        ]
      },
      twitter: {
        card: 'summary_large_image',
        title: defaultTitle,
        description: defaultDescription,
        images: ['/icon-512x512.png'],
        creator: '@ZacharyOde83974',
        site: '@ZacharyOde83974'
      },

      robots: {
        index: true,
        follow: true,
        googleBot: {
          index: true,
          follow: true,
          'max-video-preview': -1,
          'max-image-preview': 'large',
          'max-snippet': -1,
        },
      },
      verification: {
        google: 'your-google-verification-code',
      },
      alternates: {
        canonical: 'https://zacharyodero.com',
      },
      category: 'technology'
    };
  }
}

// Separate viewport export as required by Next.js 15
export const viewport = {
  width: 'device-width',
  initialScale: 1,
  maximumScale: 1,
  userScalable: false,
  themeColor: '#1e40af'
}

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode
}>) {
  return (
    <html lang="en" suppressHydrationWarning>
      <body className="font-sans">
        <StructuredData />
        <Providers>
          {children}
        </Providers>
      </body>
    </html>
  )
}
