import { NextRequest, NextResponse } from 'next/server';
import { STKPushCallback } from '@/lib/mpesa/daraja-api';

// This endpoint simulates a callback from M-Pesa
export async function POST(request: NextRequest) {
  try {
    console.log('Simulated callback API called');
    
    // Parse request body
    const body = await request.json();
    const { CheckoutRequestID, MerchantRequestID, PhoneNumber, Amount, ReferenceCode } = body;
    
    console.log('Request body:', { CheckoutRequestID, MerchantRequestID, PhoneNumber, Amount, ReferenceCode });
    
    // Validate required fields
    if (!CheckoutRequestID || !MerchantRequestID) {
      console.log('Error: CheckoutRequestID and MerchantRequestID are required');
      return NextResponse.json(
        {
          success: false,
          error: 'CheckoutRequestID and MerchantRequestID are required',
        },
        { status: 400 }
      );
    }
    
    // Generate a random M-Pesa receipt number
    const mpesaReceiptNumber = generateMpesaReceiptNumber();
    
    // Create a simulated callback payload
    const callbackData: STKPushCallback = {
      Body: {
        stkCallback: {
          MerchantRequestID: MerchantRequestID,
          CheckoutRequestID: CheckoutRequestID,
          ResultCode: 0,
          ResultDesc: 'The service request is processed successfully.',
          CallbackMetadata: {
            Item: [
              {
                Name: 'Amount',
                Value: Amount || 1000,
              },
              {
                Name: 'MpesaReceiptNumber',
                Value: mpesaReceiptNumber,
              },
              {
                Name: 'TransactionDate',
                Value: generateTransactionDate(),
              },
              {
                Name: 'PhoneNumber',
                Value: PhoneNumber || '************',
              },
              {
                Name: 'AccountReference',
                Value: ReferenceCode || 'REF123456',
              },
            ],
          },
        },
      },
    };
    
    console.log('Simulated callback data:', JSON.stringify(callbackData));
    
    // Send the callback to the callback endpoint
    try {
      const callbackResponse = await fetch(`${process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000'}/api/mpesa/callback`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(callbackData),
      });
      
      if (!callbackResponse.ok) {
        const errorText = await callbackResponse.text();
        console.error('Error response from callback endpoint:', errorText);
        return NextResponse.json(
          {
            success: false,
            error: `Failed to process callback: ${callbackResponse.status} ${callbackResponse.statusText}`,
            details: errorText,
          },
          { status: 500 }
        );
      }
      
      const callbackResult = await callbackResponse.json();
      
      return NextResponse.json({
        success: true,
        data: {
          message: 'Simulated callback sent successfully',
          mpesaReceiptNumber,
          callbackResult,
        },
      });
    } catch (callbackError) {
      console.error('Error sending simulated callback:', callbackError);
      return NextResponse.json(
        {
          success: false,
          error: `Failed to send simulated callback: ${callbackError instanceof Error ? callbackError.message : 'Unknown error'}`,
        },
        { status: 500 }
      );
    }
  } catch (error) {
    console.error('Error processing simulated callback request:', error);
    return NextResponse.json(
      {
        success: false,
        error: `Failed to process simulated callback request: ${error instanceof Error ? error.message : 'Unknown error'}`,
      },
      { status: 500 }
    );
  }
}

// Generate a random M-Pesa receipt number (10 characters)
function generateMpesaReceiptNumber(): string {
  const characters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
  let result = '';
  for (let i = 0; i < 10; i++) {
    result += characters.charAt(Math.floor(Math.random() * characters.length));
  }
  return result;
}

// Generate a transaction date in the format YYYYMMDDHHmmss
function generateTransactionDate(): string {
  const date = new Date();
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const day = String(date.getDate()).padStart(2, '0');
  const hours = String(date.getHours()).padStart(2, '0');
  const minutes = String(date.getMinutes()).padStart(2, '0');
  const seconds = String(date.getSeconds()).padStart(2, '0');
  
  return `${year}${month}${day}${hours}${minutes}${seconds}`;
}
