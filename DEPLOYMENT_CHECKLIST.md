# Production Deployment Checklist

## Environment Variables Verification

### ✅ Supabase Configuration

- [ ] `NEXT_PUBLIC_SUPABASE_URL` - Your Supabase project URL
- [ ] `NEXT_PUBLIC_SUPABASE_ANON_KEY` - Supabase anonymous key
- [ ] `SUPABASE_SERVICE_ROLE_KEY` - Supabase service role key (keep secret!)

### ✅ Email Configuration (Resend)

- [ ] `RESEND_API_KEY` - Your Resend API key
- [ ] `FROM_EMAIL` - Email sender (<EMAIL>)
- [ ] `TO_EMAIL` - Your email for receiving notifications

### ✅ Site Configuration

- [ ] `NEXT_PUBLIC_SITE_URL` - Your production domain
- [ ] `NODE_ENV=production` - Environment setting
- [ ] `NEXTAUTH_SECRET` - Strong random string for sessions
- [ ] `NEXTAUTH_URL` - Your production URL

### ✅ CMS Configuration

- [ ] `ADMIN_EMAIL` - Your admin email for CMS access
- [ ] `ADMIN_PASSWORD_HASH` - B<PERSON>rypt hashed password for admin login

## Pre-Deployment Checks

### ✅ Code Quality

- [ ] All TypeScript errors resolved
- [ ] ESLint warnings addressed
- [ ] Build process completes successfully
- [ ] No console errors in production build

### ✅ SEO & Metadata

- [ ] Favicon and app icons created
- [ ] Open Graph tags configured
- [ ] Twitter Card metadata set
- [ ] Structured data (JSON-LD) implemented
- [ ] robots.txt file present
- [ ] sitemap.xml configured

### ✅ Performance

- [ ] Images optimized and using Next.js Image component
- [ ] Fonts loading efficiently
- [ ] Bundle size analyzed and optimized
- [ ] Core Web Vitals targets met

### ✅ Security

- [ ] Environment variables properly secured
- [ ] No sensitive data exposed client-side
- [ ] HTTPS enforced
- [ ] Security headers configured

### ✅ Functionality Testing

- [ ] Contact forms working
- [ ] Email notifications sending
- [ ] CMS authentication working
- [ ] Database connections stable
- [ ] All pages loading correctly

## Post-Deployment Verification

### ✅ Live Site Checks

- [ ] Site loads correctly at production URL
- [ ] All pages accessible
- [ ] Forms submitting successfully
- [ ] Email notifications received
- [ ] CMS dashboard accessible
- [ ] Mobile responsiveness verified

### ✅ SEO Tools

- [ ] Google Search Console verification
- [ ] Submit sitemap to search engines
- [ ] Test social media sharing previews
- [ ] Verify structured data with Google's Rich Results Test

### ✅ Performance Monitoring

- [ ] Set up error tracking (optional)
- [ ] Monitor Core Web Vitals
- [ ] Check loading speeds across devices
- [ ] Verify CDN performance

## Troubleshooting Common Issues

### Environment Variables Not Working

1. Check variable names match exactly (case-sensitive)
2. Verify variables are set for "Production" environment in Vercel
3. Redeploy after adding new environment variables

### Build Failures

1. Check for TypeScript errors
2. Verify all dependencies are installed
3. Check for missing environment variables during build

### Email Not Sending

1. Verify Resend API key is correct
2. Check FROM_EMAIL is using allowed domain
3. Verify TO_EMAIL is correct

### Database Connection Issues

1. Check Supabase URL and keys
2. Verify database is accessible from Vercel
3. Check for any IP restrictions

## Support Resources

- [Next.js Deployment Documentation](https://nextjs.org/docs/deployment)
- [Vercel Environment Variables Guide](https://vercel.com/docs/concepts/projects/environment-variables)
- [Supabase Production Checklist](https://supabase.com/docs/guides/platform/going-into-prod)
