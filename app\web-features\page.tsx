'use client';

import { motion } from 'framer-motion';
import Link from 'next/link';
import {
  Smartphone,
  Settings,
  Users,
  ShoppingCart,
  Search,
  Zap,
  Globe,
  Server,
  Plug,
  Shield,
  RefreshCw,
  HardDrive,
  BarChart3,
  Target,
  Eye,
  Image,
  Database,
  Accessibility,
  Languages,
  ArrowRight,
  CheckCircle
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';

export default function WebFeaturesPage() {
  const fadeInUp = {
    initial: { opacity: 0, y: 20 },
    animate: { opacity: 1, y: 0 },
    transition: { duration: 0.5 }
  };

  const staggerChildren = {
    animate: {
      transition: {
        staggerChildren: 0.1
      }
    }
  };

  return (
    <div className="container mx-auto px-4 py-8 max-w-6xl">
      {/* Hero Section */}
      <motion.div
        className="text-center mb-12"
        initial={{ opacity: 0, y: 30 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6 }}
      >
        <h1 className="text-4xl md:text-5xl font-bold mb-6 bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
          Web Development Features Explained
        </h1>
        <p className="text-xl text-muted-foreground max-w-3xl mx-auto mb-8">
          Understanding website features and technologies in simple terms, focusing on business benefits
          to help you make informed decisions about your web development project.
        </p>
        <Badge variant="secondary" className="text-sm px-4 py-2">
          Business-Focused Guide
        </Badge>
      </motion.div>

      {/* Introduction */}
      <motion.div
        className="mb-12"
        {...fadeInUp}
      >
        <Card className="border-l-4 border-l-blue-500">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <CheckCircle className="h-5 w-5 text-blue-500" />
              Why This Guide Matters
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-muted-foreground">
              This guide explains common website features and technologies in simple terms, focusing on the business benefits
              rather than technical details. Understanding these concepts will help you make informed decisions about your website project,
              choose the right package, and select additional services that align with your business goals.
            </p>
          </CardContent>
        </Card>
      </motion.div>

      {/* Table of Contents */}
      <motion.div
        className="mb-12"
        {...fadeInUp}
      >
        <Card>
          <CardHeader>
            <CardTitle>Quick Navigation</CardTitle>
            <CardDescription>Jump to any section that interests you</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {[
                { title: "Core Website Features", href: "#core-features" },
                { title: "Next.js Benefits", href: "#nextjs-benefits" },
                { title: "Security Features", href: "#security" },
                { title: "Analytics & Intelligence", href: "#analytics" },
                { title: "Performance Optimization", href: "#performance" },
                { title: "Accessibility Features", href: "#accessibility" }
              ].map((item, index) => (
                <motion.a
                  key={index}
                  href={item.href}
                  className="flex items-center gap-2 p-3 rounded-lg border hover:border-primary hover:bg-primary/5 transition-colors"
                  whileHover={{ scale: 1.02 }}
                  whileTap={{ scale: 0.98 }}
                >
                  <ArrowRight className="h-4 w-4 text-primary" />
                  <span className="font-medium">{item.title}</span>
                </motion.a>
              ))}
            </div>
          </CardContent>
        </Card>
      </motion.div>

      {/* Core Website Features */}
      <motion.section
        id="core-features"
        className="mb-16"
        variants={staggerChildren}
        initial="initial"
        whileInView="animate"
        viewport={{ once: true }}
      >
        <motion.h2
          className="text-3xl font-bold mb-8 text-center"
          variants={fadeInUp}
        >
          Core Website Features
        </motion.h2>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <motion.div variants={fadeInUp}>
            <FeatureCard
              icon={<Smartphone className="h-6 w-6" />}
              title="Responsive Design"
              description="A website that automatically adjusts to look good on all devices - desktop computers, tablets, and phones."
              benefits={[
                "Reach customers on any device they prefer to use",
                "Improve customer experience, leading to higher engagement and sales",
                "Boost search engine rankings (Google prioritizes mobile-friendly sites)",
                "Eliminate the need for separate mobile and desktop websites"
              ]}
            />
          </motion.div>

          <motion.div variants={fadeInUp}>
            <FeatureCard
              icon={<Settings className="h-6 w-6" />}
              title="Content Management System (CMS)"
              description="A user-friendly interface that lets you update your website content without technical knowledge."
              benefits={[
                "Update text, images, and other content yourself without hiring a developer",
                "Save time and money on routine website updates",
                "Keep your website fresh with timely information",
                "Empower your team to contribute content directly"
              ]}
            />
          </motion.div>

          <motion.div variants={fadeInUp}>
            <FeatureCard
              icon={<Users className="h-6 w-6" />}
              title="User Authentication"
              description="Systems that allow visitors to create accounts, log in, and access personalized features."
              benefits={[
                "Build customer relationships through personalized experiences",
                "Collect valuable data about your users' preferences",
                "Enable features like saved favorites, purchase history, or customized recommendations",
                "Create members-only content or special offers to increase customer loyalty"
              ]}
            />
          </motion.div>

          <motion.div variants={fadeInUp}>
            <FeatureCard
              icon={<ShoppingCart className="h-6 w-6" />}
              title="E-commerce Functionality"
              description="The ability to sell products or services directly through your website."
              benefits={[
                "Sell to customers 24/7 without geographical limitations",
                "Reduce overhead compared to physical retail locations",
                "Create automated sales processes that scale with your business",
                "Access detailed analytics about customer shopping behaviors"
              ]}
            />
          </motion.div>
        </div>
      </motion.section>

      {/* SEO Features */}
      <motion.section
        className="mb-16"
        variants={staggerChildren}
        initial="initial"
        whileInView="animate"
        viewport={{ once: true }}
      >
        <motion.div variants={fadeInUp}>
          <FeatureCard
            icon={<Search className="h-6 w-6" />}
            title="Search Engine Optimization (SEO) Features"
            description="Built-in capabilities that make your website more visible in search engines like Google."
            benefits={[
              "Attract more potential customers through organic (free) search traffic",
              "Reduce dependency on paid advertising",
              "Build credibility as customers find you naturally",
              "Target specific customer problems and questions"
            ]}
          />
        </motion.div>
      </motion.section>

      {/* Next.js-Specific Benefits */}
      <motion.section
        id="nextjs-benefits"
        className="mb-16"
        variants={staggerChildren}
        initial="initial"
        whileInView="animate"
        viewport={{ once: true }}
      >
        <motion.h2
          className="text-3xl font-bold mb-8 text-center"
          variants={fadeInUp}
        >
          Next.js-Specific Benefits
        </motion.h2>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <motion.div variants={fadeInUp}>
            <FeatureCard
              icon={<Zap className="h-6 w-6" />}
              title="Fast Loading Speed"
              description="Next.js creates websites that load quickly, even with complex features."
              benefits={[
                "Reduce visitor abandonment (people leaving because the site is too slow)",
                "Improve conversion rates - faster sites sell more",
                "Enhance user experience, encouraging return visits",
                "Gain search engine advantage (Google ranks faster sites higher)"
              ]}
            />
          </motion.div>

          <motion.div variants={fadeInUp}>
            <FeatureCard
              icon={<Globe className="h-6 w-6" />}
              title="Progressive Web App Capabilities"
              description="Technology that makes websites feel more like mobile apps, with offline functionality and home screen installation."
              benefits={[
                "Provide a premium user experience similar to native mobile apps",
                "Allow customers to access your content even with poor internet connections",
                "Increase engagement through home screen presence on user devices",
                "Save development costs compared to building separate mobile apps"
              ]}
            />
          </motion.div>

          <motion.div variants={fadeInUp}>
            <FeatureCard
              icon={<Server className="h-6 w-6" />}
              title="Server-Side Rendering"
              description="A technique where your website content loads completely before being sent to the user."
              benefits={[
                "Faster perceived loading speed for better user experience",
                "Improved search engine visibility for your content",
                "Better performance for users with slower internet connections",
                "Reduced screen flicker or layout shifts during loading"
              ]}
            />
          </motion.div>

          <motion.div variants={fadeInUp}>
            <FeatureCard
              icon={<Plug className="h-6 w-6" />}
              title="API Integration"
              description="The ability to connect your website with other business tools and services."
              benefits={[
                "Automatically sync website data with your CRM, email marketing, or inventory systems",
                "Create seamless experiences between your website and other business operations",
                "Reduce manual data entry and potential errors",
                "Build custom workflows specific to your business needs"
              ]}
            />
          </motion.div>
        </div>
      </motion.section>

      {/* Security Features */}
      <motion.section
        id="security"
        className="mb-16"
        variants={staggerChildren}
        initial="initial"
        whileInView="animate"
        viewport={{ once: true }}
      >
        <motion.h2
          className="text-3xl font-bold mb-8 text-center"
          variants={fadeInUp}
        >
          Security Features
        </motion.h2>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <motion.div variants={fadeInUp}>
            <FeatureCard
              icon={<Shield className="h-6 w-6" />}
              title="SSL Certification"
              description="Technology that encrypts data exchanged between your website and visitors."
              benefits={[
                "Protect sensitive customer information like contact details or payment data",
                "Build trust with security indicators (like the padlock icon in browsers)",
                "Meet compliance requirements for data protection",
                "Avoid browser warnings that can scare away potential customers"
              ]}
            />
          </motion.div>

          <motion.div variants={fadeInUp}>
            <FeatureCard
              icon={<RefreshCw className="h-6 w-6" />}
              title="Regular Updates & Maintenance"
              description="Ongoing technical maintenance to keep your website secure and functioning optimally."
              benefits={[
                "Protect against the latest security threats and vulnerabilities",
                "Ensure compatibility with new browsers and devices",
                "Maintain site performance and reliability",
                "Prevent costly emergency fixes by addressing issues proactively"
              ]}
            />
          </motion.div>

          <motion.div variants={fadeInUp}>
            <FeatureCard
              icon={<HardDrive className="h-6 w-6" />}
              title="Backup Systems"
              description="Automatic copying and storage of your website data to prevent loss."
              benefits={[
                "Quick recovery from any technical problems or security incidents",
                "Peace of mind knowing your business website is protected",
                "Minimize potential downtime that could cost you customers and revenue",
                "Ability to restore previous versions if needed"
              ]}
            />
          </motion.div>
        </div>
      </motion.section>

      {/* Analytics & Business Intelligence */}
      <motion.section
        id="analytics"
        className="mb-16"
        variants={staggerChildren}
        initial="initial"
        whileInView="animate"
        viewport={{ once: true }}
      >
        <motion.h2
          className="text-3xl font-bold mb-8 text-center"
          variants={fadeInUp}
        >
          Analytics & Business Intelligence
        </motion.h2>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <motion.div variants={fadeInUp}>
            <FeatureCard
              icon={<BarChart3 className="h-6 w-6" />}
              title="User Behavior Tracking"
              description="Tools that show how visitors interact with your website."
              benefits={[
                "Understand which products or services generate the most interest",
                "Identify and fix problematic pages where visitors leave",
                "Optimize your sales funnel based on actual user behavior",
                "Make data-driven decisions about website improvements"
              ]}
            />
          </motion.div>

          <motion.div variants={fadeInUp}>
            <FeatureCard
              icon={<Target className="h-6 w-6" />}
              title="Conversion Tracking"
              description="Measurement of specific business goals completed on your website."
              benefits={[
                "Know exactly which marketing efforts are generating sales",
                "Calculate your return on investment for digital initiatives",
                "Test different approaches to see what works best",
                "Focus resources on your most effective business strategies"
              ]}
            />
          </motion.div>

          <motion.div variants={fadeInUp}>
            <FeatureCard
              icon={<Eye className="h-6 w-6" />}
              title="Heat Mapping"
              description="Visual representations showing where users click and how far they scroll."
              benefits={[
                "Place your most important content where users actually look",
                "Improve button and call-to-action placement for better results",
                "Understand if people are seeing your key messages",
                "Design more effective page layouts based on user behavior"
              ]}
            />
          </motion.div>
        </div>
      </motion.section>

      {/* Performance Optimization */}
      <motion.section
        id="performance"
        className="mb-16"
        variants={staggerChildren}
        initial="initial"
        whileInView="animate"
        viewport={{ once: true }}
      >
        <motion.h2
          className="text-3xl font-bold mb-8 text-center"
          variants={fadeInUp}
        >
          Performance Optimization
        </motion.h2>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <motion.div variants={fadeInUp}>
            <FeatureCard
              icon={<Image className="h-6 w-6" />}
              title="Image Optimization"
              description="Automatic resizing and compression of images to balance quality and speed."
              benefits={[
                "Faster loading pages, especially important on mobile devices",
                "Reduced bandwidth costs for high-traffic sites",
                "Better user experience with crisp, properly sized images",
                "No technical knowledge required to add optimized images"
              ]}
            />
          </motion.div>

          <motion.div variants={fadeInUp}>
            <FeatureCard
              icon={<Database className="h-6 w-6" />}
              title="Caching Systems"
              description="Technology that stores parts of your website to deliver them faster to repeat visitors."
              benefits={[
                "Dramatically improved loading speeds for returning customers",
                "Reduced server costs, especially during high-traffic periods",
                "Better performance during marketing campaigns or sales events",
                "Improved user experience even on complex, feature-rich pages"
              ]}
            />
          </motion.div>
        </div>
      </motion.section>

      {/* Accessibility Features */}
      <motion.section
        id="accessibility"
        className="mb-16"
        variants={staggerChildren}
        initial="initial"
        whileInView="animate"
        viewport={{ once: true }}
      >
        <motion.h2
          className="text-3xl font-bold mb-8 text-center"
          variants={fadeInUp}
        >
          Accessibility Features
        </motion.h2>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <motion.div variants={fadeInUp}>
            <FeatureCard
              icon={<Accessibility className="h-6 w-6" />}
              title="ADA Compliance Tools"
              description="Features that make your website usable for people with disabilities."
              benefits={[
                "Reach the entire potential market, including people with disabilities",
                "Reduce legal liability risks related to digital accessibility",
                "Demonstrate corporate social responsibility and inclusivity",
                "Often improves usability for all visitors, not just those with disabilities"
              ]}
            />
          </motion.div>

          <motion.div variants={fadeInUp}>
            <FeatureCard
              icon={<Languages className="h-6 w-6" />}
              title="Multi-language Support"
              description="The ability to present your website content in different languages."
              benefits={[
                "Expand into new international markets",
                "Better serve diverse local communities",
                "Demonstrate cultural awareness and inclusivity",
                "Competitive advantage in multilingual regions"
              ]}
            />
          </motion.div>
        </div>
      </motion.section>

      {/* Conclusion */}
      <motion.section
        className="mb-16"
        initial={{ opacity: 0, y: 20 }}
        whileInView={{ opacity: 1, y: 0 }}
        viewport={{ once: true }}
        transition={{ duration: 0.5 }}
      >
        <Card className="border-l-4 border-l-green-500">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <CheckCircle className="h-5 w-5 text-green-500" />
              Making Informed Decisions
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-muted-foreground mb-4">
              The features outlined in this guide represent common components of modern business websites.
              The specific needs for your project will depend on your unique business goals, target audience,
              and operational requirements.
            </p>
            <p className="text-muted-foreground">
              Working with your web development team to identify which features align with your business objectives
              will help ensure your website investment delivers meaningful returns. Use this knowledge to make
              informed decisions about your package selection and additional services.
            </p>
          </CardContent>
        </Card>
      </motion.section>

      {/* Call to Action */}
      <motion.div
        className="text-center mb-12"
        initial={{ opacity: 0, y: 20 }}
        whileInView={{ opacity: 1, y: 0 }}
        viewport={{ once: true }}
        transition={{ duration: 0.5 }}
      >
        <Card className="bg-gradient-to-r from-blue-50 to-purple-50 dark:from-blue-950/20 dark:to-purple-950/20 border-blue-200 dark:border-blue-800">
          <CardContent className="pt-6">
            <h3 className="text-2xl font-bold mb-4">Ready to Start Your Project?</h3>
            <p className="text-muted-foreground mb-6 max-w-2xl mx-auto">
              Now that you understand the business benefits of various web features,
              you can make informed decisions about your project package and additional services.
            </p>
            <Link href="/start-project">
              <Button size="lg" className="gap-2">
                <ArrowRight className="h-5 w-5" />
                Choose Your Package
              </Button>
            </Link>
          </CardContent>
        </Card>
      </motion.div>
    </div>
  );
}

interface FeatureCardProps {
  icon: React.ReactNode;
  title: string;
  description: string;
  benefits: string[];
}

function FeatureCard({ icon, title, description, benefits }: FeatureCardProps) {
  return (
    <Card className="h-full hover:shadow-lg transition-shadow">
      <CardHeader>
        <div className="flex items-center gap-3 mb-2">
          <div className="p-2 rounded-lg bg-primary/10 text-primary">
            {icon}
          </div>
          <CardTitle className="text-xl">{title}</CardTitle>
        </div>
        <CardDescription className="text-base">{description}</CardDescription>
      </CardHeader>
      <CardContent>
        <div className="space-y-2">
          <h4 className="font-semibold text-sm text-primary mb-3">Business Benefits:</h4>
          <ul className="space-y-2">
            {benefits.map((benefit, index) => (
              <li key={index} className="flex items-start gap-2 text-sm">
                <CheckCircle className="h-4 w-4 text-green-500 mt-0.5 flex-shrink-0" />
                <span>{benefit}</span>
              </li>
            ))}
          </ul>
        </div>
      </CardContent>
    </Card>
  );
}
