/**
 * Test script for email notifications
 * This script tests the email notification functions locally
 */

const { sendProjectRequestNotification, sendQuestionnaireCompletionNotification } = require('../lib/email-notifications');

async function testProjectRequestNotification() {
  console.log('Testing project request notification...');
  
  const testData = {
    client_name: 'Test Client',
    client_email: '<EMAIL>',
    client_phone: '+254700000000',
    package_type: 'standard',
    additional_services: ['seo-optimization', 'analytics-setup'],
    maintenance_plan: 'basic',
    total_amount: 125000,
    reference_code: 'TEST123456',
    created_at: new Date().toISOString(),
  };

  try {
    const result = await sendProjectRequestNotification(testData);
    console.log('Project request notification result:', result);
  } catch (error) {
    console.error('Error testing project request notification:', error);
  }
}

async function testQuestionnaireCompletionNotification() {
  console.log('Testing questionnaire completion notification...');
  
  const testData = {
    client_name: 'Test Client',
    client_email: '<EMAIL>',
    client_phone: '+254700000000',
    package_type: 'premium',
    reference_code: 'TEST123456',
    questionnaire_id: 'quest_123456',
    completed_at: new Date().toISOString(),
    // General information
    project_name: 'Test E-commerce Website',
    project_description: 'A modern e-commerce platform for selling handmade crafts',
    target_audience: 'Young professionals aged 25-40',
    project_goals: ['increase-online-sales', 'improve-brand-awareness', 'expand-market-reach'],
    color_scheme: 'modern',
    has_logo_or_brand_guidelines: true,
    preferred_launch_timeline: '1-2weeks',
    // Package-specific data
    package_data: {
      requested_pages: ['Home', 'Shop', 'About', 'Contact', 'Blog'],
      has_content_ready: true,
      contact_form_fields: ['name', 'email', 'message'],
      has_domain_name: false,
      preferred_hosting: 'vercel',
      blog_content_type: 'product-updates',
      blog_update_frequency: 'weekly',
      cms_preference: 'contentful',
    },
  };

  try {
    const result = await sendQuestionnaireCompletionNotification(testData);
    console.log('Questionnaire completion notification result:', result);
  } catch (error) {
    console.error('Error testing questionnaire completion notification:', error);
  }
}

async function runTests() {
  console.log('Starting email notification tests...\n');
  
  await testProjectRequestNotification();
  console.log('\n' + '='.repeat(50) + '\n');
  await testQuestionnaireCompletionNotification();
  
  console.log('\nEmail notification tests completed!');
}

// Run tests if this script is executed directly
if (require.main === module) {
  runTests().catch(console.error);
}

module.exports = {
  testProjectRequestNotification,
  testQuestionnaireCompletionNotification,
  runTests,
};
