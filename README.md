# My Next.js Portfolio

A modern portfolio website built with Next.js, React, TypeScript, and Tailwind CSS.

[![Deployed on Vercel](https://img.shields.io/badge/Deployed%20on-Vercel-black?style=for-the-badge&logo=vercel)](https://vercel.com/zacharyoderos-projects/v0-my-nextjs-portfolio)
[![Built with Next.js](https://img.shields.io/badge/Built%20with-Next.js-black?style=for-the-badge&logo=next.js)](https://nextjs.org/)

## Features

- Responsive design for all devices
- Dark/light mode toggle
- Animated UI elements with Framer Motion
- Contact form with Supabase integration
- TypeScript for type safety
- Tailwind CSS for styling

## Getting Started

### Prerequisites

- Node.js 18.x or later
- npm or pnpm

### Installation

1. Clone the repository:

   ```bash
   git clone https://github.com/ZacharyOdero/my-nextjs-portfolio.git
   cd my-nextjs-portfolio
   ```

2. Install dependencies:

   ```bash
   npm install
   # or
   pnpm install
   ```

3. Set up environment variables:
   Create a `.env.local` file in the root directory with the following variables:

   ```
   NEXT_PUBLIC_SUPABASE_URL=your-supabase-url
   NEXT_PUBLIC_SUPABASE_ANON_KEY=your-supabase-anon-key
   ```

4. Run the development server:

   ```bash
   npm run dev
   # or
   pnpm dev
   ```

5. Open [http://localhost:3000](http://localhost:3000) in your browser.

## Supabase Setup for Contact Form

1. Create a Supabase account at [supabase.com](https://supabase.com)
2. Create a new project
3. Create a new table called `contact_messages` with the following columns:
   - `id` (uuid, primary key)
   - `name` (text, not null)
   - `email` (text, not null)
   - `subject` (text, not null)
   - `message` (text, not null)
   - `created_at` (timestamp with time zone, default: now())
4. Get your Supabase URL and anon key from the project settings
5. Add these values to your `.env.local` file

## Deployment

The project is deployed on Vercel. Any changes pushed to the main branch will be automatically deployed.

## Built With

- [Next.js](https://nextjs.org/) - React framework
- [React](https://reactjs.org/) - UI library
- [TypeScript](https://www.typescriptlang.org/) - Type safety
- [Tailwind CSS](https://tailwindcss.com/) - Styling
- [Framer Motion](https://www.framer.com/motion/) - Animations
- [Supabase](https://supabase.com/) - Backend and database
- [React Hook Form](https://react-hook-form.com/) - Form handling
- [Zod](https://zod.dev/) - Form validation
