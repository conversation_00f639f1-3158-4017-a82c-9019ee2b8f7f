<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Payment System Test Client</title>
    <style>
      body {
        font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto,
          Oxygen, Ubuntu, Cantarell, "Open Sans", "Helvetica Neue", sans-serif;
        line-height: 1.6;
        color: #333;
        max-width: 800px;
        margin: 0 auto;
        padding: 20px;
      }
      h1 {
        color: #2563eb;
        border-bottom: 2px solid #e5e7eb;
        padding-bottom: 10px;
      }
      h2 {
        color: #4b5563;
        margin-top: 30px;
      }
      .card {
        background-color: #f9fafb;
        border-radius: 8px;
        padding: 20px;
        margin-bottom: 20px;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
      }
      .step {
        background-color: #eff6ff;
        border-left: 4px solid #2563eb;
        padding: 15px;
        margin-bottom: 20px;
      }
      code {
        background-color: #f1f5f9;
        padding: 2px 5px;
        border-radius: 4px;
        font-family: monospace;
      }
      pre {
        background-color: #1e293b;
        color: #e2e8f0;
        padding: 15px;
        border-radius: 8px;
        overflow-x: auto;
        font-family: monospace;
      }
      button {
        background-color: #2563eb;
        color: white;
        border: none;
        padding: 10px 15px;
        border-radius: 4px;
        cursor: pointer;
        font-size: 14px;
        margin-right: 10px;
        margin-bottom: 10px;
      }
      button:hover {
        background-color: #1d4ed8;
      }
      .test-data {
        display: flex;
        flex-wrap: wrap;
        gap: 10px;
        margin-bottom: 20px;
      }
      .test-data button {
        flex: 1;
        min-width: 150px;
      }
      .result {
        background-color: #f8fafc;
        border: 1px solid #e2e8f0;
        border-radius: 8px;
        padding: 15px;
        margin-top: 20px;
        min-height: 100px;
      }
      .tabs {
        display: flex;
        border-bottom: 1px solid #e5e7eb;
        margin-bottom: 20px;
      }
      .tab {
        padding: 10px 20px;
        cursor: pointer;
        border-bottom: 2px solid transparent;
      }
      .tab.active {
        border-bottom: 2px solid #2563eb;
        color: #2563eb;
        font-weight: 500;
      }
      .tab-content {
        display: none;
      }
      .tab-content.active {
        display: block;
      }
      .note {
        background-color: #fef3c7;
        border-left: 4px solid #f59e0b;
        padding: 15px;
        margin-bottom: 20px;
      }
    </style>
  </head>
  <body>
    <h1>Payment System Test Client</h1>

    <div class="note">
      <strong>Note:</strong> This is a test client for the payment system
      workflow. Use this to test the entire flow from package selection to
      questionnaire access.
    </div>

    <div class="tabs">
      <div class="tab active" data-tab="instructions">Instructions</div>
      <div class="tab" data-tab="test-data">Test Data</div>
      <div class="tab" data-tab="console">Console</div>
    </div>

    <div class="tab-content active" id="instructions">
      <h2>How to Test the Payment System</h2>

      <div class="step">
        <h3>Step 1: Open Your Payment Flow Page</h3>
        <p>
          Navigate to your payment flow page at
          <a href="/start-project" target="_blank">/start-project</a>
        </p>
      </div>

      <div class="step">
        <h3>Step 2: Open Browser Console</h3>
        <p>
          Open your browser's developer tools and navigate to the Console tab.
        </p>
        <p>
          Shortcut: <code>F12</code> or
          <code>Ctrl+Shift+I</code> (Windows/Linux) or
          <code>Cmd+Option+I</code> (Mac)
        </p>
      </div>

      <div class="step">
        <h3>Step 3: Copy and Paste the Test Script</h3>
        <p>
          Copy the entire content of the <code>test-payment-flow.js</code> file
          and paste it into the console.
        </p>
      </div>

      <div class="step">
        <h3>Step 4: Run the Test Functions</h3>
        <p>
          Run the test functions in the console to fill out the forms
          automatically:
        </p>
        <pre>
// Test the full flow
testFullPaymentFlow('basic', 'client1', 0);

// Or test individual steps
fillPackageSelectionForm('basic');
fillClientInformationForm('client1');
fillPaymentVerificationForm(0);</pre
        >
      </div>

      <div class="step">
        <h3>Step 5: Complete the Flow</h3>
        <p>
          Click the "Continue" or "Verify Payment" buttons after each form is
          filled to proceed to the next step.
        </p>
        <p>
          The script will guide you through the process with console messages.
        </p>
      </div>

      <div class="step">
        <h3>Troubleshooting</h3>
        <p>
          If you encounter issues with form elements not being found, run the
          inspection function:
        </p>
        <pre>inspectFormElements()</pre>
        <p>
          This will print detailed information about all form elements to the
          console, helping you identify the actual IDs and values.
        </p>
      </div>
    </div>

    <div class="tab-content" id="test-data">
      <h2>Available Test Data</h2>

      <h3>Package Types</h3>
      <div class="card">
        <div class="test-data">
          <button
            onclick="copyToClipboard('fillPackageSelectionForm(\'basic\')')"
          >
            Basic Package
          </button>
          <button
            onclick="copyToClipboard('fillPackageSelectionForm(\'standard\')')"
          >
            Standard Package
          </button>
          <button
            onclick="copyToClipboard('fillPackageSelectionForm(\'premium\')')"
          >
            Premium Package
          </button>
        </div>
        <pre>
{
  basic: {
    package_type: 'basic',
    additional_services: ['api-dev', 'custom-components'],
    maintenance_plan: 'basic'
  },
  standard: {
    package_type: 'standard',
    additional_services: ['headless-cms', 'performance'],
    maintenance_plan: 'standard'
  },
  premium: {
    package_type: 'premium',
    additional_services: ['pwa', 'third-party-api'],
    maintenance_plan: 'standard'
  }
}</pre
        >
      </div>

      <h3>Client Information</h3>
      <div class="card">
        <div class="test-data">
          <button
            onclick="copyToClipboard('fillClientInformationForm(\'client1\')')"
          >
            Client 1
          </button>
          <button
            onclick="copyToClipboard('fillClientInformationForm(\'client2\')')"
          >
            Client 2
          </button>
          <button
            onclick="copyToClipboard('fillClientInformationForm(\'client3\')')"
          >
            Client 3
          </button>
        </div>
        <pre>
{
  client1: {
    client_name: 'John Doe',
    client_email: '<EMAIL>',
    client_phone: '0796564593'
  },
  client2: {
    client_name: 'Jane Smith',
    client_email: '<EMAIL>',
    client_phone: '0723456789'
  },
  client3: {
    client_name: 'Test User',
    client_email: '<EMAIL>',
    client_phone: '0734567890'
  }
}</pre
        >
      </div>

      <h3>M-Pesa Codes</h3>
      <div class="card">
        <div class="test-data">
          <button onclick="copyToClipboard('fillPaymentVerificationForm(0)')">
            Code 1
          </button>
          <button onclick="copyToClipboard('fillPaymentVerificationForm(1)')">
            Code 2
          </button>
          <button onclick="copyToClipboard('fillPaymentVerificationForm(2)')">
            Code 3
          </button>
          <button onclick="copyToClipboard('fillPaymentVerificationForm(3)')">
            Code 4
          </button>
        </div>
        <pre>
[
  'ABC123DEFG',
  'XYZ456MNOP',
  'QWE789RTYU',
  'ZXC012VBNM'
]</pre
        >
      </div>

      <h3>Full Flow Test</h3>
      <div class="card">
        <div class="test-data">
          <button
            onclick="copyToClipboard('testFullPaymentFlow(\'basic\', \'client1\', 0)')"
          >
            Basic Package Flow
          </button>
          <button
            onclick="copyToClipboard('testFullPaymentFlow(\'standard\', \'client2\', 1)')"
          >
            Standard Package Flow
          </button>
          <button
            onclick="copyToClipboard('testFullPaymentFlow(\'premium\', \'client3\', 2)')"
          >
            Premium Package Flow
          </button>
        </div>
      </div>

      <h3>Troubleshooting</h3>
      <div class="card">
        <div class="test-data">
          <button onclick="copyToClipboard('inspectFormElements()')">
            Inspect Form Elements
          </button>
        </div>
        <p class="mt-4 text-sm">
          Use this function to inspect the actual IDs, names, and values of form
          elements if you're having trouble with the test script. It will print
          detailed information to the console to help you debug issues.
        </p>
      </div>
    </div>

    <div class="tab-content" id="console">
      <h2>Console Output</h2>
      <p>Copy the script below and paste it into your browser console:</p>

      <div class="card">
        <button onclick="copyFullScript()">Copy Full Script</button>
        <div class="result" id="script-output">
          <pre id="script-content">// Loading script content...</pre>
        </div>
      </div>
    </div>

    <script>
      // Tab switching
      document.querySelectorAll(".tab").forEach((tab) => {
        tab.addEventListener("click", () => {
          document
            .querySelectorAll(".tab")
            .forEach((t) => t.classList.remove("active"));
          document
            .querySelectorAll(".tab-content")
            .forEach((c) => c.classList.remove("active"));

          tab.classList.add("active");
          document.getElementById(tab.dataset.tab).classList.add("active");
        });
      });

      // Copy to clipboard function
      function copyToClipboard(text) {
        navigator.clipboard
          .writeText(text)
          .then(() => {
            alert("Copied to clipboard! Paste this in your browser console.");
          })
          .catch((err) => {
            console.error("Failed to copy: ", err);
          });
      }

      // Load and copy full script
      async function loadScript() {
        try {
          const response = await fetch("test-payment-flow.js");
          const script = await response.text();
          document.getElementById("script-content").textContent = script;
          return script;
        } catch (error) {
          document.getElementById("script-content").textContent =
            "Error loading script: " + error.message;
          return null;
        }
      }

      function copyFullScript() {
        const scriptContent =
          document.getElementById("script-content").textContent;
        navigator.clipboard
          .writeText(scriptContent)
          .then(() => {
            alert(
              "Full script copied to clipboard! Paste this in your browser console."
            );
          })
          .catch((err) => {
            console.error("Failed to copy: ", err);
          });
      }

      // Load script on page load
      window.addEventListener("DOMContentLoaded", loadScript);
    </script>
  </body>
</html>
