"use client";

import { useEffect, useState } from "react";
import Link from "next/link";
import Image from "next/image";
import { toast } from "sonner";
import { Edit, Plus, Trash2, ExternalLink } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";
import { PortfolioImage, ImageType } from "@/lib/cms/types";
import { useIsMobile } from "@/hooks/use-mobile";

export default function ImagesPage() {
  const [images, setImages] = useState<PortfolioImage[]>([]);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [selectedType, setSelectedType] = useState<ImageType | "all">("all");
  const isMobile = useIsMobile();

  useEffect(() => {
    fetchImages();
  }, [selectedType]);

  const fetchImages = async () => {
    setIsLoading(true);
    try {
      const url = selectedType === "all"
        ? "/api/cms/images"
        : `/api/cms/images?type=${selectedType}`;

      const res = await fetch(url);
      if (!res.ok) throw new Error("Failed to fetch images");

      const data = await res.json();
      setImages(data);
    } catch (error) {
      console.error("Error fetching images:", error);
      toast.error("Failed to load images");
    } finally {
      setIsLoading(false);
    }
  };

  const handleDeleteImage = async (id: string) => {
    try {
      const res = await fetch(`/api/cms/images/${id}`, {
        method: "DELETE",
      });

      if (!res.ok) throw new Error("Failed to delete image");

      toast.success("Image deleted successfully");
      fetchImages();
    } catch (error) {
      console.error("Error deleting image:", error);
      toast.error("Failed to delete image");
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4">
        <h1 className="text-2xl md:text-3xl font-bold">Image Management</h1>
        <Link href="/admin/images/upload" className="w-full sm:w-auto">
          <Button className="w-full sm:w-auto">
            <Plus className="mr-2 h-4 w-4" />
            Upload New Image
          </Button>
        </Link>
      </div>

      <Card>
        <CardHeader className="p-4 md:p-6">
          <CardTitle className="text-lg md:text-xl">Filter Images</CardTitle>
        </CardHeader>
        <CardContent className="p-4 md:p-6 pt-0">
          <div className="flex items-center">
            <div className="w-full sm:w-[200px]">
              <Select
                value={selectedType}
                onValueChange={(value) => setSelectedType(value as ImageType | "all")}
              >
                <SelectTrigger className="h-10">
                  <SelectValue placeholder="Select type" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Types</SelectItem>
                  <SelectItem value="profile">Profile</SelectItem>
                  <SelectItem value="project">Project</SelectItem>
                  <SelectItem value="background">Background</SelectItem>
                  <SelectItem value="logo">Logo</SelectItem>
                  <SelectItem value="other">Other</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader className="p-4 md:p-6">
          <CardTitle className="text-lg md:text-xl">Images</CardTitle>
        </CardHeader>
        <CardContent className="p-4 md:p-6 pt-0">
          {isLoading ? (
            <div className="flex justify-center py-8">
              <div className="h-8 w-8 animate-spin rounded-full border-b-2 border-t-2 border-blue-500"></div>
            </div>
          ) : images.length === 0 ? (
            <div className="flex flex-col items-center justify-center py-8 text-center">
              <p className="mb-4 text-gray-500">No images found</p>
              <Link href="/admin/images/upload">
                <Button variant="outline">Upload Image</Button>
              </Link>
            </div>
          ) : (
            <div className="grid grid-cols-1 gap-4 sm:gap-6 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4">
              {images.map((image) => (
                <Card key={image.id} className="overflow-hidden h-full">
                  <div className="relative aspect-square">
                    <Image
                      src={image.public_url}
                      alt={image.alt_text}
                      fill
                      className="object-cover"
                    />
                  </div>
                  <CardContent className="p-3 md:p-4">
                    <h3 className="mb-1 font-medium text-sm md:text-base">{image.name}</h3>
                    <p className="mb-2 text-xs text-gray-500 capitalize">{image.type}</p>
                    <div className="flex flex-wrap gap-2">
                      <Link href={`/admin/images/edit/${image.id}`}>
                        <Button variant="outline" size="sm" className="h-9 touch-manipulation">
                          <Edit className="mr-1 h-3 w-3" />
                          Edit
                        </Button>
                      </Link>
                      <AlertDialog>
                        <AlertDialogTrigger asChild>
                          <Button variant="outline" size="sm" className="h-9 touch-manipulation">
                            <Trash2 className="mr-1 h-3 w-3" />
                            Delete
                          </Button>
                        </AlertDialogTrigger>
                        <AlertDialogContent className="max-w-[90vw] sm:max-w-lg">
                          <AlertDialogHeader>
                            <AlertDialogTitle>
                              Are you sure you want to delete this image?
                            </AlertDialogTitle>
                            <AlertDialogDescription>
                              This action cannot be undone. This will permanently delete the
                              image from your portfolio.
                            </AlertDialogDescription>
                          </AlertDialogHeader>
                          <AlertDialogFooter className="flex-col sm:flex-row gap-2 sm:gap-0">
                            <AlertDialogCancel className="mt-2 sm:mt-0">Cancel</AlertDialogCancel>
                            <AlertDialogAction
                              onClick={() => handleDeleteImage(image.id)}
                              className="sm:ml-2"
                            >
                              Delete
                            </AlertDialogAction>
                          </AlertDialogFooter>
                        </AlertDialogContent>
                      </AlertDialog>
                    </div>
                    <a
                      href={image.public_url}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="mt-3 flex items-center text-xs text-blue-500 hover:underline touch-manipulation"
                    >
                      <ExternalLink className="mr-1 h-3 w-3" />
                      View full size
                    </a>
                  </CardContent>
                </Card>
              ))}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
