/**
 * M-Pesa Verification Debug Utility
 * 
 * This script helps diagnose issues with the M-Pesa payment verification process.
 * Copy and paste this entire script into your browser console when on the payment verification page.
 */

// =============================================
// Debug Functions
// =============================================

// Function to check Supabase connection
async function checkSupabaseConnection() {
  console.log('=== CHECKING SUPABASE CONNECTION ===');
  
  try {
    // Get Supabase URL and key from environment variables
    const supabaseUrl = document.querySelector('meta[name="supabase-url"]')?.getAttribute('content');
    const supabaseKey = document.querySelector('meta[name="supabase-key"]')?.getAttribute('content');
    
    if (!supabaseUrl || !supabaseKey) {
      console.error('Supabase URL or key not found in meta tags');
      console.log('Add the following meta tags to your layout.tsx or page.tsx:');
      console.log('<meta name="supabase-url" content={process.env.NEXT_PUBLIC_SUPABASE_URL} />');
      console.log('<meta name="supabase-key" content={process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY} />');
      return false;
    }
    
    console.log(`Supabase URL: ${supabaseUrl}`);
    console.log(`Supabase Key: ${supabaseKey.substring(0, 10)}...`);
    
    // Make a simple request to Supabase to check connection
    const response = await fetch(`${supabaseUrl}/rest/v1/client_projects?select=id&limit=1`, {
      headers: {
        'apikey': supabaseKey,
        'Authorization': `Bearer ${supabaseKey}`
      }
    });
    
    if (response.ok) {
      console.log('✅ Supabase connection successful!');
      const data = await response.json();
      console.log('Response data:', data);
      return true;
    } else {
      console.error('❌ Supabase connection failed!');
      console.error('Status:', response.status, response.statusText);
      const errorText = await response.text();
      console.error('Error:', errorText);
      return false;
    }
  } catch (error) {
    console.error('❌ Error checking Supabase connection:', error);
    return false;
  }
}

// Function to log detailed information about the verification process
function logVerificationDetails() {
  console.log('=== PAYMENT VERIFICATION DEBUG INFO ===');
  
  // Get reference code from the page
  const referenceCodeElement = document.querySelector('.bg-muted p + div .font-mono');
  const referenceCode = referenceCodeElement ? referenceCodeElement.textContent.trim() : 'Not found';
  
  console.log(`Reference Code: ${referenceCode}`);
  
  // Get M-Pesa code input
  const mpesaCodeInput = document.querySelector('input[name="mpesa_code"]');
  const mpesaCode = mpesaCodeInput ? mpesaCodeInput.value : 'Not found';
  
  console.log(`M-Pesa Code: ${mpesaCode}`);
  
  // Check if the form is in submitting state
  const submitButton = document.querySelector('button[type="submit"]');
  const isSubmitting = submitButton && submitButton.textContent.includes('Verifying');
  
  console.log(`Form is submitting: ${isSubmitting}`);
  
  // Check for error messages
  const errorMessages = Array.from(document.querySelectorAll('[role="alert"]')).map(el => el.textContent);
  
  if (errorMessages.length > 0) {
    console.log('Error messages:', errorMessages);
  } else {
    console.log('No visible error messages');
  }
  
  return {
    referenceCode,
    mpesaCode,
    isSubmitting,
    errorMessages
  };
}

// Function to monitor network requests
function monitorNetworkRequests() {
  console.log('=== MONITORING NETWORK REQUESTS ===');
  console.log('Check the Network tab in DevTools for requests to verifyMpesaPayment');
  console.log('Look for POST requests to /api/mpesa/verify or similar endpoints');
  console.log('Instructions:');
  console.log('1. Open the Network tab in DevTools');
  console.log('2. Filter for "fetch" or "xhr" requests');
  console.log('3. Submit the form and observe the network requests');
  console.log('4. Look for any failed requests (red) and check the response');
}

// Function to test server action directly
async function testServerAction(referenceCode, mpesaCode) {
  console.log('=== TESTING SERVER ACTION DIRECTLY ===');
  console.log(`Testing with Reference Code: ${referenceCode}`);
  console.log(`Testing with M-Pesa Code: ${mpesaCode}`);
  
  try {
    // Create a form with the data
    const formData = new FormData();
    formData.append('reference_code', referenceCode);
    formData.append('mpesa_code', mpesaCode);
    
    // Make a direct fetch request to the server action endpoint
    const response = await fetch('/api/mpesa/verify', {
      method: 'POST',
      body: formData,
    });
    
    if (response.ok) {
      const result = await response.json();
      console.log('✅ Server action test successful!');
      console.log('Response:', result);
      return result;
    } else {
      console.error('❌ Server action test failed!');
      console.error('Status:', response.status, response.statusText);
      const errorText = await response.text();
      console.error('Error:', errorText);
      return null;
    }
  } catch (error) {
    console.error('❌ Error testing server action:', error);
    return null;
  }
}

// =============================================
// Test M-Pesa Codes
// =============================================

const testMpesaCodes = [
  'ABC123DEFG',  // Original test code
  'XYZ456MNOP',  // Original test code
  'QWE789RTYU',  // Original test code
  'ZXC012VBNM',  // Original test code
  'MPESA12345',  // Additional test code with MPESA prefix
  'M1P2E3S4A5',  // Additional test code with M-Pesa-like pattern
  '1234567890',  // Numeric only code
  'ABCDEFGHIJ'   // Alphabetic only code
];

// Function to try all test codes
async function tryAllTestCodes() {
  console.log('=== TRYING ALL TEST CODES ===');
  
  const referenceCodeElement = document.querySelector('.bg-muted p + div .font-mono');
  const referenceCode = referenceCodeElement ? referenceCodeElement.textContent.trim() : null;
  
  if (!referenceCode) {
    console.error('Reference code not found on the page');
    return;
  }
  
  console.log(`Using reference code: ${referenceCode}`);
  
  for (const code of testMpesaCodes) {
    console.log(`Trying M-Pesa code: ${code}`);
    
    // Fill the input field
    const codeInput = document.querySelector('input[name="mpesa_code"]');
    if (codeInput) {
      codeInput.value = code;
      codeInput.dispatchEvent(new Event('input', { bubbles: true }));
      
      // Click the submit button
      const submitButton = document.querySelector('button[type="submit"]');
      if (submitButton && !submitButton.disabled) {
        submitButton.click();
        console.log(`Submitted form with code: ${code}`);
        
        // Wait for response
        await new Promise(resolve => setTimeout(resolve, 5000));
        
        // Check if we're still on the verification page
        const stillOnVerificationPage = document.querySelector('input[name="mpesa_code"]') !== null;
        if (!stillOnVerificationPage) {
          console.log(`Success! Code ${code} was accepted.`);
          return true;
        } else {
          console.log(`Code ${code} was rejected or verification is still processing.`);
        }
      } else {
        console.log('Submit button not found or disabled');
      }
    } else {
      console.log('M-Pesa code input not found');
      break;
    }
    
    // Wait before trying the next code
    await new Promise(resolve => setTimeout(resolve, 2000));
  }
  
  console.log('Finished trying all test codes');
  return false;
}

// =============================================
// Usage Instructions
// =============================================

console.log('=== M-PESA VERIFICATION DEBUG UTILITY LOADED ===');
console.log('To debug payment verification issues, run:');
console.log('1. checkSupabaseConnection() - Check if Supabase connection is working');
console.log('2. logVerificationDetails() - Get detailed info about the current verification state');
console.log('3. monitorNetworkRequests() - Instructions for monitoring network requests');
console.log('4. tryAllTestCodes() - Try all test codes sequentially');
console.log('5. testServerAction(referenceCode, mpesaCode) - Test server action directly');
