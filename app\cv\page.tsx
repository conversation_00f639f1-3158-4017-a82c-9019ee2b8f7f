"use client"

import { motion } from "framer-motion"
import { useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Download, Mail, Phone, MapPin, Globe, Github, Linkedin } from "lucide-react"

export default function CVPage() {
  useEffect(() => {
    document.title = "CV - <PERSON> | Computer Science Student & Full-Stack Developer";
  }, []);

  const handleDownloadResume = () => {
    // Create a link element and trigger download
    const link = document.createElement('a');
    link.href = '/Zachary_Odero_Resume.pdf';
    link.download = 'Zachary_Odero_Resume.pdf';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  return (
    <div className="container mx-auto px-4 py-8 max-w-4xl">
      {/* Header Section */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
        className="text-center mb-8"
      >
        <h1 className="text-4xl md:text-5xl font-bold text-black dark:text-white mb-4">
          Zachary Odero
        </h1>
        <p className="text-xl text-gray-600 dark:text-gray-300 mb-6">
          Computer Science Student | Full-Stack Web Developer | AI Enthusiast
        </p>

        {/* Contact Information */}
        <div className="flex flex-wrap justify-center gap-3 md:gap-4 mb-6 text-sm">
          <div className="flex items-center gap-1 min-w-0">
            <Mail className="h-4 w-4 flex-shrink-0" />
            <span className="break-all"><EMAIL></span>
          </div>
          <div className="flex items-center gap-1">
            <Phone className="h-4 w-4 flex-shrink-0" />
            <span>+254 796 564 593</span>
          </div>
          <div className="flex items-center gap-1 min-w-0">
            <MapPin className="h-4 w-4 flex-shrink-0" />
            <span className="text-center sm:text-left">Maseno University, Kisumu, Kenya</span>
          </div>
          <div className="flex items-center gap-1">
            <Globe className="h-4 w-4 flex-shrink-0" />
            <span>zachweb.dev</span>
          </div>
        </div>

        {/* Social Links */}
        <div className="flex justify-center gap-4 mb-6">
          <a
            href="https://www.linkedin.com/in/zacharyodero"
            target="_blank"
            rel="noopener noreferrer"
            className="flex items-center gap-2 text-blue-600 hover:text-blue-700 dark:text-blue-400 dark:hover:text-blue-300"
          >
            <Linkedin className="h-4 w-4" />
            <span>LinkedIn</span>
          </a>
          <a
            href="https://github.com/zacharyodero"
            target="_blank"
            rel="noopener noreferrer"
            className="flex items-center gap-2 text-blue-600 hover:text-blue-700 dark:text-blue-400 dark:hover:text-blue-300"
          >
            <Github className="h-4 w-4" />
            <span>GitHub</span>
          </a>
        </div>

        {/* Download Button */}
        <Button
          onClick={handleDownloadResume}
          className="bg-blue-600 hover:bg-blue-700 dark:bg-blue-500 dark:hover:bg-blue-600"
        >
          <Download className="h-4 w-4 mr-2" />
          Download PDF Resume
        </Button>
      </motion.div>

      {/* Professional Summary */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5, delay: 0.1 }}
        className="mb-8"
      >
        <Card>
          <CardHeader>
            <CardTitle className="text-2xl font-bold text-blue-600 dark:text-blue-400">
              Professional Summary
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-gray-700 dark:text-gray-300 leading-relaxed">
              Ambitious and driven third-year Computer Science student at Maseno University with hands-on
              experience in full-stack web development and a growing expertise in AI technologies. Passionate
              about creating modern web applications using Next.js and Django, with a keen interest in
              leveraging artificial intelligence to solve real-world problems. Active member of Google
              Developer's Club with experience in building scalable web applications and a commitment to
              continuous learning in emerging technologies.
            </p>
          </CardContent>
        </Card>
      </motion.div>

      {/* Education */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5, delay: 0.2 }}
        className="mb-8"
      >
        <Card>
          <CardHeader>
            <CardTitle className="text-2xl font-bold text-blue-600 dark:text-blue-400">
              Education
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div>
                <h3 className="text-xl font-semibold text-black dark:text-white">
                  Bachelor of Science in Computer Science
                </h3>
                <p className="text-gray-600 dark:text-gray-400">
                  Maseno University, Kisumu, Kenya
                </p>
                <p className="text-gray-600 dark:text-gray-400">
                  Expected Graduation: 2027 | Current Year: Third Year
                </p>
                <p className="text-gray-700 dark:text-gray-300 mt-2">
                  <strong>Relevant Coursework:</strong> Data Structures & Algorithms, Software Engineering,
                  Database Systems, Web Development, Object-Oriented Programming, Artificial Intelligence Fundamentals
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      </motion.div>

      {/* Technical Skills */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5, delay: 0.3 }}
        className="mb-8"
      >
        <Card>
          <CardHeader>
            <CardTitle className="text-2xl font-bold text-blue-600 dark:text-blue-400">
              Technical Skills
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div>
                <h4 className="font-semibold text-black dark:text-white mb-2">Programming Languages</h4>
                <div className="flex flex-wrap gap-2">
                  {["Python", "JavaScript", "HTML/CSS", "SQL"].map((skill) => (
                    <Badge key={skill} variant="secondary" className="bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200">
                      {skill}
                    </Badge>
                  ))}
                </div>
              </div>
              <div>
                <h4 className="font-semibold text-black dark:text-white mb-2">Web Development</h4>
                <div className="flex flex-wrap gap-2">
                  {["Next.js", "React", "Django", "Node.js", "Tailwind CSS"].map((skill) => (
                    <Badge key={skill} variant="secondary" className="bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200">
                      {skill}
                    </Badge>
                  ))}
                </div>
              </div>
              <div>
                <h4 className="font-semibold text-black dark:text-white mb-2">Databases</h4>
                <div className="flex flex-wrap gap-2">
                  {["Supabase", "PostgreSQL", "SQLite"].map((skill) => (
                    <Badge key={skill} variant="secondary" className="bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200">
                      {skill}
                    </Badge>
                  ))}
                </div>
              </div>
              <div>
                <h4 className="font-semibold text-black dark:text-white mb-2">Tools & Platforms</h4>
                <div className="flex flex-wrap gap-2">
                  {["Git/GitHub", "VS Code", "Vercel", "Railway"].map((skill) => (
                    <Badge key={skill} variant="secondary" className="bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200">
                      {skill}
                    </Badge>
                  ))}
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </motion.div>

      {/* Projects */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5, delay: 0.4 }}
        className="mb-8"
      >
        <Card>
          <CardHeader>
            <CardTitle className="text-2xl font-bold text-blue-600 dark:text-blue-400">
              Projects
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-6">
              <div>
                <h3 className="text-xl font-semibold text-black dark:text-white">
                  Professional Portfolio Website
                </h3>
                <p className="text-gray-600 dark:text-gray-400 mb-2">
                  Personal Project | zachweb.dev | January 2025 - March 2025
                </p>
                <p className="text-gray-700 dark:text-gray-300 mb-3">
                  Designed and developed a modern, responsive portfolio website showcasing technical skills
                  and projects, demonstrating proficiency in contemporary web development practices.
                </p>
                <div className="mb-3">
                  <h4 className="font-semibold text-black dark:text-white mb-2">Technical Implementation:</h4>
                  <ul className="list-disc list-inside text-gray-700 dark:text-gray-300 space-y-1">
                    <li>Built using Next.js with TypeScript for optimal performance and type safety</li>
                    <li>Implemented responsive design with Tailwind CSS for seamless cross-device experience</li>
                    <li>Integrated modern animations and interactive elements for enhanced user engagement</li>
                    <li>Optimized for SEO and accessibility following web standards best practices</li>
                    <li>Deployed on Vercel with continuous integration and deployment pipeline</li>
                  </ul>
                </div>
                <div>
                  <h4 className="font-semibold text-black dark:text-white mb-2">Key Features:</h4>
                  <ul className="list-disc list-inside text-gray-700 dark:text-gray-300 space-y-1">
                    <li>Dynamic project showcase with detailed case studies</li>
                    <li>Contact form with email integration</li>
                    <li>Dark/light theme toggle for improved user experience</li>
                    <li>Mobile-first responsive design approach</li>
                    <li>Fast loading times with Next.js optimization features</li>
                  </ul>
                </div>
              </div>

              <div>
                <h3 className="text-xl font-semibold text-black dark:text-white">
                  Local Business Job Finder Web Application
                </h3>
                <p className="text-gray-600 dark:text-gray-400 mb-2">
                  Academic Project | January 2025 - March 2025 | In Development
                </p>
                <p className="text-gray-700 dark:text-gray-300 mb-3">
                  Currently developing a comprehensive web application to connect local job seekers with
                  businesses in the community, addressing employment challenges in the local market.
                </p>
                <div className="mb-3">
                  <h4 className="font-semibold text-black dark:text-white mb-2">Technical Stack:</h4>
                  <ul className="list-disc list-inside text-gray-700 dark:text-gray-300 space-y-1">
                    <li>Backend developed using Django with Python for robust server-side functionality</li>
                    <li>Supabase integration for real-time database management and authentication</li>
                    <li>RESTful API design for scalable data operations</li>
                    <li>Responsive frontend design for optimal user experience across devices</li>
                  </ul>
                </div>
                <div>
                  <h4 className="font-semibold text-black dark:text-white mb-2">Planned Features:</h4>
                  <ul className="list-disc list-inside text-gray-700 dark:text-gray-300 space-y-1">
                    <li>Advanced job search and filtering system with location-based recommendations</li>
                    <li>Employer dashboard for job posting and application management</li>
                    <li>User authentication and profile management system</li>
                    <li>Real-time notifications for job matches and application updates</li>
                    <li>Integration with local business directories</li>
                  </ul>
                </div>
                <p className="text-gray-600 dark:text-gray-400 mt-2">
                  <strong>Current Status:</strong> Backend development 80% complete, preparing for deployment phase
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      </motion.div>

      {/* Leadership & Activities */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5, delay: 0.5 }}
        className="mb-8"
      >
        <Card>
          <CardHeader>
            <CardTitle className="text-2xl font-bold text-blue-600 dark:text-blue-400">
              Leadership & Extracurricular Activities
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div>
              <h3 className="text-xl font-semibold text-black dark:text-white">
                Google Developer's Club (GDC) - Active Member
              </h3>
              <p className="text-gray-600 dark:text-gray-400 mb-3">
                Maseno University Chapter | September 2023 - Present
              </p>
              <ul className="list-disc list-inside text-gray-700 dark:text-gray-300 space-y-1 mb-4">
                <li>Participate in weekly coding workshops and technical sessions focusing on modern web technologies</li>
                <li>Collaborate on community tech projects and participate in coding challenges</li>
                <li>Engage in peer learning sessions on emerging technologies including AI and machine learning</li>
                <li>Contribute to knowledge sharing through group discussions and collaborative problem-solving</li>
              </ul>
              <div>
                <h4 className="font-semibold text-black dark:text-white mb-2">Key Contributions:</h4>
                <ul className="list-disc list-inside text-gray-700 dark:text-gray-300 space-y-1">
                  <li>Actively participate in campus coding bootcamps and technical workshops</li>
                  <li>Contribute to solution design sessions for local community tech challenges</li>
                  <li>Share knowledge and insights with fellow members on web development best practices</li>
                </ul>
              </div>
            </div>
          </CardContent>
        </Card>
      </motion.div>

      {/* Certifications & Achievements */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5, delay: 0.6 }}
        className="mb-8"
      >
        <Card>
          <CardHeader>
            <CardTitle className="text-2xl font-bold text-blue-600 dark:text-blue-400">
              Certifications & Achievements
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div>
                <h4 className="font-semibold text-black dark:text-white mb-2">Certifications & Training:</h4>
                <ul className="list-disc list-inside text-gray-700 dark:text-gray-300 space-y-1">
                  <li>Google Developer Student Clubs - Active Member since September 2023</li>
                  <li>Next.js Development - Self-directed learning and practical application</li>
                  <li>Django Web Framework - Academic and personal project implementation</li>
                  <li>Supabase Database Management - Modern database solutions and real-time functionality</li>
                </ul>
              </div>
              <div>
                <h4 className="font-semibold text-black dark:text-white mb-2">Achievements & Recognition:</h4>
                <ul className="list-disc list-inside text-gray-700 dark:text-gray-300 space-y-1">
                  <li>Academic Excellence: Consistent performance in Computer Science coursework with focus on practical application</li>
                  <li>Technical Portfolio: Successfully developed and deployed professional portfolio website</li>
                  <li>Project Development: Currently developing full-stack web application addressing local employment challenges</li>
                  <li>Community Engagement: Active participation in Google Developer's Club technical initiatives</li>
                </ul>
              </div>
            </div>
          </CardContent>
        </Card>
      </motion.div>

      {/* References */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5, delay: 0.7 }}
        className="mb-8"
      >
        <Card>
          <CardHeader>
            <CardTitle className="text-2xl font-bold text-blue-600 dark:text-blue-400">
              References
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div>
              <h3 className="text-lg font-semibold text-black dark:text-white">
                Jack Oraro
              </h3>
              <p className="text-gray-600 dark:text-gray-400">
                Google Developer's Club Leader & Senior Computer Science Student
              </p>
              <p className="text-gray-600 dark:text-gray-400 mb-4">
                Maseno University
              </p>

              {/* Leadership Roles */}
              <div className="mb-4">
                <h4 className="font-semibold text-black dark:text-white mb-2">Leadership Roles</h4>
                <div className="space-y-3">
                  <div>
                    <h5 className="font-medium text-black dark:text-white">Android Team Lead</h5>
                    <p className="text-sm text-gray-600 dark:text-gray-400">Google Developer Students Club (GDSC), KIIT</p>
                    <ul className="text-sm text-gray-700 dark:text-gray-300 mt-1 space-y-1">
                      <li>• Organized and led Android development study jams and hackathons</li>
                      <li>• Mentored and supported students in building Android applications and understanding mobile development frameworks</li>
                    </ul>
                  </div>

                  <div>
                    <h5 className="font-medium text-black dark:text-white">Project Leader – DecisionHub & Podstream</h5>
                    <ul className="text-sm text-gray-700 dark:text-gray-300 mt-1 space-y-1">
                      <li>• Led a team of 4 developers in building DecisionHub, a rule-based decision-making platform for banks</li>
                      <li>• Directed frontend and backend integration, version control systems, and AI-enhanced rule creation</li>
                      <li>• Spearheaded Podstream, a podcast streaming platform, enhancing productivity through effective task delegation and project timelines</li>
                    </ul>
                  </div>

                  <div>
                    <h5 className="font-medium text-black dark:text-white">Founder – JSdynasty Programming School</h5>
                    <ul className="text-sm text-gray-700 dark:text-gray-300 mt-1 space-y-1">
                      <li>• Established a tech learning platform serving both Maseno University students and an online audience</li>
                      <li>• Created and shared educational resources, coding sessions, and real-world project mentorship</li>
                    </ul>
                  </div>
                </div>
              </div>

              {/* Hackathon Leadership & Recognition */}
              <div className="mb-4">
                <h4 className="font-semibold text-black dark:text-white mb-2">Hackathon Leadership & Recognition</h4>
                <ul className="text-sm text-gray-700 dark:text-gray-300 space-y-1">
                  <li>• <strong>1st Place – Corridor Platform Hackathon (2024):</strong> Outperformed 50 teams with innovative project delivery</li>
                  <li>• <strong>1st Place – Flipr Hackathon 17 (2023):</strong> Led a team to win out of 200+ teams, showcasing full-stack and DevOps proficiency</li>
                </ul>
              </div>

              {/* Contact Information */}
              <div className="flex flex-wrap gap-3 md:gap-4 mt-4 text-sm">
                <div className="flex items-center gap-1 min-w-0">
                  <Mail className="h-4 w-4 flex-shrink-0" />
                  <span className="break-all"><EMAIL></span>
                </div>
                <div className="flex items-center gap-1">
                  <Phone className="h-4 w-4 flex-shrink-0" />
                  <span>+254 713 584 267</span>
                </div>
                <div className="flex items-center gap-1 min-w-0">
                  <Github className="h-4 w-4 flex-shrink-0" />
                  <a
                    href="https://github.com/orarojack"
                    target="_blank"
                    rel="noopener noreferrer"
                    className="break-all text-blue-600 hover:text-blue-700 dark:text-blue-400 dark:hover:text-blue-300"
                  >
                    github.com/orarojack
                  </a>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </motion.div>

      {/* Availability Statement */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5, delay: 0.8 }}
        className="mb-8"
      >
        <Card className="bg-blue-50 dark:bg-blue-950 border-blue-200 dark:border-blue-800">
          <CardContent className="pt-6">
            <p className="text-center text-gray-700 dark:text-gray-300 font-medium">
              Available for internships, part-time opportunities, and collaborative projects.
              Particularly interested in roles involving full-stack web development, AI integration,
              and innovative technology solutions.
            </p>
          </CardContent>
        </Card>
      </motion.div>
    </div>
  )
}
