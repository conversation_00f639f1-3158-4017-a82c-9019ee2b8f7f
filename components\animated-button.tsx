'use client';

import { motion } from 'framer-motion';
import { Button } from '@/components/ui/button';
import { ReactNode } from 'react';

interface AnimatedButtonProps {
  children: ReactNode;
  onClick?: () => void;
  className?: string;
}

export default function AnimatedButton({ children, onClick, className }: AnimatedButtonProps) {
  return (
    <motion.div
      className="will-animate"
      initial={{ y: 0 }}
      animate={{
        y: [0, -5, 0],
        transition: {
          duration: 2,
          repeat: Infinity,
          repeatType: 'reverse',
          ease: 'easeInOut'
        }
      }}
      whileHover={{
        scale: 1.05,
        transition: { duration: 0.2 }
      }}
      whileTap={{
        scale: 0.95,
        transition: { duration: 0.1 }
      }}
      style={{ transform: 'translateZ(0)' }} // Force hardware acceleration
    >
      <Button
        className={className}
        onClick={onClick}
      >
        {children}
      </Button>
    </motion.div>
  );
}
